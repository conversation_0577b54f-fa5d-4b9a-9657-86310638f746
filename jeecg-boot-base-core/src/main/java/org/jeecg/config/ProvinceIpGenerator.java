package org.jeecg.config;


import java.util.*;
import java.util.*;
import java.util.concurrent.ThreadLocalRandom;
import java.util.stream.Collectors;

public class ProvinceIpGenerator {


    // 使用 ConcurrentHashMap 保证线程安全，虽然静态初始化块是线程安全的，
    // 但如果未来有动态修改的需求，ConcurrentHashMap更好。
    // 如果确定只在静态块初始化且不再修改，HashMap也可以。
    private static final Map<String, String[]> provinceIpMap = new HashMap<>();
    // 用于快速从省份名称查找代码
    private static final Map<String, Integer> provinceNameToCodeMap = new HashMap<>();
    // 用于快速从代码查找省份名称
    private static final Map<Integer, String> codeToProvinceNameMap = new HashMap<>();
    // 用于快速从城市编码查找省份名称
    private static final Map<String, String> cityCodeToProvinceNameMap = new HashMap<>();

    // 2. 数字与省份的对应关系枚举
    public enum ProvinceCode {
        BEIJING(1, "北京"),
        TIANJIN(2, "天津"),
        HEBEI(3, "河北"),
        SHANXI(4, "山西"),
        INNER_MONGOLIA(5, "内蒙古"),
        LIAONING(6, "辽宁"),
        JILIN(7, "吉林"),
        HEILONGJIANG(8, "黑龙江"),
        SHANGHAI(9, "上海"),
        JIANGSU(10, "江苏"),
        ZHEJIANG(11, "浙江"),
        ANHUI(12, "安徽"),
        FUJIAN(13, "福建"),
        JIANGXI(14, "江西"),
        SHANDONG(15, "山东"),
        HENAN(16, "河南"),
        HUBEI(17, "湖北"),
        HUNAN(18, "湖南"),
        GUANGDONG(19, "广东"),
        GUANGXI(20, "广西"),
        HAINAN(21, "海南"),
        CHONGQING(22, "重庆"),
        SICHUAN(23, "四川"),
        GUIZHOU(24, "贵州"),
        YUNNAN(25, "云南"),
        TIBET(26, "西藏"), // 西藏
        SHAANXI(27, "陕西"), // 陕西
        GANSU(28, "甘肃"),
        QINGHAI(29, "青海"),
        NINGXIA(30, "宁夏"),
        XINJIANG(31, "新疆");
        // 可以继续添加澳门、台湾等

        private final int code;
        private final String provinceName;

        ProvinceCode(int code, String provinceName) {
            this.code = code;
            this.provinceName = provinceName;
        }

        public int getCode() {
            return code;
        }

        public String getProvinceName() {
            return provinceName;
        }

        // 静态方法，通过 code 获取枚举实例
        public static ProvinceCode findByCode(int code) {
            for (ProvinceCode pc : values()) {
                if (pc.code == code) {
                    return pc;
                }
            }
            return null; // 或者抛出异常
        }

        // 静态方法，通过省份名称获取枚举实例 (忽略大小写)
        public static ProvinceCode findByProvinceName(String name) {
            if (name == null || name.trim().isEmpty()) {
                return null;
            }
            for (ProvinceCode pc : values()) {
                // 使用 equalsIgnoreCase 提高用户输入的容错性
                if (pc.provinceName.equalsIgnoreCase(name.trim())) {
                    return pc;
                }
            }
            return null; // 或者抛出异常
        }
    }

    static {
        // 初始化 IP 段映射 Map
        provinceIpMap.put("北京", new String[]{"**********", "**************"});
        provinceIpMap.put("天津", new String[]{"**********", "**************"});
        provinceIpMap.put("河北", new String[]{"**********", "**************"});
        provinceIpMap.put("山西", new String[]{"**********", "**************"});
        provinceIpMap.put("内蒙古", new String[]{"**********", "**************"});
        provinceIpMap.put("辽宁", new String[]{"*********", "*************"});
        provinceIpMap.put("吉林", new String[]{"***********", "***************"});
        provinceIpMap.put("黑龙江", new String[]{"*********", "*************"});
        provinceIpMap.put("上海", new String[]{"**********", "**************"});
        provinceIpMap.put("江苏", new String[]{"**********", "**************"});
        provinceIpMap.put("浙江", new String[]{"*********", "*************"});
        provinceIpMap.put("安徽", new String[]{"**********", "**************"});
        provinceIpMap.put("福建", new String[]{"*********", "*************"});
        provinceIpMap.put("江西", new String[]{"*********", "*************"});
        provinceIpMap.put("山东", new String[]{"**********", "**************"});
        provinceIpMap.put("河南", new String[]{"**********", "**************"});
        provinceIpMap.put("湖北", new String[]{"*********", "*************"});
        provinceIpMap.put("湖南", new String[]{"*********", "*************"});
        provinceIpMap.put("广东", new String[]{"*********", "*************"});
        provinceIpMap.put("广西", new String[]{"*********", "*************"});
        provinceIpMap.put("海南", new String[]{"*********", "*************"});
        provinceIpMap.put("重庆", new String[]{"***********", "***************"});
        provinceIpMap.put("四川", new String[]{"**********", "**************"});
        provinceIpMap.put("贵州", new String[]{"***********", "***************"});
        provinceIpMap.put("云南", new String[]{"***********", "***************"});
        provinceIpMap.put("西藏", new String[]{"***********", "***************"});
        provinceIpMap.put("陕西", new String[]{"***********", "***************"});
        provinceIpMap.put("甘肃", new String[]{"**********", "**************"});
        provinceIpMap.put("青海", new String[]{"**********", "**************"});
        provinceIpMap.put("宁夏", new String[]{"***********", "***************"});
        provinceIpMap.put("新疆", new String[]{"***********", "***************"});

        // 初始化城市编码到省份名称的映射
        cityCodeToProvinceNameMap.put("110000", "北京");
        cityCodeToProvinceNameMap.put("120000", "天津");
        cityCodeToProvinceNameMap.put("130000", "河北");
        cityCodeToProvinceNameMap.put("140000", "山西");
        cityCodeToProvinceNameMap.put("150000", "内蒙古");
        cityCodeToProvinceNameMap.put("210000", "辽宁");
        cityCodeToProvinceNameMap.put("220000", "吉林");
        cityCodeToProvinceNameMap.put("230000", "黑龙江");
        cityCodeToProvinceNameMap.put("310000", "上海");
        cityCodeToProvinceNameMap.put("320000", "江苏");
        cityCodeToProvinceNameMap.put("330000", "浙江");
        cityCodeToProvinceNameMap.put("340000", "安徽");
        cityCodeToProvinceNameMap.put("350000", "福建");
        cityCodeToProvinceNameMap.put("360000", "江西");
        cityCodeToProvinceNameMap.put("370000", "山东");
        cityCodeToProvinceNameMap.put("410000", "河南");
        cityCodeToProvinceNameMap.put("420000", "湖北");
        cityCodeToProvinceNameMap.put("430000", "湖南");
        cityCodeToProvinceNameMap.put("440000", "广东");
        cityCodeToProvinceNameMap.put("450000", "广西");
        cityCodeToProvinceNameMap.put("460000", "海南");
        cityCodeToProvinceNameMap.put("500000", "重庆");
        cityCodeToProvinceNameMap.put("510000", "四川");
        cityCodeToProvinceNameMap.put("520000", "贵州");
        cityCodeToProvinceNameMap.put("530000", "云南");
        cityCodeToProvinceNameMap.put("540000", "西藏");
        cityCodeToProvinceNameMap.put("610000", "陕西");
        cityCodeToProvinceNameMap.put("620000", "甘肃");
        cityCodeToProvinceNameMap.put("630000", "青海");
        cityCodeToProvinceNameMap.put("640000", "宁夏");
        cityCodeToProvinceNameMap.put("650000", "新疆");

        // 确保枚举中的省份都在 provinceIpMap 中有定义，反之亦然
        // 同时填充辅助 Map
        for (ProvinceCode pc : ProvinceCode.values()) {
            String provinceName = pc.getProvinceName();
            int code = pc.getCode();
            if (!provinceIpMap.containsKey(provinceName)) {
                // 或者记录日志，或者根据需要处理
                System.err.println("警告: ProvinceCode 枚举中的省份 '" + provinceName + "' 在 provinceIpMap 中没有对应的 IP 段。");
            }
            provinceNameToCodeMap.put(provinceName.toLowerCase(), code); // 存储小写名称以便不区分大小写查找
            codeToProvinceNameMap.put(code, provinceName);
        }

        // 检查 provinceIpMap 中的 key 是否都在枚举中定义了
        for (String mapKey : provinceIpMap.keySet()) {
            if (ProvinceCode.findByProvinceName(mapKey) == null) {
                System.err.println("警告: provinceIpMap 中的省份 '" + mapKey + "' 没有在 ProvinceCode 枚举中定义。");
            }
        }
    }

    /**
     * 1. 根据数字代码获取对应省份的随机 IP 地址。
     *
     * @param code 省份代码 (来自 ProvinceCode 枚举)
     * @return 对应省份的随机 IP 地址
     * @throws IllegalArgumentException 如果代码无效或找不到对应的省份/IP段
     */
    public static String getIpByCode(int code) {
        ProvinceCode provinceCode = ProvinceCode.findByCode(code);
        if (provinceCode == null) {
            throw new IllegalArgumentException("无效的省份代码：" + code);
        }
        String provinceName = provinceCode.getProvinceName();
        // 复用现有的按省份名称生成IP的逻辑
        return getRandomIpForProvince(provinceName);
    }

    /**
     * 根据城市编码获取对应省份的随机 IP 地址。
     *
     * @param cityCode 城市编码，如 "110000"（北京市）
     * @return 对应省份的随机 IP 地址
     * @throws IllegalArgumentException 如果城市编码无效或找不到对应的省份/IP段
     */
    public static String getIpByCityCode(String cityCode) {
        if (cityCode == null || cityCode.isEmpty()) {
            throw new IllegalArgumentException("城市编码不能为空");
        }

        // 尝试直接匹配完整的城市编码
        String provinceName = cityCodeToProvinceNameMap.get(cityCode);

        // 如果没有找到完整匹配，尝试匹配前两位（省级行政区划代码）
        if (provinceName == null && cityCode.length() >= 2) {
            String provinceCode = cityCode.substring(0, 2) + "0000";
            provinceName = cityCodeToProvinceNameMap.get(provinceCode);
        }

        // 如果仍然没有找到，抛出异常
        if (provinceName == null) {
            throw new IllegalArgumentException("无效的城市编码：" + cityCode);
        }

        // 复用现有的按省份名称生成IP的逻辑
        return getRandomIpForProvince(provinceName);
    }

    /**
     * 3. 根据省份名称获取对应的数字代码。
     *
     * @param provinceName 省份名称 (例如 "北京", "上海")
     * @return 对应的数字代码，如果找不到则返回 -1 (或可以抛出异常)
     */
    public static int getCodeByProvinceName(String provinceName) {
        if (provinceName == null) {
            return -1; // 或抛出异常
        }
        // 使用辅助 map 进行快速查找，忽略大小写
        // 注意：这里假设 provinceNameToCodeMap 的 key 是小写的
        Integer code = provinceNameToCodeMap.get(provinceName.trim().toLowerCase());
        return (code != null) ? code : -1; // 如果找不到返回 -1

        /* // 或者直接使用枚举查找
        ProvinceCode pc = ProvinceCode.findByProvinceName(provinceName);
        return (pc != null) ? pc.getCode() : -1; // 如果找不到返回 -1
        */
    }


    /**
     * 为指定省份生成一个随机 IP 地址。
     *
     * @param province 省份名称
     * @return 该省份IP段内的一个随机IP
     * @throws IllegalArgumentException 如果省份名称无效或未在映射中定义
     */
    public static String getRandomIpForProvince(String province) {
        if (province.equals("未知省份")) {
            return null;
        }
        // 使用 ThreadLocalRandom 获取随机数生成器实例
        return generateIpByProvince(province, ThreadLocalRandom.current());
    }

    /**
     * 返回完整的 11位 的随机手机号
     */
    public static String generateMaskedPhone() {
        ThreadLocalRandom random = ThreadLocalRandom.current();
        String[] prefixes = {
                "131", "132", "133", "134", "135", "136", "137", "138", "139",
                "150", "151", "152", "153", "155", "156", "157", "158", "159",
                "180", "181", "182", "183", "185", "186", "187", "188", "189"
        };

        // 随机选择一个前缀
        String prefix = prefixes[random.nextInt(prefixes.length)];

        // 随机生成后8位数字
        int suffix = random.nextInt(10000000, 100000000); // 确保是8位数
        String formattedSuffix = String.format("%08d", suffix); // 保证是8位数


        return prefix + formattedSuffix;
    }


    /**
     * 从所有已定义的省份中随机选择一个，并生成该省份的随机IP地址。
     *
     * @return 随机省份的随机IP地址
     */
    public static String getRandomIpForProvinceByRandom() {
        ThreadLocalRandom random = ThreadLocalRandom.current();

        // 从 codeToProvinceNameMap 的键（即所有有效的 code）中随机选择一个
        List<Integer> codes = new ArrayList<>(codeToProvinceNameMap.keySet());
        if (codes.isEmpty()) {
            throw new IllegalStateException("没有配置任何省份代码和IP信息");
        }
        int randomCode = codes.get(random.nextInt(codes.size()));

        // 调用 getIpByCode 生成IP
        return getIpByCode(randomCode);

        /* // 原来的方法：直接从 provinceIpMap 随机取 key
        List<String> provinces = new ArrayList<>(provinceIpMap.keySet());
        if (provinces.isEmpty()) {
            throw new IllegalStateException("没有配置任何省份IP信息");
        }
        String province = provinces.get(random.nextInt(provinces.size()));
        return generateIpByProvince(province, random);
        */
    }


    // generateIpByProvince, ipToLong, longToIp 方法保持不变，但现在接收 Random 实例

    /**
     * 根据省份名称和提供的 Random 实例生成随机 IP。
     * (内部辅助方法)
     * @param province 省份名称
     * @param random   随机数生成器实例
     * @return 随机 IP 地址
     * @throws IllegalArgumentException 如果省份无效
     */
    public static String generateIpByProvince(String province, Random random) {
        String[] ipRange = provinceIpMap.get(province);
        if (ipRange == null) {
            // 确保即使内部调用也处理无效省份
            throw new IllegalArgumentException("无效的省份名称或该省份未配置IP段：" + province);
        }

        String startIp = ipRange[0];
        String endIp = ipRange[1];

        try {
            long start = ipToLong(startIp);
            long end = ipToLong(endIp);
            // 确保 end >= start
            if (start > end) {
                throw new IllegalArgumentException("配置错误: 省份 '" + province + "' 的起始IP大于结束IP。");
            }
            // 处理 start == end 的情况
            if (start == end) {
                return startIp;
            }
            // 生成 [start, end] 范围内的随机 long 值
            // random.nextDouble() * (end - start + 1) 会生成 [0, end - start + 1) 的 double
            // 加 start 后范围是 [start, end + 1)
            // 取 long 后变成 [start, end]
            long randomIpLong = start + (long)(random.nextDouble() * (end - start + 1));
            // 防止极小概率下 double 计算导致超出 end，虽然理论上 (end-start+1) 避免了这个问题
            randomIpLong = Math.min(randomIpLong, end);

            return longToIp(randomIpLong);
        } catch (NumberFormatException e) {
            throw new IllegalArgumentException("IP地址格式错误，省份: " + province + ", IP: " + startIp + " 或 " + endIp, e);
        }
    }

    /**
     * 将点分十进制 IP 地址转换为 long 类型整数。
     */
    private static long ipToLong(String ipAddress) {
        String[] ipParts = ipAddress.split("\\.");
        if (ipParts.length != 4) {
            throw new NumberFormatException("无效的IP地址格式: " + ipAddress);
        }
        long result = 0;
        for (int i = 0; i < 4; i++) {
            long part = Long.parseLong(ipParts[i]);
            if (part < 0 || part > 255) {
                throw new NumberFormatException("IP地址部分超出范围 (0-255): " + ipParts[i]);
            }
            result |= part << (24 - (8 * i));
        }
        return result;
    }

    /**
     * 将 long 类型整数转换回点分十进制 IP 地址。
     */
    private static String longToIp(long ip) {
        return ((ip >> 24) & 0xFF) + "." +
                ((ip >> 16) & 0xFF) + "." +
                ((ip >> 8) & 0xFF) + "." +
                (ip & 0xFF);
    }

    // --- 示例用法 ---
    public static void main(String[] args) {
        // 1. 通过代码获取 IP
        int beijingCode = ProvinceCode.BEIJING.getCode(); // 或者直接用 1
        try {
            String beijingIp = getIpByCode(beijingCode);
            System.out.println("通过代码 " + beijingCode + " 获取的北京随机 IP: " + beijingIp);

            String guangdongIp = getIpByCode(19); // 广东的代码是 19
            System.out.println("通过代码 19 获取的广东随机 IP: " + guangdongIp);

            // 测试无效代码
            // getIpByCode(99); // 这会抛出 IllegalArgumentException
        } catch (IllegalArgumentException e) {
            System.err.println("获取IP时出错: " + e.getMessage());
        }

        // 2. 通过城市编码获取 IP
        try {
            String beijingIpByCityCode = getIpByCityCode("110000");
            System.out.println("通过城市编码 110000 获取的北京随机 IP: " + beijingIpByCityCode);

            String shanghaiIpByCityCode = getIpByCityCode("310000");
            System.out.println("通过城市编码 310000 获取的上海随机 IP: " + shanghaiIpByCityCode);

            // 测试二级城市编码（使用前两位匹配省份）
            String hangzhouIpByCityCode = getIpByCityCode("330100");
            System.out.println("通过城市编码 330100 (杭州) 获取的浙江随机 IP: " + hangzhouIpByCityCode);

            // 测试无效城市编码
            // getIpByCityCode("999999"); // 这会抛出 IllegalArgumentException
        } catch (IllegalArgumentException e) {
            System.err.println("通过城市编码获取IP时出错: " + e.getMessage());
        }

        // 3. 通过省份名称获取代码
        String provinceNameToFind = "上海";
        int shanghaiCode = getCodeByProvinceName(provinceNameToFind);
        System.out.println("省份 '" + provinceNameToFind + "' 对应的代码: " + shanghaiCode);

        provinceNameToFind = "hebei"; // 测试不区分大小写
        int hebeiCode = getCodeByProvinceName(provinceNameToFind);
        System.out.println("省份 '" + provinceNameToFind + "' (忽略大小写) 对应的代码: " + hebeiCode);

        provinceNameToFind = "火星"; // 测试无效省份
        int invalidCode = getCodeByProvinceName(provinceNameToFind);
        System.out.println("省份 '" + provinceNameToFind + "' 对应的代码: " + invalidCode); // 输出 -1

        // 其他功能测试
        System.out.println("随机获取一个省份的IP: " + getRandomIpForProvinceByRandom());
        System.out.println("随机获取一个带掩码的手机号: " + generateMaskedPhone());
        System.out.println("直接获取天津的随机IP: " + getRandomIpForProvince("天津"));
    }
}