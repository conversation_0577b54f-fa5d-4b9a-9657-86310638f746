package org.jeecg.config.aliyun;

import com.aliyun.ocr_api20210707.Client;
import com.aliyun.teaopenapi.models.Config;
import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.dysmsapi.model.v20170525.SendSmsRequest;
import com.aliyuncs.profile.DefaultProfile;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

@Setter
@Component
@ConfigurationProperties(prefix = "aliyun")
public class AliyunOcrConfig {
    private String KeyId;
    private String KeySecret;
    private String AccessKeyId;
    private String AccessKeySecret;
    private String endpoint;

    @Bean
    public Client ocrClient(){
        try {

            Client client = new Client(new Config().setAccessKeyId(KeyId).setAccessKeySecret(KeySecret).setEndpoint(endpoint));
            return client;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Bean
    public IAcsClient sendClient(){
        try {
            DefaultProfile profile = DefaultProfile.getProfile("cn-hangzhou",
                    AccessKeyId, // 替换为您的 AccessKey ID
                    AccessKeySecret); // 替换为您的 AccessKey Secret

            IAcsClient client = new DefaultAcsClient(profile);
            return client;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

}
