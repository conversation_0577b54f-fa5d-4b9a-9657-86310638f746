package org.jeecg.config.aliyun;

import com.aliyun.oss.ClientException;
import com.aliyuncs.dysmsapi.model.v20170525.SendSmsRequest;
import com.aliyuncs.dysmsapi.model.v20170525.SendSmsResponse;
import com.aliyuncs.IAcsClient;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
@Setter
@Component
public class SmsUtil {

    @Value("${aliyun.TemplateCode}")
    private String templateCode;

    @Resource
    private IAcsClient client;

    public SendSmsResponse sendSms(String phoneNumber, String templateParam) {
        try {
            SendSmsRequest request = new SendSmsRequest();
            request.setPhoneNumbers(phoneNumber);
            request.setSignName("阿里云短信测试"); // 替换为您的短信签名
            request.setTemplateCode(templateCode);
            request.setTemplateParam(templateParam);

            // 发送请求并获取响应
            SendSmsResponse response = client.getAcsResponse(request);
            return response;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }
}