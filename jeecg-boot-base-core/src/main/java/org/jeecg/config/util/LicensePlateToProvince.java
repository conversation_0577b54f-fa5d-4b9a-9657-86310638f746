package org.jeecg.config.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

import java.util.HashMap;
import java.util.Map;
import java.util.*;

public class LicensePlateToProvince {
    private static final Map<Character, String> provinceMap = new HashMap<>();
    private static final Map<String, List<String>> provinceToCityMap = new HashMap<>();
    private static final Set<String> directCities = new HashSet<>(Arrays.asList("北京", "天津", "上海", "重庆"));

    static {
        // 初始化车牌前缀与省份映射
        provinceMap.put('京', "北京");
        provinceMap.put('津', "天津");
        provinceMap.put('冀', "河北");
        provinceMap.put('晋', "山西");
        provinceMap.put('内', "内蒙古");
        provinceMap.put('辽', "辽宁");
        provinceMap.put('吉', "吉林");
        provinceMap.put('黑', "黑龙江");
        provinceMap.put('沪', "上海");
        provinceMap.put('苏', "江苏");
        provinceMap.put('浙', "浙江");
        provinceMap.put('皖', "安徽");
        provinceMap.put('闽', "福建");
        provinceMap.put('赣', "江西");
        provinceMap.put('鲁', "山东");
        provinceMap.put('豫', "河南");
        provinceMap.put('鄂', "湖北");
        provinceMap.put('湘', "湖南");
        provinceMap.put('粤', "广东");
        provinceMap.put('桂', "广西");
        provinceMap.put('琼', "海南");
        provinceMap.put('渝', "重庆");
        provinceMap.put('川', "四川");
        provinceMap.put('贵', "贵州");
        provinceMap.put('云', "云南");
        provinceMap.put('藏', "西藏");
        provinceMap.put('陕', "陕西");
        provinceMap.put('甘', "甘肃");
        provinceMap.put('青', "青海");
        provinceMap.put('宁', "宁夏");
        provinceMap.put('新', "新疆");
        provinceMap.put('港', "香港");
    }

    static {
        // 初始化省份对应城市和区县
        provinceToCityMap.put("北京", Arrays.asList("北京市东城区", "北京市西城区", "北京市朝阳区", "北京市海淀区"));
        provinceToCityMap.put("天津", Arrays.asList("天津市和平区", "天津市河东区", "天津市南开区", "天津市红桥区"));
        provinceToCityMap.put("河北", Arrays.asList("石家庄市", "唐山市", "秦皇岛市", "保定市", "邯郸市", "邢台市"));
        provinceToCityMap.put("山西", Arrays.asList("太原市", "大同市", "阳泉市", "长治市", "晋城市"));
        provinceToCityMap.put("内蒙古", Arrays.asList("呼和浩特市", "包头市", "赤峰市", "通辽市", "鄂尔多斯市"));
        provinceToCityMap.put("辽宁", Arrays.asList("沈阳市", "大连市", "鞍山市", "抚顺市", "本溪市"));
        provinceToCityMap.put("吉林", Arrays.asList("长春市", "吉林市", "四平市", "辽源市", "通化市"));
        provinceToCityMap.put("黑龙江", Arrays.asList("哈尔滨市", "齐齐哈尔市", "牡丹江市", "佳木斯市", "大庆市"));
        provinceToCityMap.put("上海", Arrays.asList("上海市黄浦区", "上海市徐汇区", "上海市长宁区", "上海市浦东新区"));
        provinceToCityMap.put("江苏", Arrays.asList("南京市", "苏州市", "无锡市", "常州市", "徐州市"));
        provinceToCityMap.put("浙江", Arrays.asList("杭州市", "宁波市", "温州市", "绍兴市", "湖州市"));
        provinceToCityMap.put("福建", Arrays.asList("福州市", "厦门市", "泉州市", "漳州市", "莆田市"));
        provinceToCityMap.put("山东", Arrays.asList("济南市", "青岛市", "烟台市", "潍坊市", "济宁市"));
        provinceToCityMap.put("湖北", Arrays.asList("武汉市", "襄阳市", "宜昌市", "荆州市", "黄石市"));
        provinceToCityMap.put("湖南", Arrays.asList("长沙市", "株洲市", "湘潭市", "衡阳市", "岳阳市"));
        provinceToCityMap.put("广东", Arrays.asList("广州市", "深圳市", "佛山市", "东莞市", "珠海市"));
        provinceToCityMap.put("广西", Arrays.asList("南宁市", "桂林市", "柳州市", "北海市", "梧州市"));
        provinceToCityMap.put("海南", Arrays.asList("海口市", "三亚市", "儋州市", "文昌市", "琼海市"));
        provinceToCityMap.put("重庆", Arrays.asList("重庆市渝中区", "重庆市江北区", "重庆市南岸区", "重庆市渝北区"));
        provinceToCityMap.put("四川", Arrays.asList("成都市", "绵阳市", "德阳市", "南充市", "宜宾市"));
        provinceToCityMap.put("贵州", Arrays.asList("贵阳市", "遵义市", "六盘水市", "安顺市", "毕节市"));
        provinceToCityMap.put("云南", Arrays.asList("昆明市", "曲靖市", "玉溪市", "大理市", "丽江市"));
        provinceToCityMap.put("陕西", Arrays.asList("西安市", "宝鸡市", "咸阳市", "渭南市", "汉中市"));
        provinceToCityMap.put("甘肃", Arrays.asList("兰州市", "天水市", "酒泉市", "嘉峪关市", "张掖市"));
        provinceToCityMap.put("青海", Arrays.asList("西宁市", "海东市", "格尔木市"));
        provinceToCityMap.put("宁夏", Arrays.asList("银川市", "石嘴山市", "吴忠市"));
        provinceToCityMap.put("新疆", Arrays.asList("乌鲁木齐市", "克拉玛依市", "喀什市", "阿克苏市", "伊宁市"));
    }

    /**
     * 根据获取省份车牌
     * 传参为,每日设置中的所属城市 int 类型数组
     * 若为空则随机返回个城市的车牌
     * @return
     */
    public static String returnPlate(List<String> cityList) {
        // 模拟城市编码与车牌前缀映射
        Map<String, String> cityPlateMap = new HashMap<>();
        cityPlateMap.put("福州", "闽A"); // 福州
        cityPlateMap.put("厦门", "闽B"); // 厦门

        if (cityList == null || cityList.isEmpty()) {
            return "未知车牌";
        }

        // 随机选择一个城市编号
        int index = new Random().nextInt(cityList.size());
        String cityCode = cityList.get(index);

        // 获取城市对应车牌前缀，默认闽A
        String prefix = cityPlateMap.getOrDefault(cityCode, "闽A");

        // 随机生成5位数字 + 字母组合
        StringBuilder suffix = new StringBuilder();
        String chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
        Random random = new Random();
        for (int i = 0; i < 5; i++) {
            suffix.append(chars.charAt(random.nextInt(chars.length())));
        }

        return prefix + suffix.toString();
    }
    public static String returnUserName(){
        return "张三";
    }

    public static String getProvinceFromLicensePlate(String licensePlate) {
        if (licensePlate == null || licensePlate.isEmpty()) {
            return "无效车牌";
        }

        char firstChar = licensePlate.charAt(0); // 获取第一个汉字
        return provinceMap.getOrDefault(firstChar, "未知省份"); // 转化为省份
    }

    public static String getRandomCity(String province) {
        //如果为 1 的话,即为省份,返回随机省份下的的一个地区 ,如果为 2 的话即为省份下的市,需要拼接上对应的省份,去provinceToCityMap中的 value 中查找,去掉province的市
        if (province == null || province.isEmpty()) {
            return "未知地区";
        }

        List<String> cities = provinceToCityMap.get(province);
        if (cities == null || cities.isEmpty()) {
            return "未知地区";
        }

        Random random = new Random();
        String city = cities.get(random.nextInt(cities.size()));
        
        // 对直辖市进行特殊处理
        if (directCities.contains(province)) {
            return city; // 直辖市不添加"省"字，直接返回区名
        } else {
            return province + "省" + city; // 普通省份添加"省"字
        }
    }

    public static String getCityByProvinces(String selectedCity) {
        if (selectedCity == null || selectedCity.trim().isEmpty()) {
            return "未知地区";
        }

        // 遍历所有省份和城市列表，模糊匹配
        for (Map.Entry<String, List<String>> entry : provinceToCityMap.entrySet()) {
            for (String candidateCity : entry.getValue()) {
                if (candidateCity.contains(selectedCity.replace("市", "")) || candidateCity.contains(selectedCity)) {
                    // 对直辖市进行特殊处理
                    if (directCities.contains(entry.getKey())) {
                        return selectedCity; // 直辖市不添加"省"字
                    } else {
                        return entry.getKey() + "省" + selectedCity; // 普通省份添加"省"字
                    }
                }
            }
        }

        return "未知地区";
    }

}
