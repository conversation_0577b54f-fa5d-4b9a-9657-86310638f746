package org.jeecg.modules.demo.excel.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.modules.demo.excel.entity.WhIntroduction;
import org.jeecg.modules.demo.excel.service.IWhIntroductionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Api(tags="门户介绍方块")
@RestController
@RequestMapping("/api/excel/whIntroduction")
@Slf4j
public class ApiWhIntroductionController {
    @Autowired
    private IWhIntroductionService whIntroductionService;

    /**
     * 通过id查询
     *
     * @return
     */
    //@AutoLog(value = "门户介绍方块-通过id查询")
    @ApiOperation(value="门户介绍方块-通过id查询", notes="门户介绍方块-通过id查询")
    @GetMapping(value = "/queryOne")
    public Result<WhIntroduction> queryOne() {
        WhIntroduction whIntroduction = whIntroductionService.lambdaQuery()
                .last("ORDER BY RAND() LIMIT 1")
                .one();

        if(whIntroduction==null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(whIntroduction);
    }
}
