package org.jeecg.modules.demo.excel.controller;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.demo.excel.entity.WhExcel;
import org.jeecg.modules.demo.excel.service.IWhExcelService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: 表单生成
 * @Author: jeecg-boot
 * @Date:   2024-08-16
 * @Version: V1.0
 */
@Api(tags="表单生成")
@RestController
@RequestMapping("/excel/whExcel")
@Slf4j
public class WhExcelController extends JeecgController<WhExcel, IWhExcelService> {
	@Autowired
	private IWhExcelService whExcelService;
	
	/**
	 * 分页列表查询
	 *
	 * @param whExcel
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "表单生成-分页列表查询")
	@ApiOperation(value="表单生成-分页列表查询", notes="表单生成-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<WhExcel>> queryPageList(WhExcel whExcel,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<WhExcel> queryWrapper = QueryGenerator.initQueryWrapper(whExcel, req.getParameterMap());
		Page<WhExcel> page = new Page<WhExcel>(pageNo, pageSize);
		IPage<WhExcel> pageList = whExcelService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param whExcel
	 * @return
	 */
	@AutoLog(value = "表单生成-添加")
	@ApiOperation(value="表单生成-添加", notes="表单生成-添加")
	@RequiresPermissions("excel:wh_excel:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody WhExcel whExcel) {
		whExcelService.save(whExcel);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param whExcel
	 * @return
	 */
	@AutoLog(value = "表单生成-编辑")
	@ApiOperation(value="表单生成-编辑", notes="表单生成-编辑")
	@RequiresPermissions("excel:wh_excel:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody WhExcel whExcel) {
		whExcelService.updateById(whExcel);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "表单生成-通过id删除")
	@ApiOperation(value="表单生成-通过id删除", notes="表单生成-通过id删除")
	@RequiresPermissions("excel:wh_excel:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		whExcelService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "表单生成-批量删除")
	@ApiOperation(value="表单生成-批量删除", notes="表单生成-批量删除")
	@RequiresPermissions("excel:wh_excel:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.whExcelService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "表单生成-通过id查询")
	@ApiOperation(value="表单生成-通过id查询", notes="表单生成-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<WhExcel> queryById(@RequestParam(name="id",required=true) String id) {
		WhExcel whExcel = whExcelService.getById(id);
		if(whExcel==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(whExcel);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param whExcel
    */
    @RequiresPermissions("excel:wh_excel:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, WhExcel whExcel) {
        return super.exportXls(request, whExcel, WhExcel.class, "表单生成");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("excel:wh_excel:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, WhExcel.class);
    }

}
