package org.jeecg.modules.demo.excel.controller;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.demo.excel.entity.WhIntroduction;
import org.jeecg.modules.demo.excel.service.IWhIntroductionService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: 门户介绍方块
 * @Author: jeecg-boot
 * @Date:   2024-08-20
 * @Version: V1.0
 */
@Api(tags="门户介绍方块")
@RestController
@RequestMapping("/excel/whIntroduction")
@Slf4j
public class WhIntroductionController extends JeecgController<WhIntroduction, IWhIntroductionService> {
	@Autowired
	private IWhIntroductionService whIntroductionService;
	
	/**
	 * 分页列表查询
	 *
	 * @param whIntroduction
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "门户介绍方块-分页列表查询")
	@ApiOperation(value="门户介绍方块-分页列表查询", notes="门户介绍方块-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<WhIntroduction>> queryPageList(WhIntroduction whIntroduction,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<WhIntroduction> queryWrapper = QueryGenerator.initQueryWrapper(whIntroduction, req.getParameterMap());
		Page<WhIntroduction> page = new Page<WhIntroduction>(pageNo, pageSize);
		IPage<WhIntroduction> pageList = whIntroductionService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param whIntroduction
	 * @return
	 */
	@AutoLog(value = "门户介绍方块-添加")
	@ApiOperation(value="门户介绍方块-添加", notes="门户介绍方块-添加")
	@RequiresPermissions("excel:wh_introduction:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody WhIntroduction whIntroduction) {
		whIntroductionService.save(whIntroduction);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param whIntroduction
	 * @return
	 */
	@AutoLog(value = "门户介绍方块-编辑")
	@ApiOperation(value="门户介绍方块-编辑", notes="门户介绍方块-编辑")
	@RequiresPermissions("excel:wh_introduction:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody WhIntroduction whIntroduction) {
		whIntroductionService.updateById(whIntroduction);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "门户介绍方块-通过id删除")
	@ApiOperation(value="门户介绍方块-通过id删除", notes="门户介绍方块-通过id删除")
	@RequiresPermissions("excel:wh_introduction:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		whIntroductionService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "门户介绍方块-批量删除")
	@ApiOperation(value="门户介绍方块-批量删除", notes="门户介绍方块-批量删除")
	@RequiresPermissions("excel:wh_introduction:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.whIntroductionService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "门户介绍方块-通过id查询")
	@ApiOperation(value="门户介绍方块-通过id查询", notes="门户介绍方块-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<WhIntroduction> queryById(@RequestParam(name="id",required=true) String id) {
		WhIntroduction whIntroduction = whIntroductionService.getById(id);
		if(whIntroduction==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(whIntroduction);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param whIntroduction
    */
    @RequiresPermissions("excel:wh_introduction:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, WhIntroduction whIntroduction) {
        return super.exportXls(request, whIntroduction, WhIntroduction.class, "门户介绍方块");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("excel:wh_introduction:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, WhIntroduction.class);
    }

}
