package org.jeecg.modules.demo.excel.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 门户介绍方块
 * @Author: jeecg-boot
 * @Date:   2024-08-20
 * @Version: V1.0
 */
@Data
@TableName("wh_introduction")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="wh_introduction对象", description="门户介绍方块")
public class WhIntroduction implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private String id;
	/**标签 1*/
	@Excel(name = "标签 1", width = 15)
    @ApiModelProperty(value = "标签 1")
    private String numOne;
    @ApiModelProperty(value = "标签 1标题")
    private String numOneTop;
	/**标签2*/
	@Excel(name = "标签2", width = 15)
    @ApiModelProperty(value = "标签2")
    private String numTwo;
    @ApiModelProperty(value = "标签 3标题")
    private String numTwoTop;
	/**标签 3*/
	@Excel(name = "标签 3", width = 15)
    @ApiModelProperty(value = "标签 3")
    private String numThree;
    @ApiModelProperty(value = "标签 3标题")
    private String numThreeTop;
	/**方块一*/
	@Excel(name = "方块一", width = 15)
    @ApiModelProperty(value = "方块一")
    private String blockOne;
	/**方块二*/
	@Excel(name = "方块二", width = 15)
    @ApiModelProperty(value = "方块二")
    private String blockTwo;
	/**方块三*/
	@Excel(name = "方块三", width = 15)
    @ApiModelProperty(value = "方块三")
    private String blockThree;
	/**方块四*/
	@Excel(name = "方块四", width = 15)
    @ApiModelProperty(value = "方块四")
    private String blockFour;
}
