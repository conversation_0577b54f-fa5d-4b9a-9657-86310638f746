package org.jeecg.modules.demo.firmInformation.controller;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.modules.demo.firmInformation.entity.WhClick;
import org.jeecg.modules.demo.firmInformation.entity.WhFirmInformation;
import org.jeecg.modules.demo.firmInformation.service.IWhClickService;
import org.jeecg.modules.demo.firmInformation.service.IWhFirmInformationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description: 保险公司基本信息
 * @Author: qixin-boot
 * @Date:   2024-08-04
 * @Version: V1.0
 */
@Api(tags="首页保险公司基本信息")
@RestController
@RequestMapping("/api/firmInformation/firmInformation")
@Slf4j
public class ApiFirmInformationController {
    @Autowired
    private IWhFirmInformationService firmInformationService;
    @Autowired
    private IWhClickService whClickService;
    /**
     * 分页列表查询
     *
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @AutoLog(value = "保险公司基本信息-分页列表查询")
    @ApiOperation(value="保险公司基本信息-分页列表查询", notes="保险公司基本信息-分页列表查询")
    @GetMapping(value = "/list")
    @Cacheable(value = "WhFirmInformation", key = "'wh_firmInformation_list'")
    public Result<?> queryHomeList(@RequestParam(name="name", required = false) String name,
                                   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
                                   @RequestParam(name="pageSize", defaultValue="999999") Integer pageSize,
                                   HttpServletRequest req) {
        QueryWrapper<WhFirmInformation> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("is_delete", 0);
        queryWrapper.eq("enable_type", "Y");
        if (StrUtil.isNotBlank(name)) {
            queryWrapper.like("name", name);
        }
        Page<WhFirmInformation> page = new Page<WhFirmInformation>(pageNo, pageSize);
        IPage<WhFirmInformation> pageList = firmInformationService.page(page, queryWrapper);
        pageList.getRecords().forEach(item->{
            Date registrationDate = item.getRegistration();
            if (registrationDate != null) {
                Calendar calendar = Calendar.getInstance();
                calendar.setTime(registrationDate);
                int year = calendar.get(Calendar.YEAR);
                item.setRegistrationYear(year + "年");
            }
            WhClick whClick = whClickService.getById(item.getClicksId());
                    if (whClick!=null){
                        item.setClicksNum(whClick.getWhNum());
                    }
                }
        );
        // 自定义排序
        List<WhFirmInformation> sortedList = pageList.getRecords().stream()
                .sorted((o1, o2) -> {
                    // 1. 置顶的排序：置顶的记录排在前面
                    if ("Y".equals(o1.getTopType()) && !"Y".equals(o2.getTopType())) {
                        return -1; // o1 排在前面
                    } else if (!"Y".equals(o1.getTopType()) && "Y".equals(o2.getTopType())) {
                        return 1; // o2 排在前面
                    }

                    // 2. 根据排序字段排序
                    Integer sort1 = o1.getPageSort();
                    Integer sort2 = o2.getPageSort();
                    if (sort1 == null && sort2 == null) {
                        return 0; // 都为 null 时相等
                    } else if (sort1 == null) {
                        return 1; // sort1 为 null 排在最后
                    } else if (sort2 == null) {
                        return -1; // sort2 为 null 排在最后
                    } else {
                        return Integer.compare(sort1, sort2); // 按照排序字段排序
                    }
                })
                .collect(Collectors.toList());

        // 返回处理后的分页数据
        pageList.setRecords(sortedList);
        return Result.OK(pageList);
    }
}
