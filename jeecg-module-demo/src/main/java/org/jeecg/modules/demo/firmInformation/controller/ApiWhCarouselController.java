package org.jeecg.modules.demo.firmInformation.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.modules.demo.firmInformation.entity.WhCarousel;
import org.jeecg.modules.demo.firmInformation.entity.WhClick;
import org.jeecg.modules.demo.firmInformation.service.IWhCarouselService;
import org.jeecg.modules.demo.firmInformation.service.IWhClickService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
@Api(tags="首页轮播图")
@RestController
@RequestMapping("/api/firmInformation/whCarousel")
@Slf4j
public class ApiWhCarouselController extends JeecgController<WhCarousel, IWhCarouselService> {
    @Autowired
    private IWhCarouselService whCarouselService;
    @Autowired
    private IWhClickService whClickService;

    /**
     * 分页列表查询
     *
     * @param whCarousel
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    //@AutoLog(value = "轮播图-分页列表查询")
    @ApiOperation(value="轮播图-分页列表查询", notes="轮播图-分页列表查询")
    @GetMapping(value = "/list")
    public Result<IPage<WhCarousel>> queryPageList(WhCarousel whCarousel,
                                                   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
                                                   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
                                                   HttpServletRequest req) {
        QueryWrapper<WhCarousel> queryWrapper = QueryGenerator.initQueryWrapper(whCarousel, req.getParameterMap());
        queryWrapper.eq("is_delete", 0);
        queryWrapper.eq("enable_type", "Y");
        Page<WhCarousel> page = new Page<WhCarousel>(pageNo, pageSize);
        IPage<WhCarousel> pageList = whCarouselService.page(page, queryWrapper);
        pageList.getRecords().forEach(item->{
                    WhClick whClick = whClickService.getById(item.getClicksId());
                    if (whClick!=null){
                        item.setClicksNum(whClick.getWhNum());
                    }
                }
        );
        return Result.OK(pageList);
    }
}
