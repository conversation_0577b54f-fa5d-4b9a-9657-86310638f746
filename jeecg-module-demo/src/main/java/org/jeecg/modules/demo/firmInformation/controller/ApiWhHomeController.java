package org.jeecg.modules.demo.firmInformation.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.modules.demo.firmInformation.entity.WhHome;
import org.jeecg.modules.demo.firmInformation.service.IWhHomeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.stream.Stream;

@Api(tags="首页公司简介")
@RestController
@RequestMapping("/api/firmInformation/whHome")
@Slf4j
public class ApiWhHomeController {
    @Autowired
    private IWhHomeService whHomeService;

    @ApiOperation(value="wh_home-分页列表查询", notes="wh_home-分页列表查询")
    @GetMapping(value = "/getOne")
    @Cacheable(value = "whHomeCache", key = "'wh_home_list'")
    public Result<WhHome> queryPageList() {
        List<WhHome> list = whHomeService.list();
        return Result.OK(list.get(0));
    }
}
