package org.jeecg.modules.demo.firmInformation.controller;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.modules.demo.firmInformation.entity.WhClick;
import org.jeecg.modules.demo.firmInformation.entity.WhFirmInformation;
import org.jeecg.modules.demo.firmInformation.entity.WhNews;
import org.jeecg.modules.demo.firmInformation.service.IWhClickService;
import org.jeecg.modules.demo.firmInformation.service.IWhNewsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Api(tags="首页新闻资讯")
@RestController
@RequestMapping("/api/firmInformation/whNews")
@Slf4j
public class ApiWhNewsController {
    @Autowired
    private IWhNewsService whNewsService;
    @Autowired
    private IWhClickService whClickService;

    /**
     * 分页列表查询
     *
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @AutoLog(value = "新闻资讯-分页列表查询")
    @ApiOperation(value="新闻资讯-分页列表查询", notes="新闻资讯-分页列表查询")
    @GetMapping(value = "/list")
    public Result<?> queryPageList(@RequestParam(name="name", required = false) String name,
                                   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
                                   @RequestParam(name="pageSize", defaultValue="1000") Integer pageSize,
                                   HttpServletRequest req) {
        QueryWrapper<WhNews> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("id", "message_time", "news_img", "clicks_id", "name");
        queryWrapper.eq("is_delete", 0);
        queryWrapper.eq("enable_type", "Y");
        if (StrUtil.isNotBlank(name)) {
            queryWrapper.like("name", name);
        }
        Page<WhNews> page = new Page<WhNews>(pageNo, pageSize);
        IPage<WhNews> pageList = whNewsService.page(page, queryWrapper);
        pageList.getRecords().forEach(item -> {
            Date messageTime = item.getMessageTime();
            if (messageTime != null) {
                Calendar calendar = Calendar.getInstance();
                calendar.setTime(messageTime);
                int year = calendar.get(Calendar.YEAR);
                int month = calendar.get(Calendar.MONTH) + 1;  // Calendar.MONTH 从 0 开始，因此需要加 1
                int day = calendar.get(Calendar.DAY_OF_MONTH);
                item.setMessageTiming(year + "/" + month + "/" + day);
            }

            WhClick whClick = whClickService.getById(item.getClicksId());
            if (whClick != null) {
                item.setClicksNum(whClick.getWhNum());
            }
        });

        // 自定义排序
        List<WhNews> sortedList = pageList.getRecords().stream()
                .sorted((o1, o2) -> {
                    // 1. 置顶的排序：置顶的记录排在前面
                    if ("Y".equals(o1.getTopType()) && !"Y".equals(o2.getTopType())) {
                        return -1; // o1 排在前面
                    } else if (!"Y".equals(o1.getTopType()) && "Y".equals(o2.getTopType())) {
                        return 1; // o2 排在前面
                    }

                    // 2. 根据排序字段排序
                    Integer sort1 = o1.getPageSort();
                    Integer sort2 = o2.getPageSort();
                    if (sort1 == null && sort2 == null) {
                        return 0; // 都为 null 时相等
                    } else if (sort1 == null) {
                        return 1; // sort1 为 null 排在最后
                    } else if (sort2 == null) {
                        return -1; // sort2 为 null 排在最后
                    } else {
                        return Integer.compare(sort1, sort2); // 按照排序字段排序
                    }
                })
                .collect(Collectors.toList());

        // 返回处理后的分页数据
        pageList.setRecords(sortedList);
        return Result.OK(pageList);
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    //@AutoLog(value = "wh_news-通过id查询")
    @ApiOperation(value="wh_news-通过id查询", notes="wh_news-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<WhNews> queryById(@RequestParam(name="id",required=true) String id) {
        WhNews whNews = whNewsService.getById(id);
        if(whNews==null) {
            return Result.error("未找到对应数据");
        }
        WhClick whClick = whClickService.getById(whNews.getClicksId());
        Date messageTime = whNews.getMessageTime();
        if (messageTime != null) {
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(messageTime);
            int year = calendar.get(Calendar.YEAR);
            int month = calendar.get(Calendar.MONTH) + 1;  // Calendar.MONTH 从 0 开始，因此需要加 1
            int day = calendar.get(Calendar.DAY_OF_MONTH);
            whNews.setMessageTiming(year + "/" + month + "/" + day);
        }
        whNews.setClicksNum(whClick.getWhNum());
        return Result.OK(whNews);
    }
}
