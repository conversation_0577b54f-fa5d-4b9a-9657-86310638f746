package org.jeecg.modules.demo.firmInformation.controller;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.modules.demo.firmInformation.entity.WhClick;
import org.jeecg.modules.demo.firmInformation.entity.WhFirmInformation;
import org.jeecg.modules.demo.firmInformation.entity.WhProducts;
import org.jeecg.modules.demo.firmInformation.service.IWhClickService;
import org.jeecg.modules.demo.firmInformation.service.IWhProductsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description: 保险产品
 * @Author: qixin-boot
 * @Date:   2024-08-04
 * @Version: V1.0
 */
@Api(tags="首页保险产品")
@RestController
@RequestMapping("/api/firmInformation/whProducts")
@Slf4j
public class ApiWhProductsController {
    @Autowired
    private IWhProductsService whProductsService;
    @Autowired
    private IWhClickService whClickService;
    /**
     * 分页列表查询
     *
     * @param whProducts
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @ApiOperation(value="wh_products-分页列表查询", notes="wh_products-分页列表查询")
    @GetMapping(value = "/list")
    public Result<IPage<WhProducts>> queryPageList(@RequestParam(name="name", required = false) String name,
                                                   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
                                                   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
                                                   HttpServletRequest req) {
        QueryWrapper<WhProducts> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("is_delete", 0);
        queryWrapper.eq("enable_type", "Y");
        if (StrUtil.isNotBlank(name)) {
            queryWrapper.like("name", name);
        }
        Page<WhProducts> page = new Page<WhProducts>(pageNo, pageSize);
        IPage<WhProducts> pageList = whProductsService.page(page, queryWrapper);
        pageList.getRecords().forEach(item->{
                    WhClick whClick = whClickService.getById(item.getClicksId());
                    Date messageTime = item.getMessageTime();
                    if (messageTime != null) {
                        Calendar calendar = Calendar.getInstance();
                        calendar.setTime(messageTime);
                        int year = calendar.get(Calendar.YEAR);
                        int month = calendar.get(Calendar.MONTH) + 1;  // Calendar.MONTH 从 0 开始，因此需要加 1
                        int day = calendar.get(Calendar.DAY_OF_MONTH);
                        item.setMessageTiming(year + "/" + month + "/" + day);
                    }
                    if (whClick!=null){
                        item.setClicksNum(whClick.getWhNum());
                    }
                }
        );
        // 自定义排序
        List<WhProducts> sortedList = pageList.getRecords().stream()
                .sorted((o1, o2) -> {
                    // 1. 置顶的排序：置顶的记录排在前面
                    if ("Y".equals(o1.getTopType()) && !"Y".equals(o2.getTopType())) {
                        return -1; // o1 排在前面
                    } else if (!"Y".equals(o1.getTopType()) && "Y".equals(o2.getTopType())) {
                        return 1; // o2 排在前面
                    }

                    // 2. 根据排序字段排序
                    Integer sort1 = o1.getPageSort();
                    Integer sort2 = o2.getPageSort();
                    if (sort1 == null && sort2 == null) {
                        return 0; // 都为 null 时相等
                    } else if (sort1 == null) {
                        return 1; // sort1 为 null 排在最后
                    } else if (sort2 == null) {
                        return -1; // sort2 为 null 排在最后
                    } else {
                        return Integer.compare(sort1, sort2); // 按照排序字段排序
                    }
                })
                .collect(Collectors.toList());
        pageList.setRecords(sortedList);
        // 返回处理后的分页数据
        return Result.OK(pageList);
    }
}
