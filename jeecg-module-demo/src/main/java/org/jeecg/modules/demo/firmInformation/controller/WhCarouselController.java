package org.jeecg.modules.demo.firmInformation.controller;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.demo.firmInformation.entity.WhCarousel;
import org.jeecg.modules.demo.firmInformation.entity.WhClick;
import org.jeecg.modules.demo.firmInformation.entity.WhFirmInformation;
import org.jeecg.modules.demo.firmInformation.entity.WhNews;
import org.jeecg.modules.demo.firmInformation.service.IWhCarouselService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.modules.demo.firmInformation.service.IWhClickService;
import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: 轮播图
 * @Author: jeecg-boot
 * @Date:   2024-08-06
 * @Version: V1.0
 */
@Api(tags="轮播图")
@RestController
@RequestMapping("/firmInformation/whCarousel")
@Slf4j
public class WhCarouselController extends JeecgController<WhCarousel, IWhCarouselService> {
	@Autowired
	private IWhCarouselService whCarouselService;
	 @Autowired
	 private IWhClickService whClickService;
	
	/**
	 * 分页列表查询
	 *
	 * @param whCarousel
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "轮播图-分页列表查询")
	@ApiOperation(value="轮播图-分页列表查询", notes="轮播图-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<WhCarousel>> queryPageList(WhCarousel whCarousel,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<WhCarousel> queryWrapper = new QueryWrapper<>();
		Map<String, String[]> parameterMap = req.getParameterMap();
		if (ObjectUtil.isNotEmpty(parameterMap.get("name"))) {
			String[] names = parameterMap.get("name");
			queryWrapper.like("name", names[0]);
		}
		queryWrapper.orderByDesc("create_time");
		Page<WhCarousel> page = new Page<WhCarousel>(pageNo, pageSize);
		IPage<WhCarousel> pageList = whCarouselService.page(page, queryWrapper);
		pageList.getRecords().forEach(item->{
					WhClick whClick = whClickService.getById(item.getClicksId());
					if (whClick!=null){
						item.setClicksNum(whClick.getWhNum());
					}
				}
		);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param whCarousel
	 * @return
	 */
	@AutoLog(value = "轮播图-添加")
	@ApiOperation(value="轮播图-添加", notes="轮播图-添加")
	@RequiresPermissions("firmInformation:wh_carousel:add")
	@PostMapping(value = "/add")
	@CacheEvict(value = "WhCarousel", key = "'wh_carousel_list'")
	public Result<String> add(@RequestBody WhCarousel whCarousel) {
		// 置顶修改其他轮播图为非置顶
		if ("Y".equals(whCarousel.getTopType())) {
			QueryWrapper<WhCarousel> queryWrapper = new QueryWrapper<>();
			queryWrapper.eq("is_delete", 0);
			queryWrapper.eq("top_type", "Y");

			// 置顶的其他记录将 `topType` 更新为非置顶
			WhCarousel updateProduct = new WhCarousel();
			updateProduct.setTopType("N");
			whCarouselService.update(updateProduct, queryWrapper);
		}
		String whClickId = whClickService.savaWhClick(whCarousel.getClicksNum());
		whCarousel.setClicksId(whClickId);
		whCarouselService.save(whCarousel);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param whCarousel
	 * @return
	 */
	@AutoLog(value = "轮播图-编辑")
	@ApiOperation(value="轮播图-编辑", notes="轮播图-编辑")
	@RequiresPermissions("firmInformation:wh_carousel:edit")
	@CacheEvict(value = "WhCarousel", key = "'wh_carousel_list'")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody WhCarousel whCarousel) {
		// 置顶修改其他轮播图为非置顶
		if ("Y".equals(whCarousel.getTopType())) {
			QueryWrapper<WhCarousel> queryWrapper = new QueryWrapper<>();
			queryWrapper.eq("is_delete", 0); // 确保只处理有效记录
			queryWrapper.ne("id", whCarousel.getId()); // 排除当前正在更新的记录
			queryWrapper.eq("top_type", "Y"); // 查找其他已经置顶的记录

			// 置顶的其他记录将 `topType` 更新为非置顶
			WhCarousel updateProduct = new WhCarousel();
			updateProduct.setTopType("N");
			whCarouselService.update(updateProduct, queryWrapper);
		}
		whCarouselService.updateById(whCarousel);
		whClickService.updateWhClick(whCarousel.getClicksNum(),whCarousel.getClicksId());
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "轮播图-通过id删除")
	@ApiOperation(value="轮播图-通过id删除", notes="轮播图-通过id删除")
	@RequiresPermissions("firmInformation:wh_carousel:delete")
	@CacheEvict(value = "WhCarousel", key = "'wh_carousel_list'")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		whCarouselService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "轮播图-批量删除")
	@ApiOperation(value="轮播图-批量删除", notes="轮播图-批量删除")
	@RequiresPermissions("firmInformation:wh_carousel:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	@CacheEvict(value = "WhCarousel", key = "'wh_carousel_list'")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.whCarouselService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "轮播图-通过id查询")
	@ApiOperation(value="轮播图-通过id查询", notes="轮播图-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<WhCarousel> queryById(@RequestParam(name="id",required=true) String id) {
		WhCarousel whCarousel = whCarouselService.getById(id);
		if(whCarousel==null) {
			return Result.error("未找到对应数据");
		}
		WhClick whClick = whClickService.getById(whCarousel.getClicksId());
		whCarousel.setClicksNum(whClick.getWhNum());
		return Result.OK(whCarousel);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param whCarousel
    */
    @RequiresPermissions("firmInformation:wh_carousel:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, WhCarousel whCarousel) {
		// Step.1 组装查询条件
		QueryWrapper<WhCarousel> queryWrapper = QueryGenerator.initQueryWrapper(whCarousel, request.getParameterMap());
		LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();

		// 过滤选中数据
		String selections = request.getParameter("selections");
		if (oConvertUtils.isNotEmpty(selections)) {
			List<String> selectionList = Arrays.asList(selections.split(","));
			queryWrapper.in("id",selectionList);
		}
		// Step.2 获取导出数据
		List<WhCarousel> exportList = service.list(queryWrapper);

		// Step.3 AutoPoi 导出Excel
		ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
		//此处设置的filename无效 ,前端会重更新设置一下
		mv.addObject(NormalExcelConstants.FILE_NAME, "轮播图");
		mv.addObject(NormalExcelConstants.CLASS, WhCarousel.class);
		//update-begin--Author:liusq  Date:20210126 for：图片导出报错，ImageBasePath未设置--------------------
		ExportParams exportParams=new ExportParams("轮播图" + "报表", "导出人:" + sysUser.getRealname(), "轮播图");
		//update-end--Author:liusq  Date:20210126 for：图片导出报错，ImageBasePath未设置----------------------
		mv.addObject(NormalExcelConstants.PARAMS,exportParams);
		mv.addObject(NormalExcelConstants.DATA_LIST, exportList);
		return mv;
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("firmInformation:wh_carousel:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
		//TODO 导入代码
		MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
		Map<String, MultipartFile> fileMap = multipartRequest.getFileMap();
		for (Map.Entry<String, MultipartFile> entity : fileMap.entrySet()) {
			// 获取上传文件对象
			MultipartFile file = entity.getValue();
			ImportParams params = new ImportParams();
			params.setTitleRows(2);
			params.setHeadRows(1);
			params.setNeedSave(true);
			try {
				List<WhCarousel> list = ExcelImportUtil.importExcel(file.getInputStream(), WhCarousel.class, params);
				//update-begin-author:taoyan date:20190528 for:批量插入数据
				long start = System.currentTimeMillis();
				List<WhCarousel> savaList = new ArrayList<WhCarousel>();
				List<WhCarousel> updateList = new ArrayList<WhCarousel>();

				list.forEach(item -> {
							if (StrUtil.isNotBlank(item.getId())) {
								WhCarousel whCarousel = whCarouselService.getById(item.getId());
								//id不为空,执行修改操作
								WhClick whClick = whClickService.getById(whCarousel.getClicksId());
								whClick.setWhNum(item.getClicksNum());
								whClickService.updateById(whClick);
								updateList.add(item);
							}else {
								if (ObjectUtil.isEmpty(item.getClicksNum())) {
									item.setClicksNum(0);
								}
								//执行新增操作
								String whClickId = whClickService.savaWhClick(item.getClicksNum());
								item.setClicksId(whClickId);
								savaList.add(item);
							}
						}
				);
				if (CollectionUtil.isNotEmpty(savaList)) {
					whCarouselService.saveBatch(list);
				}
				if (CollectionUtil.isNotEmpty(updateList)) {
					whCarouselService.updateBatchById(updateList);
				}

				//400条 saveBatch消耗时间1592毫秒  循环插入消耗时间1947毫秒
				//1200条  saveBatch消耗时间3687毫秒 循环插入消耗时间5212毫秒
				//log.info("消耗时间" + (System.currentTimeMillis() - start) + "毫秒");
				//update-end-author:taoyan date:20190528 for:批量插入数据
				return Result.ok("文件导入成功！数据行数：" + list.size());
			} catch (Exception e) {
				//update-begin-author:taoyan date:20211124 for: 导入数据重复增加提示
				String msg = e.getMessage();
				log.error(msg, e);
				if(msg!=null && msg.indexOf("Duplicate entry")>=0){
					return Result.error("文件导入失败:有重复数据！");
				}else{
					return Result.error("文件导入失败:" + e.getMessage());
				}
				//update-end-author:taoyan date:20211124 for: 导入数据重复增加提示
			} finally {
				try {
					file.getInputStream().close();
				} catch (IOException e) {
					e.printStackTrace();
				}
			}
		}
		return Result.error("文件导入失败！");
    }

}
