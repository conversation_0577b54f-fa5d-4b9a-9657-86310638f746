package org.jeecg.modules.demo.firmInformation.controller;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.demo.firmInformation.entity.WhFirmAnother;
import org.jeecg.modules.demo.firmInformation.service.IWhFirmAnotherService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: wh_firm_another
 * @Author: jeecg-boot
 * @Date:   2024-08-06
 * @Version: V1.0
 */
@Api(tags="wh_firm_another")
@RestController
@RequestMapping("/firmInformation/whFirmAnother")
@Slf4j
public class WhFirmAnotherController extends JeecgController<WhFirmAnother, IWhFirmAnotherService> {
	@Autowired
	private IWhFirmAnotherService whFirmAnotherService;
	
	/**
	 * 分页列表查询
	 *
	 * @param whFirmAnother
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "wh_firm_another-分页列表查询")
	@ApiOperation(value="wh_firm_another-分页列表查询", notes="wh_firm_another-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<WhFirmAnother>> queryPageList(WhFirmAnother whFirmAnother,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<WhFirmAnother> queryWrapper = QueryGenerator.initQueryWrapper(whFirmAnother, req.getParameterMap());
		Page<WhFirmAnother> page = new Page<WhFirmAnother>(pageNo, pageSize);
		IPage<WhFirmAnother> pageList = whFirmAnotherService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param whFirmAnother
	 * @return
	 */
	@AutoLog(value = "wh_firm_another-添加")
	@ApiOperation(value="wh_firm_another-添加", notes="wh_firm_another-添加")
	@RequiresPermissions("firmInformation:wh_firm_another:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody WhFirmAnother whFirmAnother) {
		whFirmAnotherService.save(whFirmAnother);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param whFirmAnother
	 * @return
	 */
	@AutoLog(value = "wh_firm_another-编辑")
	@ApiOperation(value="wh_firm_another-编辑", notes="wh_firm_another-编辑")
	@RequiresPermissions("firmInformation:wh_firm_another:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody WhFirmAnother whFirmAnother) {
		whFirmAnotherService.updateById(whFirmAnother);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "wh_firm_another-通过id删除")
	@ApiOperation(value="wh_firm_another-通过id删除", notes="wh_firm_another-通过id删除")
	@RequiresPermissions("firmInformation:wh_firm_another:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		whFirmAnotherService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "wh_firm_another-批量删除")
	@ApiOperation(value="wh_firm_another-批量删除", notes="wh_firm_another-批量删除")
	@RequiresPermissions("firmInformation:wh_firm_another:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.whFirmAnotherService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "wh_firm_another-通过id查询")
	@ApiOperation(value="wh_firm_another-通过id查询", notes="wh_firm_another-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<WhFirmAnother> queryById(@RequestParam(name="id",required=true) String id) {
		WhFirmAnother whFirmAnother = whFirmAnotherService.getById(id);
		if(whFirmAnother==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(whFirmAnother);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param whFirmAnother
    */
    @RequiresPermissions("firmInformation:wh_firm_another:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, WhFirmAnother whFirmAnother) {
        return super.exportXls(request, whFirmAnother, WhFirmAnother.class, "wh_firm_another");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("firmInformation:wh_firm_another:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, WhFirmAnother.class);
    }

}
