package org.jeecg.modules.demo.firmInformation.controller;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.intern.InternUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.demo.firmInformation.dto.WhFirmClickDto;
import org.jeecg.modules.demo.firmInformation.dto.WhFirmInformationDto;
import org.jeecg.modules.demo.firmInformation.entity.WhClick;
import org.jeecg.modules.demo.firmInformation.entity.WhFirmAnother;
import org.jeecg.modules.demo.firmInformation.entity.WhFirmInformation;
import org.jeecg.modules.demo.firmInformation.entity.WhNews;
import org.jeecg.modules.demo.firmInformation.service.IWhClickService;
import org.jeecg.modules.demo.firmInformation.service.IWhFirmInformationService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.modules.demo.firmInformation.vo.WhBoardFirmClickVo;
import org.jeecg.modules.demo.firmInformation.vo.WhFirmClickVo;
import org.jeecg.modules.demo.firmInformation.vo.WhFirmInformationVo;
import org.jeecg.modules.demo.firmInformation.vo.WhPotalClickIpVo;
import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: 合作公司
 * @Author: jeecg-boot
 * @Date:   2024-08-06
 * @Version: V1.0
 */
@Api(tags="合作公司")
@RestController
@RequestMapping("/firmInformation/whFirmInformation")
@Slf4j
public class WhFirmInformationController extends JeecgController<WhFirmInformation, IWhFirmInformationService> {
	@Autowired
	private IWhFirmInformationService whFirmInformationService;
	 @Autowired
	 private IWhClickService whClickService;

	/**
	 * 分页列表查询
	 *
	 * @param whFirmInformation
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "合作公司-分页列表查询")
	@ApiOperation(value="合作公司-分页列表查询", notes="合作公司-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<WhFirmInformation>> queryPageList(WhFirmInformation whFirmInformation,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {

		QueryWrapper<WhFirmInformation> queryWrapper = new QueryWrapper<>();
		Map<String, String[]> parameterMap = req.getParameterMap();
		if (ObjectUtil.isNotEmpty(parameterMap.get("name"))) {
			String[] names = parameterMap.get("name");
			queryWrapper.like("name", names[0]);
		}
		queryWrapper.orderByDesc("create_time");

		Page<WhFirmInformation> page = new Page<WhFirmInformation>(pageNo, pageSize);
		IPage<WhFirmInformation> pageList = whFirmInformationService.page(page, queryWrapper);
		pageList.getRecords().forEach(item->{
					WhClick whClick = whClickService.getById(item.getClicksId());
					if (whClick!=null){
						item.setClicksNum(whClick.getWhNum());
					}
				}
		);
		return Result.OK(pageList);
	}

	 @ApiOperation(value="合作公司-合计数统计", notes="合作公司-合计数统计")
	 @GetMapping(value = "/getAllList")
	 public Result<List<WhFirmInformationVo>> getAllList(@RequestParam(required = false) String name) {
		 LambdaQueryWrapper<WhFirmInformation> queryWrapper = new LambdaQueryWrapper<>();
		 queryWrapper.select(WhFirmInformation::getId, WhFirmInformation::getName)
				 .eq(WhFirmInformation::getIsDelete, "N");
		 if (StrUtil.isNotBlank(name)) {
			 queryWrapper.like(WhFirmInformation::getName, name);
		 }

		 List<WhFirmInformation> pageList = whFirmInformationService.list(queryWrapper);
		 if (CollectionUtil.isEmpty(pageList)) {
			 return Result.OK();
		 }
		 List<WhFirmInformationVo> recode = new ArrayList<>();
		 pageList.forEach(item->{
			 WhFirmInformationVo informationVo = new WhFirmInformationVo();
			 informationVo.setId(item.getId());
			 informationVo.setName(item.getName());
			 recode.add(informationVo);
		 });
		 return Result.OK(recode);
	 }

	 @ApiOperation(value="合作公司-合计数统计", notes="合作公司-合计数统计")
	 @GetMapping(value = "/count")
	 public Result<IPage<WhFirmInformationVo>> count(WhFirmInformationDto dto,
													 @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
													 @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
													 @RequestParam(name="noSort", required=false) Boolean noSort,
													 HttpServletRequest req,
													 HttpServletResponse response) {
		 // 设置缓存控制头，允许浏览器缓存结果10分钟
		 response.setHeader("Cache-Control", "max-age=600, public");
		 // 设置ETag和Last-Modified头，支持条件请求
		 String etag = String.format("W/\"%d-%d-%s-%s\"",
				pageNo,
				pageSize,
				dto.getName() == null ? "" : dto.getName().hashCode(),
				dto.getStartTime() == null ? "" : dto.getStartTime().getTime());
		 response.setHeader("ETag", etag);

		 // 检查客户端缓存是否有效
		 String ifNoneMatch = req.getHeader("If-None-Match");
		 if (ifNoneMatch != null && ifNoneMatch.equals(etag)) {
			 response.setStatus(HttpServletResponse.SC_NOT_MODIFIED);
			 return null;
		 }

		 if (noSort != null) {
			 dto.setNoSort(noSort);
		 }

		 long startTime = System.currentTimeMillis();
		 Page<WhFirmInformationVo> page = new Page<WhFirmInformationVo>(pageNo, pageSize);
		 IPage<WhFirmInformationVo> pageList = whFirmInformationService.getCount(page, dto);
		 long endTime = System.currentTimeMillis();
		 log.info("公司点击排行查询耗时: {} ms", (endTime - startTime));

		 return Result.OK(pageList);
	 }


	 @ApiOperation(value="合作公司-柱状数据环比", notes="合作公司-柱状数据环比")
	 @GetMapping(value = "/getCount")
	 public Result<WhBoardFirmClickVo> getCount(WhFirmClickDto dto) {
		 List<WhFirmClickVo> pageList = whFirmInformationService.getCountOus(dto);
		 WhBoardFirmClickVo vo = new WhBoardFirmClickVo();
		 vo.setClickList(pageList);
		 //取出pageList中的clickSum，进行合计，赋值到以下
		 vo.setClickCollect(pageList.stream().mapToInt(WhFirmClickVo::getClickSum).sum());
		 return Result.OK(vo);
	 }

	 @ApiOperation(value="合作公司- ip曲线图", notes="合作公司-ip曲线图")
	 @GetMapping(value = "/getIpCount")
	 public Result<WhPotalClickIpVo> getIpCount(WhFirmClickDto dto) {
		 WhPotalClickIpVo pageList = whFirmInformationService.getIpCount(dto);
		 return Result.OK(pageList);
	 }

	/**
	 *   添加
	 *
	 * @param whFirmInformation
	 * @return
	 */
	@AutoLog(value = "合作公司-添加")
	@ApiOperation(value="合作公司-添加", notes="合作公司-添加")
	@RequiresPermissions("firmInformation:wh_firm_information:add")
	@PostMapping(value = "/add")
	@CacheEvict(value = "WhFirmInformation", key = "'wh_firmInformation_list'")
	public Result<String> add(@RequestBody WhFirmInformation whFirmInformation) {
		// 置顶修改其他保险产品为非置顶
		if ("Y".equals(whFirmInformation.getTopType())) {
			QueryWrapper<WhFirmInformation> queryWrapper = new QueryWrapper<>();
			queryWrapper.eq("is_delete", 0);
			queryWrapper.eq("top_type", "Y");

			// 置顶的其他记录将 `topType` 更新为非置顶
			WhFirmInformation updateProduct = new WhFirmInformation();
			updateProduct.setTopType("N");
			whFirmInformationService.update(updateProduct, queryWrapper);
		}
		String whClick = whClickService.savaWhClick(whFirmInformation.getClicksNum());
		whFirmInformation.setClicksId(whClick);
		whFirmInformationService.save(whFirmInformation);

		return Result.OK("添加成功！");
	}

	/**
	 *  编辑
	 *
	 * @param whFirmInformation
	 * @return
	 */
	@AutoLog(value = "合作公司-编辑")
	@ApiOperation(value="合作公司-编辑", notes="合作公司-编辑")
	@RequiresPermissions("firmInformation:wh_firm_information:edit")
	@CacheEvict(value = "WhFirmInformation", key = "'wh_firmInformation_list'")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody WhFirmInformation whFirmInformation) {
		// 置顶修改其他保险产品为非置顶
		if ("Y".equals(whFirmInformation.getTopType())) {
			QueryWrapper<WhFirmInformation> queryWrapper = new QueryWrapper<>();
			queryWrapper.eq("is_delete", 0); // 确保只处理有效记录
			queryWrapper.ne("id", whFirmInformation.getId()); // 排除当前正在更新的记录
			queryWrapper.eq("top_type", "Y"); // 查找其他已经置顶的记录

			// 置顶的其他记录将 `topType` 更新为非置顶
			WhFirmInformation updateProduct = new WhFirmInformation();
			updateProduct.setTopType("N");
			whFirmInformationService.update(updateProduct, queryWrapper);
		}
		whFirmInformationService.updateById(whFirmInformation);
		whClickService.updateWhClick(whFirmInformation.getClicksNum(),whFirmInformation.getClicksId());
		return Result.OK("编辑成功!");
	}

	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "合作公司-通过id删除")
	@ApiOperation(value="合作公司-通过id删除", notes="合作公司-通过id删除")
	@RequiresPermissions("firmInformation:wh_firm_information:delete")
	@DeleteMapping(value = "/delete")
	@CacheEvict(value = "WhFirmInformation", key = "'wh_firmInformation_list'")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		whFirmInformationService.removeById(id);
		return Result.OK("删除成功!");
	}

	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "合作公司-批量删除")
	@ApiOperation(value="合作公司-批量删除", notes="合作公司-批量删除")
	@RequiresPermissions("firmInformation:wh_firm_information:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	@CacheEvict(value = "WhFirmInformation", key = "'wh_firmInformation_list'")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.whFirmInformationService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}

	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "合作公司-通过id查询")
	@ApiOperation(value="合作公司-通过id查询", notes="合作公司-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<WhFirmInformation> queryById(@RequestParam(name="id",required=true) String id) {
		WhFirmInformation whFirmInformation = whFirmInformationService.getById(id);
		if(whFirmInformation==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(whFirmInformation);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param whFirmInformation
    */
    @RequiresPermissions("firmInformation:wh_firm_information:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, WhFirmInformation whFirmInformation) {
        return super.exportXls(request, whFirmInformation, WhFirmInformation.class, "合作公司");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("firmInformation:wh_firm_information:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
		MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
		Map<String, MultipartFile> fileMap = multipartRequest.getFileMap();
		for (Map.Entry<String, MultipartFile> entity : fileMap.entrySet()) {
			// 获取上传文件对象
			MultipartFile file = entity.getValue();
			ImportParams params = new ImportParams();
			params.setTitleRows(2);
			params.setHeadRows(1);
			params.setNeedSave(true);
			try {
				List<WhFirmInformation> list = ExcelImportUtil.importExcel(file.getInputStream(), WhFirmInformation.class, params);
				//update-begin-author:taoyan date:20190528 for:批量插入数据
				long start = System.currentTimeMillis();
				List<WhFirmInformation> savaList = new ArrayList<WhFirmInformation>();
				List<WhFirmInformation> updateList = new ArrayList<WhFirmInformation>();

				list.forEach(item -> {
						if (StrUtil.isNotBlank(item.getId())) {
							WhFirmInformation whFirmInformation = whFirmInformationService.getById(item.getId());
							//id不为空,执行修改操作
							WhClick whClick = whClickService.getById(whFirmInformation.getClicksId());
							whClick.setWhNum(item.getClicksNum());
							whClickService.updateById(whClick);
							updateList.add(item);
						}else {
							if (ObjectUtil.isEmpty(item.getClicksNum())) {
								item.setClicksNum(0);
							}
							//执行新增操作
							String whClickId = whClickService.savaWhClick(item.getClicksNum());
							item.setClicksId(whClickId);
							savaList.add(item);
						}
					}
				);
				if (CollectionUtil.isNotEmpty(savaList)) {
					whFirmInformationService.saveBatch(list);
				}
				if (CollectionUtil.isNotEmpty(updateList)) {
					whFirmInformationService.updateBatchById(updateList);
				}

				//400条 saveBatch消耗时间1592毫秒  循环插入消耗时间1947毫秒
				//1200条  saveBatch消耗时间3687毫秒 循环插入消耗时间5212毫秒
				//log.info("消耗时间" + (System.currentTimeMillis() - start) + "毫秒");
				//update-end-author:taoyan date:20190528 for:批量插入数据
				return Result.ok("文件导入成功！数据行数：" + list.size());
			} catch (Exception e) {
				//update-begin-author:taoyan date:20211124 for: 导入数据重复增加提示
				String msg = e.getMessage();
				log.error(msg, e);
				if(msg!=null && msg.indexOf("Duplicate entry")>=0){
					return Result.error("文件导入失败:有重复数据！");
				}else{
					return Result.error("文件导入失败:" + e.getMessage());
				}
				//update-end-author:taoyan date:20211124 for: 导入数据重复增加提示
			} finally {
				try {
					file.getInputStream().close();
				} catch (IOException e) {
					e.printStackTrace();
				}
			}
		}
		return Result.error("文件导入失败！");
    }

}
