package org.jeecg.modules.demo.firmInformation.controller;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.demo.firmInformation.entity.WhHome;
import org.jeecg.modules.demo.firmInformation.service.IWhHomeService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: wh_home
 * @Author: jeecg-boot
 * @Date:   2024-08-06
 * @Version: V1.0
 */
@Api(tags="wh_home")
@RestController
@RequestMapping("/firmInformation/whHome")
@Slf4j
public class WhHomeController extends JeecgController<WhHome, IWhHomeService> {
	@Autowired
	private IWhHomeService whHomeService;
	
	/**
	 * 分页列表查询
	 *
	 * @param whHome
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "wh_home-分页列表查询")
	@ApiOperation(value="wh_home-分页列表查询", notes="wh_home-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<WhHome>> queryPageList(WhHome whHome,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<WhHome> queryWrapper = QueryGenerator.initQueryWrapper(whHome, req.getParameterMap());
		Page<WhHome> page = new Page<WhHome>(pageNo, pageSize);
		IPage<WhHome> pageList = whHomeService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param whHome
	 * @return
	 */
	@AutoLog(value = "wh_home-添加")
	@ApiOperation(value="wh_home-添加", notes="wh_home-添加")
	@RequiresPermissions("firmInformation:wh_home:add")
	@PostMapping(value = "/add")
	@CacheEvict(value = "whHomeCache", key = "'wh_home_list'")
	public Result<String> add(@RequestBody WhHome whHome) {
		whHomeService.save(whHome);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param whHome
	 * @return
	 */
	@AutoLog(value = "wh_home-编辑")
	@ApiOperation(value="wh_home-编辑", notes="wh_home-编辑")
	@RequiresPermissions("firmInformation:wh_home:edit")
	@CacheEvict(value = "whHomeCache", key = "'wh_home_list'")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody WhHome whHome) {
		whHomeService.updateById(whHome);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "wh_home-通过id删除")
	@ApiOperation(value="wh_home-通过id删除", notes="wh_home-通过id删除")
	@RequiresPermissions("firmInformation:wh_home:delete")
	@CacheEvict(value = "whHomeCache", key = "'wh_home_list'")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		whHomeService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "wh_home-批量删除")
	@ApiOperation(value="wh_home-批量删除", notes="wh_home-批量删除")
	@RequiresPermissions("firmInformation:wh_home:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.whHomeService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "wh_home-通过id查询")
	@ApiOperation(value="wh_home-通过id查询", notes="wh_home-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<WhHome> queryById(@RequestParam(name="id",required=true) String id) {
		WhHome whHome = whHomeService.getById(id);
		if(whHome==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(whHome);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param whHome
    */
    @RequiresPermissions("firmInformation:wh_home:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, WhHome whHome) {
        return super.exportXls(request, whHome, WhHome.class, "wh_home");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("firmInformation:wh_home:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, WhHome.class);
    }

}
