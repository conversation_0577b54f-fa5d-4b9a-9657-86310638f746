package org.jeecg.modules.demo.firmInformation.controller;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.demo.firmInformation.entity.WhCarousel;
import org.jeecg.modules.demo.firmInformation.entity.WhClick;
import org.jeecg.modules.demo.firmInformation.entity.WhNews;
import org.jeecg.modules.demo.firmInformation.entity.WhProducts;
import org.jeecg.modules.demo.firmInformation.service.IWhClickService;
import org.jeecg.modules.demo.firmInformation.service.IWhProductsService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: wh_products
 * @Author: jeecg-boot
 * @Date:   2024-08-06
 * @Version: V1.0
 */
@Api(tags="wh_products")
@RestController
@RequestMapping("/firmInformation/whProducts")
@Slf4j
public class WhProductsController extends JeecgController<WhProducts, IWhProductsService> {
	@Autowired
	private IWhProductsService whProductsService;
	 @Autowired
	 private IWhClickService whClickService;
	
	/**
	 * 分页列表查询
	 *
	 * @param whProducts
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "wh_products-分页列表查询")
	@ApiOperation(value="wh_products-分页列表查询", notes="wh_products-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<WhProducts>> queryPageList(WhProducts whProducts,
												   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
												   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
												   HttpServletRequest req) {
		QueryWrapper<WhProducts> queryWrapper = new QueryWrapper<>();
		Map<String, String[]> parameterMap = req.getParameterMap();
		if (ObjectUtil.isNotEmpty(parameterMap.get("name"))) {
			String[] names = parameterMap.get("name");
			queryWrapper.like("name", names[0]);
		}
		Page<WhProducts> page = new Page<WhProducts>(pageNo, pageSize);
		IPage<WhProducts> pageList = whProductsService.page(page, queryWrapper);
		pageList.getRecords().forEach(item->{
					WhClick whClick = whClickService.getById(item.getClicksId());
					if (whClick!=null){
						item.setClicksNum(whClick.getWhNum());
					}
				}
		);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param whProducts
	 * @return
	 */
	@AutoLog(value = "wh_products-添加")
	@ApiOperation(value="wh_products-添加", notes="wh_products-添加")
	@RequiresPermissions("firmInformation:wh_products:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody WhProducts whProducts) {
		// 置顶修改其他保险产品为非置顶
		if ("Y".equals(whProducts.getTopType())) {
			QueryWrapper<WhProducts> queryWrapper = new QueryWrapper<>();
			queryWrapper.eq("is_delete", 0);
			queryWrapper.eq("top_type", "Y");

			// 置顶的其他记录将 `topType` 更新为非置顶
			WhProducts updateProduct = new WhProducts();
			updateProduct.setTopType("N");
			whProductsService.update(updateProduct, queryWrapper);
		}
		String whClick = whClickService.savaWhClick(whProducts.getClicksNum());
		whProducts.setClicksId(whClick);
		whProductsService.save(whProducts);

		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param whProducts
	 * @return
	 */
	@AutoLog(value = "wh_products-编辑")
	@ApiOperation(value="wh_products-编辑", notes="wh_products-编辑")
	@RequiresPermissions("firmInformation:wh_products:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody WhProducts whProducts) {
		// 置顶修改其他保险产品为非置顶
		if ("Y".equals(whProducts.getTopType())) {
			QueryWrapper<WhProducts> queryWrapper = new QueryWrapper<>();
			queryWrapper.eq("is_delete", 0); // 确保只处理有效记录
			queryWrapper.ne("id", whProducts.getId()); // 排除当前正在更新的记录
			queryWrapper.eq("top_type", "Y"); // 查找其他已经置顶的记录

			// 置顶的其他记录将 `topType` 更新为非置顶
			WhProducts updateProduct = new WhProducts();
			updateProduct.setTopType("N");
			whProductsService.update(updateProduct, queryWrapper);
		}
		whProductsService.updateById(whProducts);
		whClickService.updateWhClick(whProducts.getClicksNum(),whProducts.getClicksId());
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "wh_products-通过id删除")
	@ApiOperation(value="wh_products-通过id删除", notes="wh_products-通过id删除")
	@RequiresPermissions("firmInformation:wh_products:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		whProductsService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "wh_products-批量删除")
	@ApiOperation(value="wh_products-批量删除", notes="wh_products-批量删除")
	@RequiresPermissions("firmInformation:wh_products:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.whProductsService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "wh_products-通过id查询")
	@ApiOperation(value="wh_products-通过id查询", notes="wh_products-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<WhProducts> queryById(@RequestParam(name="id",required=true) String id) {
		WhProducts whProducts = whProductsService.getById(id);
		if(whProducts==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(whProducts);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param whProducts
    */
    @RequiresPermissions("firmInformation:wh_products:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, WhProducts whProducts) {
        return super.exportXls(request, whProducts, WhProducts.class, "wh_products");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("firmInformation:wh_products:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
		MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
		Map<String, MultipartFile> fileMap = multipartRequest.getFileMap();
		for (Map.Entry<String, MultipartFile> entity : fileMap.entrySet()) {
			// 获取上传文件对象
			MultipartFile file = entity.getValue();
			ImportParams params = new ImportParams();
			params.setTitleRows(2);
			params.setHeadRows(1);
			params.setNeedSave(true);
			try {
				List<WhProducts> list = ExcelImportUtil.importExcel(file.getInputStream(), WhProducts.class, params);
				//update-begin-author:taoyan date:20190528 for:批量插入数据
				long start = System.currentTimeMillis();
				List<WhProducts> savaList = new ArrayList<WhProducts>();
				List<WhProducts> updateList = new ArrayList<WhProducts>();

				list.forEach(item -> {
							if (StrUtil.isNotBlank(item.getId())) {
								WhProducts whCarousel = whProductsService.getById(item.getId());
								//id不为空,执行修改操作
								WhClick whClick = whClickService.getById(whCarousel.getClicksId());
								whClick.setWhNum(item.getClicksNum());
								whClickService.updateById(whClick);
								updateList.add(item);
							}else {
								if (ObjectUtil.isEmpty(item.getClicksNum())) {
									item.setClicksNum(0);
								}
								//执行新增操作
								String whClickId = whClickService.savaWhClick(item.getClicksNum());
								item.setClicksId(whClickId);
								savaList.add(item);
							}
						}
				);
				if (CollectionUtil.isNotEmpty(savaList)) {
					whProductsService.saveBatch(list);
				}
				if (CollectionUtil.isNotEmpty(updateList)) {
					whProductsService.updateBatchById(updateList);
				}

				//400条 saveBatch消耗时间1592毫秒  循环插入消耗时间1947毫秒
				//1200条  saveBatch消耗时间3687毫秒 循环插入消耗时间5212毫秒
				//log.info("消耗时间" + (System.currentTimeMillis() - start) + "毫秒");
				//update-end-author:taoyan date:20190528 for:批量插入数据
				return Result.ok("文件导入成功！数据行数：" + list.size());
			} catch (Exception e) {
				//update-begin-author:taoyan date:20211124 for: 导入数据重复增加提示
				String msg = e.getMessage();
				log.error(msg, e);
				if(msg!=null && msg.indexOf("Duplicate entry")>=0){
					return Result.error("文件导入失败:有重复数据！");
				}else{
					return Result.error("文件导入失败:" + e.getMessage());
				}
				//update-end-author:taoyan date:20211124 for: 导入数据重复增加提示
			} finally {
				try {
					file.getInputStream().close();
				} catch (IOException e) {
					e.printStackTrace();
				}
			}
		}
		return Result.error("文件导入失败！");
    }

}
