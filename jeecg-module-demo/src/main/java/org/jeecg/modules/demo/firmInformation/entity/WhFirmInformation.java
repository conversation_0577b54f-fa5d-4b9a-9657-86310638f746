package org.jeecg.modules.demo.firmInformation.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 合作公司
 * @Author: jeecg-boot
 * @Date:   2024-08-06
 * @Version: V1.0
 */
@Data
@TableName("wh_firm_information")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="wh_firm_information对象", description="合作公司")
public class WhFirmInformation implements Serializable {
    private static final long serialVersionUID = 1L;

	/**id*/
    @Excel(name = "id", width = 15)
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "id")
    private String id;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private Date updateTime;
	/**所属部门*/
    @ApiModelProperty(value = "所属部门")
    private String sysOrgCode;
	/**公司名称*/
	@Excel(name = "公司名称", width = 15)
    @ApiModelProperty(value = "公司名称")
    private String name;
	/**图片*/
	@Excel(name = "图片", width = 15)
    @ApiModelProperty(value = "图片")
    private String img;
	/**电话*/
	@Excel(name = "电话", width = 15)
    @ApiModelProperty(value = "电话")
    private String phone;
	/**类型*/
	@Excel(name = "类型", width = 15)
    @ApiModelProperty(value = "类型")
    private String firmTypeId;
	/**点击数子表*/
    @ApiModelProperty(value = "点击数子表")
    private String clicksId;
    @TableField(exist = false)
    @Excel(name = "点击数", width = 15)
    private transient Integer clicksNum;
	/**网页排序*/
	@Excel(name = "网页排序", width = 15)
    @ApiModelProperty(value = "网页排序")
    private Integer pageSort;
	/**其他信息*/
	@Excel(name = "其他信息", width = 15)
    @ApiModelProperty(value = "其他信息")
    private String additionalId;
	/**官网地址*/
	@Excel(name = "官网地址", width = 15)
    @ApiModelProperty(value = "官网地址")
    private String firmUrl;

    @Excel(name = "注册时间", width = 20, format = "yyyy-MM-dd")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "注册时间")
    private Date registration;
    private transient String registrationYear;
	/**展示*/
	@Excel(name = "展示(Y是;N否)", width = 15)
    @ApiModelProperty(value = "展示")
    private String enableType;
	/**删除*/
    @ApiModelProperty(value = "删除")
    private String isDelete;
	/**置顶*/
    @ApiModelProperty(value = "置顶")
    private String topType;
}
