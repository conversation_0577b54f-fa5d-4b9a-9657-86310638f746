package org.jeecg.modules.demo.firmInformation.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: wh_news
 * @Author: jeecg-boot
 * @Date:   2024-08-06
 * @Version: V1.0
 */
@Data
@TableName("wh_news")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="wh_news对象", description="wh_news")
public class WhNews implements Serializable {
    private static final long serialVersionUID = 1L;

	/**id*/
    @Excel(name = "id", width = 15)
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "id")
    private String id;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private Date updateTime;
    /**更新日期*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "资讯日期")
    private Date messageTime;
    private transient String messageTiming;
	/**封面图*/
	@Excel(name = "封面图", width = 15)
    @ApiModelProperty(value = "封面图")
    private String newsImg;
	/**简介*/
	@Excel(name = "简介", width = 15)
    @ApiModelProperty(value = "简介")
    private String introduce;
	/**点击数主键*/

    @ApiModelProperty(value = "点击数主键")
    private String clicksId;
    @TableField(exist = false)
    @Excel(name = "点击数", width = 15)
    private transient Integer clicksNum;
	/**网页排序*/
	@Excel(name = "网页排序", width = 15)
    @ApiModelProperty(value = "网页排序")
    private Integer pageSort;
	/**标题*/
	@Excel(name = "标题", width = 15)
    @ApiModelProperty(value = "标题")
    private String name;
	/**删除（0否，1是）*/
    @ApiModelProperty(value = "删除（0否，1是）")
    private Integer isDelete;
	/**置顶*/
    @ApiModelProperty(value = "置顶")
    private String topType;
	/**展示*/
	@Excel(name = "展示(Y是;N否)", width = 15)
    @ApiModelProperty(value = "展示")
    private String enableType;

    @ApiModelProperty(value = "详细介绍")
    private String detailed;
}
