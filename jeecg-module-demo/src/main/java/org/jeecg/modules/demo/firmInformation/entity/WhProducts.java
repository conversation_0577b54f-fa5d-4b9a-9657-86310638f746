package org.jeecg.modules.demo.firmInformation.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: wh_products
 * @Author: jeecg-boot
 * @Date:   2024-08-06
 * @Version: V1.0
 */
@Data
@TableName("wh_products")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="wh_products对象", description="wh_products")
public class WhProducts implements Serializable {
    private static final long serialVersionUID = 1L;

	/**id*/
    @Excel(name = "id", width = 15)
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "id")
    private String id;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private Date updateTime;
	/**标题*/
	@Excel(name = "标题", width = 15)
    @ApiModelProperty(value = "标题")
    private String name;
	/**产品图*/
	@Excel(name = "产品图", width = 15)
    @ApiModelProperty(value = "产品图")
    private String productsImg;
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "资讯日期")
    private Date messageTime;
    private transient String messageTiming;

	/**简介*/
	@Excel(name = "简介", width = 15)
    @ApiModelProperty(value = "简介")
    private String introduction;
	/**点击数主键*/
    @ApiModelProperty(value = "点击数主键")
    private String clicksId;
    @Excel(name = "点击数", width = 15)
    private transient Integer clicksNum;
	/**排序*/
	@Excel(name = "排序", width = 15)
    @ApiModelProperty(value = "排序")
    private Integer pageSort;
	/**产品官网*/
	@Excel(name = "产品官网", width = 15)
    @ApiModelProperty(value = "产品官网")
    private String productsUrl;
	/**详细介绍*/
	@Excel(name = "详细介绍", width = 15)
    @ApiModelProperty(value = "详细介绍")
    private String detailed;
	/**删除（0否，1是）*/
    @ApiModelProperty(value = "删除（0否，1是）")
    private Integer isDelete;
	/**展示*/
	@Excel(name = "展示(Y是;N否)", width = 15)
    @ApiModelProperty(value = "展示")
    private String enableType;
	/**置顶*/
    @ApiModelProperty(value = "置顶")
    private String topType;
}
