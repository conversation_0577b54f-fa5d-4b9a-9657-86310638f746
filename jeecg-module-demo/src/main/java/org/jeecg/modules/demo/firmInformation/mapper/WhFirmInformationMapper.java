package org.jeecg.modules.demo.firmInformation.mapper;

import java.util.List;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.demo.firmInformation.dto.WhFirmClickDto;
import org.jeecg.modules.demo.firmInformation.dto.WhFirmInformationDto;
import org.jeecg.modules.demo.firmInformation.entity.WhFirmInformation;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.jeecg.modules.demo.firmInformation.vo.WhFirmClickIpVo;
import org.jeecg.modules.demo.firmInformation.vo.WhFirmClickVo;
import org.jeecg.modules.demo.firmInformation.vo.WhFirmInformationVo;

/**
 * @Description: 合作公司
 * @Author: jeecg-boot
 * @Date:   2024-08-06
 * @Version: V1.0
 */
public interface WhFirmInformationMapper extends BaseMapper<WhFirmInformation> {

    List<WhFirmInformationVo> getCount(Page<WhFirmInformationVo> page, @Param("dto") WhFirmInformationDto dto);

    List<WhFirmClickVo> getCountOus(@Param("dto") WhFirmClickDto dto);

    List<WhFirmClickIpVo> getIpCount(@Param("dto") WhFirmClickDto dto);

    List<WhFirmClickVo> getCountOusBymonth(@Param("dto") WhFirmClickDto dto);
}
