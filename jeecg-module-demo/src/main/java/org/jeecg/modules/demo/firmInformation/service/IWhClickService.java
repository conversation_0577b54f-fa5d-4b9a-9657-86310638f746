package org.jeecg.modules.demo.firmInformation.service;

import org.jeecg.modules.demo.firmInformation.entity.WhClick;
import com.baomidou.mybatisplus.extension.service.IService;

import java.math.BigDecimal;

/**
 * @Description: 点击数
 * @Author: jeecg-boot
 * @Date:   2024-08-06
 * @Version: V1.0
 */
public interface IWhClickService extends IService<WhClick> {

    /**
     * 新增点击数，返回主键
     * @param num
     * @return
     */
    String savaWhClick(Integer num);


    /**
     * 点击数+1
     * @param id
     * @return
     */
    void addNum(String id);


    /**
     * 修改点击数
     *
     * @param clicksNum
     * @param clicksId
     */
    void updateWhClick(Integer clicksNum, String clicksId);
}
