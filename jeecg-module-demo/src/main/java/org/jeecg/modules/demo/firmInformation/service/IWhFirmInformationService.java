package org.jeecg.modules.demo.firmInformation.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.jeecg.modules.demo.firmInformation.dto.WhFirmClickDto;
import org.jeecg.modules.demo.firmInformation.dto.WhFirmInformationDto;
import org.jeecg.modules.demo.firmInformation.entity.WhFirmInformation;
import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.demo.firmInformation.vo.WhFirmClickVo;
import org.jeecg.modules.demo.firmInformation.vo.WhFirmInformationVo;
import org.jeecg.modules.demo.firmInformation.vo.WhPotalClickIpVo;

import java.util.List;

/**
 * @Description: 合作公司
 * @Author: jeecg-boot
 * @Date:   2024-08-06
 * @Version: V1.0
 */
public interface IWhFirmInformationService extends IService<WhFirmInformation> {

    IPage<WhFirmInformationVo> getCount(Page<WhFirmInformationVo> page, WhFirmInformationDto dto);

    List<WhFirmClickVo> getCountOus(WhFirmClickDto dto);

    WhPotalClickIpVo getIpCount(WhFirmClickDto dto);
}
