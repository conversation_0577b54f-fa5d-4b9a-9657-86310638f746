package org.jeecg.modules.demo.firmInformation.service.impl;

import org.jeecg.modules.demo.firmInformation.entity.WhClick;
import org.jeecg.modules.demo.firmInformation.mapper.WhClickMapper;
import org.jeecg.modules.demo.firmInformation.service.IWhClickService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.math.BigDecimal;

/**
 * @Description: 点击数
 * @Author: jeecg-boot
 * @Date:   2024-08-06
 * @Version: V1.0
 */
@Service
public class WhClickServiceImpl extends ServiceImpl<WhClickMapper, WhClick> implements IWhClickService {

    @Override
    public String savaWhClick(Integer num) {
        WhClick whClick = new WhClick();
        whClick.setWhNum(num);
        this.baseMapper.insert(whClick);
        return whClick.getId();
    }

    @Override
    public void addNum(String id) {
        WhClick whClick = this.getById(id);
        whClick.setWhNum(whClick.getWhNum()+1);
        this.baseMapper.updateById(whClick);
    }

    @Override
    public void updateWhClick(Integer clicksNum, String clicksId) {
        WhClick whClick = this.getById(clicksId);
        whClick.setWhNum(clicksNum);
        this.baseMapper.updateById(whClick);
    }
}
