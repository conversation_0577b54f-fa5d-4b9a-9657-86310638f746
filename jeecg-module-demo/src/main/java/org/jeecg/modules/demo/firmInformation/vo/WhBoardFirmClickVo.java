package org.jeecg.modules.demo.firmInformation.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
public class WhBoardFirmClickVo implements Serializable {

    @ApiModelProperty(value = "公司详情柱状List")
    private List<WhFirmClickVo> clickList;

    @ApiModelProperty(value = "公司合计点击数")
    private Integer clickCollect;
}
