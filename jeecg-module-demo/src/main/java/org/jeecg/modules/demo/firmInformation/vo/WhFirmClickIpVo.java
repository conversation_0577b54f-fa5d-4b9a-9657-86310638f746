package org.jeecg.modules.demo.firmInformation.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
public class WhFirmClickIpVo {
    @ApiModelProperty(value = "点击ip汇总数")
    private  Integer clickSum;

    @ApiModelProperty(value = "访问量")
    private  Integer ipSum;

    @ApiModelProperty(value = "环比比例(与前日百分比)")
    private  double ratio;

    @ApiModelProperty(value = "日期")
    private String meent;
}
