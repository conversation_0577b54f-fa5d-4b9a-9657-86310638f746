package org.jeecg.modules.demo.firmInformation.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;

import java.io.Serializable;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
public class WhFirmClickVo implements Serializable {



    @ApiModelProperty(value = "点击合计数")
    private  Integer clickSum;
    @ApiModelProperty(value = "环比率")
    private  double clickRate;

    @ApiModelProperty(value = "日，月，年")
    private String meent;
}
