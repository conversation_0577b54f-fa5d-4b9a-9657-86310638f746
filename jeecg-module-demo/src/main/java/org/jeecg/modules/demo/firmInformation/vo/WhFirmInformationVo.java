package org.jeecg.modules.demo.firmInformation.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;

import java.io.Serializable;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="wh_firm_information对象", description="合作公司")
public class WhFirmInformationVo  implements Serializable {

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "排名顺序")
    private Integer index;

    @ApiModelProperty(value = "公司名称")
    private String name;

    @ApiModelProperty(value = "点击合计数")
    private  Integer clicksSum;


}
