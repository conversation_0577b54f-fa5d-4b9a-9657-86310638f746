package org.jeecg.modules.demo.firmInformation.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
public class WhPotalClickIpVo implements Serializable {

    @ApiModelProperty(value = "曲线图列表数据")
    private List<WhFirmClickIpVo> whPotalClickIpVos;

    @ApiModelProperty(value = "总访问量")
    private String countIp;

    @ApiModelProperty(value = "今日访问量")
    private String dayIp;

    @ApiModelProperty(value = " 今天ip")
    private String dayClick;
}
