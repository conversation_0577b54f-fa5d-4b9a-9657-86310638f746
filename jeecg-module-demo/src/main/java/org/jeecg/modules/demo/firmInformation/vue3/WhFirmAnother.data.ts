import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
   {
    title: '省份',
    align:"center",
    dataIndex: 'province'
   },
   {
    title: '城市',
    align:"center",
    dataIndex: 'city'
   },
   {
    title: '区县',
    align:"center",
    dataIndex: 'district'
   },
   {
    title: '法人',
    align:"center",
    dataIndex: 'capital'
   },
   {
    title: '注册资金',
    align:"center",
    dataIndex: 'person'
   },
   {
    title: '统一社会信用代码',
    align:"center",
    dataIndex: 'creditCode'
   },
   {
    title: '注册地址',
    align:"center",
    dataIndex: 'address'
   },
   {
    title: '企业邮箱',
    align:"center",
    dataIndex: 'businessEmail'
   },
   {
    title: '经营范围',
    align:"center",
    dataIndex: 'business'
   },
   {
    title: '简介',
    align:"center",
    dataIndex: 'introduction'
   },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '省份',
    field: 'province',
    component: 'Input',
  },
  {
    label: '城市',
    field: 'city',
    component: 'Input',
  },
  {
    label: '区县',
    field: 'district',
    component: 'Input',
  },
  {
    label: '法人',
    field: 'capital',
    component: 'Input',
  },
  {
    label: '注册资金',
    field: 'person',
    component: 'Input',
  },
  {
    label: '统一社会信用代码',
    field: 'creditCode',
    component: 'Input',
  },
  {
    label: '注册地址',
    field: 'address',
    component: 'Input',
  },
  {
    label: '企业邮箱',
    field: 'businessEmail',
    component: 'Input',
  },
  {
    label: '经营范围',
    field: 'business',
    component: 'Input',
  },
  {
    label: '简介',
    field: 'introduction',
    component: 'InputTextArea',
  },
	// TODO 主键隐藏字段，目前写死为ID
	{
	  label: '',
	  field: 'id',
	  component: 'Input',
	  show: false
	},
];

// 高级查询数据
export const superQuerySchema = {
  province: {title: '省份',order: 0,view: 'text', type: 'string',},
  city: {title: '城市',order: 1,view: 'text', type: 'string',},
  district: {title: '区县',order: 2,view: 'text', type: 'string',},
  capital: {title: '法人',order: 3,view: 'text', type: 'string',},
  person: {title: '注册资金',order: 4,view: 'text', type: 'string',},
  creditCode: {title: '统一社会信用代码',order: 5,view: 'text', type: 'string',},
  address: {title: '注册地址',order: 6,view: 'text', type: 'string',},
  businessEmail: {title: '企业邮箱',order: 7,view: 'text', type: 'string',},
  business: {title: '经营范围',order: 8,view: 'text', type: 'string',},
  introduction: {title: '简介',order: 9,view: 'textarea', type: 'string',},
};

/**
* 流程表单调用这个方法获取formSchema
* @param param
*/
export function getBpmFormSchema(_formData): FormSchema[]{
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}