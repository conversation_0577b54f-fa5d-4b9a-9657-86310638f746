import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
   {
    title: '公司名称',
    align:"center",
    dataIndex: 'name'
   },
   {
    title: '图片',
    align:"center",
    dataIndex: 'img',
    customRender:render.renderImage,
   },
   {
    title: '电话',
    align:"center",
    dataIndex: 'phone'
   },
   {
    title: '类型',
    align:"center",
    dataIndex: 'firmTypeId'
   },
   {
    title: '网页排序',
    align:"center",
    dataIndex: 'pageSort'
   },
   {
    title: '其他信息',
    align:"center",
    dataIndex: 'additionalId'
   },
   {
    title: '官网地址',
    align:"center",
    dataIndex: 'firmUrl'
   },
   {
    title: '注册时间',
    align:"center",
    dataIndex: 'registration'
   },
   {
    title: '展示',
    align:"center",
    dataIndex: 'enableType',
    customRender:({text}) => {
       return  render.renderSwitch(text, [{text:'是',value:'Y'},{text:'否',value:'N'}])
     },
   },
   {
    title: '置顶',
    align:"center",
    dataIndex: 'topType',
    customRender:({text}) => {
       return  render.renderSwitch(text, [{text:'是',value:'Y'},{text:'否',value:'N'}])
     },
   },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '公司名称',
    field: 'name',
    component: 'Input',
  },
  {
    label: '图片',
    field: 'img',
     component: 'JImageUpload',
     componentProps:{
        fileMax: 0
      },
  },
  {
    label: '电话',
    field: 'phone',
    component: 'Input',
  },
  {
    label: '类型',
    field: 'firmTypeId',
    component: 'Input',
  },
  {
    label: '网页排序',
    field: 'pageSort',
    component: 'Input',
  },
  {
    label: '其他信息',
    field: 'additionalId',
    component: 'InputTextArea',
  },
  {
    label: '官网地址',
    field: 'firmUrl',
    component: 'Input',
  },
  {
    label: '注册时间',
    field: 'registration',
    component: 'DatePicker',
    componentProps: {
       showTime: true,
       valueFormat: 'YYYY-MM-DD HH:mm:ss'
     },
  },
  {
    label: '展示',
    field: 'enableType',
     component: 'JSwitch',
     componentProps:{
     },
  },
  {
    label: '置顶',
    field: 'topType',
     component: 'JSwitch',
     componentProps:{
     },
  },
	// TODO 主键隐藏字段，目前写死为ID
	{
	  label: '',
	  field: 'id',
	  component: 'Input',
	  show: false
	},
];

// 高级查询数据
export const superQuerySchema = {
  name: {title: '公司名称',order: 0,view: 'text', type: 'string',},
  img: {title: '图片',order: 1,view: 'image', type: 'string',},
  phone: {title: '电话',order: 2,view: 'text', type: 'string',},
  firmTypeId: {title: '类型',order: 3,view: 'text', type: 'string',},
  pageSort: {title: '网页排序',order: 4,view: 'text', type: 'string',},
  additionalId: {title: '其他信息',order: 5,view: 'textarea', type: 'string',},
  firmUrl: {title: '官网地址',order: 6,view: 'text', type: 'string',},
  registration: {title: '注册时间',order: 7,view: 'datetime', type: 'string',},
  enableType: {title: '展示',order: 8,view: 'number', type: 'number',},
  topType: {title: '置顶',order: 9,view: 'number', type: 'number',},
};

/**
* 流程表单调用这个方法获取formSchema
* @param param
*/
export function getBpmFormSchema(_formData): FormSchema[]{
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}