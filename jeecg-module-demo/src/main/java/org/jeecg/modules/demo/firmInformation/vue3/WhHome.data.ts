import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
   {
    title: '网站名',
    align:"center",
    dataIndex: 'name'
   },
   {
    title: '网站logo',
    align:"center",
    dataIndex: 'homeImg',
    customRender:render.renderImage,
   },
   {
    title: '移动端logo',
    align:"center",
    dataIndex: 'moveImg',
    customRender:render.renderImage,
   },
   {
    title: '公司简介',
    align:"center",
    dataIndex: 'introduction',
   },
   {
    title: '邮箱',
    align:"center",
    dataIndex: 'mailbox'
   },
   {
    title: '电话',
    align:"center",
    dataIndex: 'phone'
   },
   {
    title: '地址',
    align:"center",
    dataIndex: 'address'
   },
   {
    title: '版权申明',
    align:"center",
    dataIndex: 'copyright'
   },
   {
    title: '备案号',
    align:"center",
    dataIndex: 'icpNumber'
   },
   {
    title: '微信号',
    align:"center",
    dataIndex: 'wechat'
   },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '网站名',
    field: 'name',
    component: 'Input',
  },
  {
    label: '网站logo',
    field: 'homeImg',
     component: 'JImageUpload',
     componentProps:{
        fileMax: 0
      },
  },
  {
    label: '移动端logo',
    field: 'moveImg',
     component: 'JImageUpload',
     componentProps:{
        fileMax: 0
      },
  },
  {
    label: '公司简介',
    field: 'introduction',
    component: 'JEditor',
  },
  {
    label: '邮箱',
    field: 'mailbox',
    component: 'Input',
  },
  {
    label: '电话',
    field: 'phone',
    component: 'Input',
  },
  {
    label: '地址',
    field: 'address',
    component: 'Input',
  },
  {
    label: '版权申明',
    field: 'copyright',
    component: 'Input',
  },
  {
    label: '备案号',
    field: 'icpNumber',
    component: 'Input',
  },
  {
    label: '微信号',
    field: 'wechat',
    component: 'Input',
  },
	// TODO 主键隐藏字段，目前写死为ID
	{
	  label: '',
	  field: 'id',
	  component: 'Input',
	  show: false
	},
];

// 高级查询数据
export const superQuerySchema = {
  name: {title: '网站名',order: 0,view: 'text', type: 'string',},
  homeImg: {title: '网站logo',order: 1,view: 'image', type: 'string',},
  moveImg: {title: '移动端logo',order: 2,view: 'image', type: 'string',},
  introduction: {title: '公司简介',order: 3,view: 'umeditor', type: 'string',},
  mailbox: {title: '邮箱',order: 4,view: 'text', type: 'string',},
  phone: {title: '电话',order: 5,view: 'text', type: 'string',},
  address: {title: '地址',order: 6,view: 'text', type: 'string',},
  copyright: {title: '版权申明',order: 7,view: 'text', type: 'string',},
  icpNumber: {title: '备案号',order: 8,view: 'text', type: 'string',},
  wechat: {title: '微信号',order: 9,view: 'text', type: 'string',},
};

/**
* 流程表单调用这个方法获取formSchema
* @param param
*/
export function getBpmFormSchema(_formData): FormSchema[]{
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}