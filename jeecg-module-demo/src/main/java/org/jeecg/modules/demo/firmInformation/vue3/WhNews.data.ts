import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
   {
    title: '封面图',
    align:"center",
    dataIndex: 'newsImg',
    customRender:render.renderImage,
   },
   {
    title: '简介',
    align:"center",
    dataIndex: 'introduce'
   },
   {
    title: '网页排序',
    align:"center",
    dataIndex: 'pageSort'
   },
   {
    title: '标题',
    align:"center",
    dataIndex: 'name'
   },
   {
    title: '置顶',
    align:"center",
    dataIndex: 'topType',
    customRender:({text}) => {
       return  render.renderSwitch(text, [{text:'是',value:'Y'},{text:'否',value:'N'}])
     },
   },
   {
    title: '展示',
    align:"center",
    dataIndex: 'enableType',
    customRender:({text}) => {
       return  render.renderSwitch(text, [{text:'是',value:'Y'},{text:'否',value:'N'}])
     },
   },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
	{
      label: "标题",
      field: 'name',
      component: 'Input',
      //colProps: {span: 6},
 	},
];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '封面图',
    field: 'newsImg',
     component: 'JImageUpload',
     componentProps:{
        fileMax: 0
      },
  },
  {
    label: '简介',
    field: 'introduce',
    component: 'InputTextArea',
  },
  {
    label: '网页排序',
    field: 'pageSort',
    component: 'Input',
  },
  {
    label: '标题',
    field: 'name',
    component: 'Input',
  },
  {
    label: '置顶',
    field: 'topType',
     component: 'JSwitch',
     componentProps:{
     },
  },
  {
    label: '展示',
    field: 'enableType',
     component: 'JSwitch',
     componentProps:{
     },
  },
	// TODO 主键隐藏字段，目前写死为ID
	{
	  label: '',
	  field: 'id',
	  component: 'Input',
	  show: false
	},
];

// 高级查询数据
export const superQuerySchema = {
  newsImg: {title: '封面图',order: 0,view: 'image', type: 'string',},
  introduce: {title: '简介',order: 1,view: 'textarea', type: 'string',},
  pageSort: {title: '网页排序',order: 2,view: 'text', type: 'string',},
  name: {title: '标题',order: 3,view: 'text', type: 'string',},
  topType: {title: '置顶',order: 4,view: 'switch', type: 'string',},
  enableType: {title: '展示',order: 5,view: 'switch', type: 'string',},
};

/**
* 流程表单调用这个方法获取formSchema
* @param param
*/
export function getBpmFormSchema(_formData): FormSchema[]{
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}