import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
   {
    title: '标题',
    align:"center",
    dataIndex: 'name'
   },
   {
    title: '产品图',
    align:"center",
    dataIndex: 'productsImg',
    customRender:render.renderImage,
   },
   {
    title: '简介',
    align:"center",
    dataIndex: 'introduction'
   },
   {
    title: '点击数主键',
    align:"center",
    dataIndex: 'clicksId'
   },
   {
    title: '排序',
    align:"center",
    dataIndex: 'pageSort'
   },
   {
    title: '产品官网',
    align:"center",
    dataIndex: 'productsUrl'
   },
   {
    title: '详细介绍',
    align:"center",
    dataIndex: 'detailed',
   },
   {
    title: '展示',
    align:"center",
    dataIndex: 'enableType',
    customRender:({text}) => {
       return  render.renderSwitch(text, [{text:'是',value:'Y'},{text:'否',value:'N'}])
     },
   },
   {
    title: '置顶',
    align:"center",
    dataIndex: 'topType',
    customRender:({text}) => {
       return  render.renderSwitch(text, [{text:'是',value:'Y'},{text:'否',value:'N'}])
     },
   },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
	{
      label: "标题",
      field: 'name',
      component: 'Input',
      //colProps: {span: 6},
 	},
];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '标题',
    field: 'name',
    component: 'Input',
  },
  {
    label: '产品图',
    field: 'productsImg',
     component: 'JImageUpload',
     componentProps:{
        fileMax: 0
      },
  },
  {
    label: '简介',
    field: 'introduction',
    component: 'InputTextArea',
  },
  {
    label: '点击数主键',
    field: 'clicksId',
    component: 'Input',
  },
  {
    label: '排序',
    field: 'pageSort',
    component: 'Input',
  },
  {
    label: '产品官网',
    field: 'productsUrl',
    component: 'Input',
  },
  {
    label: '详细介绍',
    field: 'detailed',
    component: 'JEditor',
  },
  {
    label: '展示',
    field: 'enableType',
     component: 'JSwitch',
     componentProps:{
     },
  },
  {
    label: '置顶',
    field: 'topType',
     component: 'JSwitch',
     componentProps:{
     },
  },
	// TODO 主键隐藏字段，目前写死为ID
	{
	  label: '',
	  field: 'id',
	  component: 'Input',
	  show: false
	},
];

// 高级查询数据
export const superQuerySchema = {
  name: {title: '标题',order: 0,view: 'text', type: 'string',},
  productsImg: {title: '产品图',order: 1,view: 'image', type: 'string',},
  introduction: {title: '简介',order: 2,view: 'textarea', type: 'string',},
  clicksId: {title: '点击数主键',order: 3,view: 'text', type: 'string',},
  pageSort: {title: '排序',order: 4,view: 'text', type: 'string',},
  productsUrl: {title: '产品官网',order: 5,view: 'text', type: 'string',},
  detailed: {title: '详细介绍',order: 6,view: 'umeditor', type: 'string',},
  enableType: {title: '展示',order: 7,view: 'switch', type: 'string',},
  topType: {title: '置顶',order: 8,view: 'switch', type: 'string',},
};

/**
* 流程表单调用这个方法获取formSchema
* @param param
*/
export function getBpmFormSchema(_formData): FormSchema[]{
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}