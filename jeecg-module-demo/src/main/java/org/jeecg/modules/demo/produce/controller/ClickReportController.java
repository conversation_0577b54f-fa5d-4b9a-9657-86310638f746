package org.jeecg.modules.demo.produce.controller;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.demo.firmInformation.entity.WhFirmInformation;
import org.jeecg.modules.demo.firmInformation.service.IWhFirmInformationService;
import org.jeecg.modules.demo.produce.dto.WhGenerateClickDto;
import org.jeecg.modules.demo.produce.entity.ClickReport;
import org.jeecg.modules.demo.produce.service.IClickReportService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: 合作公司点击报表
 * @Author: jeecg-boot
 * @Date:   2024-08-21
 * @Version: V1.0
 */
@Api(tags="合作公司点击报表")
@RestController
@RequestMapping("/produce/clickReport")
@Slf4j
public class ClickReportController extends JeecgController<ClickReport, IClickReportService> {
	@Autowired
	private IClickReportService clickReportService;
	@Resource
	private IWhFirmInformationService whFirmInformationService;
	
	/**
	 * 分页列表查询
	 *
	 * @param clickReport
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "合作公司点击报表-分页列表查询")
	@ApiOperation(value="合作公司点击报表-分页列表查询", notes="合作公司点击报表-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<ClickReport>> queryPageList(
									@RequestParam(name="companyName", required = false) String companyName,
									@RequestParam(name="startTime", required = false) String startTime,
									@RequestParam(name="endTime", required = false) String endTime,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		LambdaQueryWrapper<ClickReport> queryWrapper = new LambdaQueryWrapper<>();


		// 如果有 companyName，则先获取符合条件的公司ID
		if (StrUtil.isNotEmpty(companyName)) {
			List<WhFirmInformation> list = whFirmInformationService.lambdaQuery()
					.like(WhFirmInformation::getName, companyName)
					.list();

			if (CollectionUtil.isNotEmpty(list)) {
				List<String> ids = list.stream()
						.map(WhFirmInformation::getId)
						.collect(Collectors.toList());
				queryWrapper.in(ClickReport::getCompanyId, ids);
			} else {
				// 如果没有找到符合条件的公司，直接返回空结果
				Page<ClickReport> emptyPage = new Page<>(pageNo, pageSize);
				return Result.OK(emptyPage);
			}
		}

		// 根据时间范围过滤记录
		if (StrUtil.isNotEmpty(startTime) && StrUtil.isNotEmpty(endTime)) {
			queryWrapper.between(ClickReport::getStatDate, startTime, endTime);
		} else if (StrUtil.isNotEmpty(startTime)) {
			queryWrapper.ge(ClickReport::getStatDate, startTime);
		} else if (StrUtil.isNotEmpty(endTime)) {
			queryWrapper.le(ClickReport::getStatDate, endTime);
		}
		queryWrapper.orderByDesc(ClickReport::getStatDate);

		// 执行分页查询
		Page<ClickReport> page = new Page<>(pageNo, pageSize);
		IPage<ClickReport> pageList = clickReportService.page(page, queryWrapper);

		// 为每个 ClickReport 设置公司名称
		pageList.getRecords().forEach(item -> {
			WhFirmInformation firmInformation = whFirmInformationService.getById(item.getCompanyId());
			if (firmInformation != null) {
				item.setCompanyName(firmInformation.getName());
			}
		});

		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param clickReport
	 * @return
	 */
	@AutoLog(value = "合作公司点击报表-添加")
	@ApiOperation(value="合作公司点击报表-添加", notes="合作公司点击报表-添加")
	@RequiresPermissions("produce:click_report:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody ClickReport clickReport) {
		clickReportService.save(clickReport);
		return Result.OK("添加成功！");
	}

	 /**
	  *   添加
	  *
	  * @param clickReport
	  * @return
	  */
	 @ApiOperation(value="合作公司点击报表-按照日期生成", notes="合作公司点击报表-按照日期生成")
	 @RequiresPermissions("produce:click_report:add")
	 @PostMapping(value = "/generate")
	 public Result<String> generate(@RequestBody WhGenerateClickDto dto) {
		 clickReportService.generate(dto);
		 return Result.OK("添加成功！");
	 }
	
	/**
	 *  编辑
	 *
	 * @param clickReport
	 * @return
	 */
	@AutoLog(value = "合作公司点击报表-编辑")
	@ApiOperation(value="合作公司点击报表-编辑", notes="合作公司点击报表-编辑")
	@RequiresPermissions("produce:click_report:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody ClickReport clickReport) {
		clickReportService.updateById(clickReport);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "合作公司点击报表-通过id删除")
	@ApiOperation(value="合作公司点击报表-通过id删除", notes="合作公司点击报表-通过id删除")
	@RequiresPermissions("produce:click_report:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		clickReportService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "合作公司点击报表-批量删除")
	@ApiOperation(value="合作公司点击报表-批量删除", notes="合作公司点击报表-批量删除")
	@RequiresPermissions("produce:click_report:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.clickReportService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "合作公司点击报表-通过id查询")
	@ApiOperation(value="合作公司点击报表-通过id查询", notes="合作公司点击报表-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<ClickReport> queryById(@RequestParam(name="id",required=true) String id) {
		ClickReport clickReport = clickReportService.getById(id);
		if(clickReport==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(clickReport);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param clickReport
    */
    @RequiresPermissions("produce:click_report:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, ClickReport clickReport) {
        return super.exportXls(request, clickReport, ClickReport.class, "合作公司点击报表");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("produce:click_report:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, ClickReport.class);
    }

}
