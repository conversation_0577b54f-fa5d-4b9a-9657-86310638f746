package org.jeecg.modules.demo.produce.controller;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.demo.produce.entity.WhBackgrounConfig;
import org.jeecg.modules.demo.produce.service.IWhBackgrounConfigService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.modules.demo.produce.vo.WhBackgrounConfigVo;
import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.util.RedisUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: 后台配置
 * @Author: jeecg-boot
 * @Date:   2024-08-23
 * @Version: V1.0
 */
@Api(tags="后台配置")
@RestController
@RequestMapping("/produce/whBackgrounConfig")
@Slf4j
public class WhBackgrounConfigController extends JeecgController<WhBackgrounConfig, IWhBackgrounConfigService> {
	@Autowired
	private IWhBackgrounConfigService whBackgrounConfigService;

	@Autowired
	private RedisUtil redisUtil;

	/**
	 * 分页列表查询
	 *
	 * @param whBackgrounConfig
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "后台配置-分页列表查询")
	@ApiOperation(value="后台配置-分页列表查询", notes="后台配置-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<WhBackgrounConfig>> queryPageList(WhBackgrounConfig whBackgrounConfig,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<WhBackgrounConfig> queryWrapper = QueryGenerator.initQueryWrapper(whBackgrounConfig, req.getParameterMap());
		Page<WhBackgrounConfig> page = new Page<WhBackgrounConfig>(pageNo, pageSize);
		IPage<WhBackgrounConfig> pageList = whBackgrounConfigService.page(page, queryWrapper);
		return Result.OK(pageList);
	}

	/**
	 *   添加
	 *
	 * @param whBackgrounConfig
	 * @return
	 */
	@AutoLog(value = "后台配置-添加")
	@ApiOperation(value="后台配置-添加", notes="后台配置-添加")
	@RequiresPermissions("produce:wh_backgroun_config:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody WhBackgrounConfig whBackgrounConfig) {
		whBackgrounConfigService.save(whBackgrounConfig);
		return Result.OK("添加成功！");
	}

	/**
	 *  编辑
	 *
	 * @param whBackgrounConfig
	 * @return
	 */
	@AutoLog(value = "后台配置-编辑")
	@ApiOperation(value="后台配置-编辑", notes="后台配置-编辑")
	@RequiresPermissions("produce:wh_backgroun_config:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody WhBackgrounConfig whBackgrounConfig) {
		whBackgrounConfigService.updateById(whBackgrounConfig);
		return Result.OK("编辑成功!");
	}

	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "后台配置-通过id删除")
	@ApiOperation(value="后台配置-通过id删除", notes="后台配置-通过id删除")
	@RequiresPermissions("produce:wh_backgroun_config:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		whBackgrounConfigService.removeById(id);
		return Result.OK("删除成功!");
	}

	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "后台配置-批量删除")
	@ApiOperation(value="后台配置-批量删除", notes="后台配置-批量删除")
	@RequiresPermissions("produce:wh_backgroun_config:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.whBackgrounConfigService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}

	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "后台配置-通过id查询")
	@ApiOperation(value="后台配置-通过id查询", notes="后台配置-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<WhBackgrounConfig> queryById(@RequestParam(name="id",required=true) String id) {
		WhBackgrounConfig whBackgrounConfig = whBackgrounConfigService.getById(id);
		if(whBackgrounConfig==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(whBackgrounConfig);
	}

	 @ApiOperation(value="后台配置-通过 code 修改", notes="后台配置-通过 code 修改")
	 @GetMapping(value = "/queryByCode")
	 public Result<WhBackgrounConfigVo> queryByCode(@RequestParam(name="code",required=true) String code) {
		 WhBackgrounConfig whBackgrounConfig = whBackgrounConfigService.getOne(new LambdaQueryWrapper<WhBackgrounConfig>()
				 .eq(WhBackgrounConfig::getCode, code));
		 if(whBackgrounConfig==null) {
			 return Result.error("未找到对应数据");
		 }
		 String conversion = whBackgrounConfig.getConversion();
		 String[] split = conversion.split(",");
		 List<Integer> list = new ArrayList<>();
		 for (int i = 0; i < split.length; i++) {
			 if (i > 4) {
				 continue;
			 }
			 list.add(Integer.parseInt(split[i]));
		 }

		 WhBackgrounConfigVo vo = new WhBackgrounConfigVo();
		 vo.setId(whBackgrounConfig.getId())
				 .setCode(whBackgrounConfig.getCode())
				 .setName(whBackgrounConfig.getName())
				 .setClickSum(whBackgrounConfig.getClickSum())
				 .setUpdateTime(whBackgrounConfig.getUpdateTime())
				 .setCompanySum(whBackgrounConfig.getCompanySum())
				 .setClickTotal(whBackgrounConfig.getClickTotal())
				 .setPercent(whBackgrounConfig.getPercent())
				 .setActiveProduct(whBackgrounConfig.getActiveProduct())
				 .setIntegerList(list);
		 return Result.OK(vo);
	 }

    /**
    * 导出excel
    *
    * @param request
    * @param whBackgrounConfig
    */
    @RequiresPermissions("produce:wh_backgroun_config:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, WhBackgrounConfig whBackgrounConfig) {
        return super.exportXls(request, whBackgrounConfig, WhBackgrounConfig.class, "后台配置");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("produce:wh_backgroun_config:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, WhBackgrounConfig.class);
    }

    /**
     * 获取运营效果趋势数据
     */
    @ApiOperation(value="获取运营效果趋势数据", notes="获取运营效果趋势数据")
    @GetMapping(value = "/getOperationTrends")
    public Result<Map<String, Object>> getOperationTrends() {
        try {
            String trendsKey = "dashboard:metrics:operation_trends";
            Object trendsData = redisUtil.get(trendsKey);

            Map<String, Object> result = new HashMap<>();
            if (trendsData != null && trendsData instanceof Map) {
                result = (Map<String, Object>) trendsData;
            } else {
                // 如果Redis中没有数据，返回默认值
                result.put("weekly", 12);
                result.put("daily", 11);
            }

            return Result.OK(result);
        } catch (Exception e) {
            log.error("获取运营效果趋势数据失败", e);
            // 返回默认值
            Map<String, Object> defaultResult = new HashMap<>();
            defaultResult.put("weekly", 12);
            defaultResult.put("daily", 11);
            return Result.OK(defaultResult);
        }
    }

}
