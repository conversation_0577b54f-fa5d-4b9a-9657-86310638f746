package org.jeecg.modules.demo.produce.controller;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.demo.produce.entity.WhClickConfig;
import org.jeecg.modules.demo.produce.service.IWhClickConfigService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: 点击率配置
 * @Author: jeecg-boot
 * @Date:   2024-08-21
 * @Version: V1.0
 */
@Api(tags="点击率配置")
@RestController
@RequestMapping("/produce/whClickConfig")
@Slf4j
public class WhClickConfigController extends JeecgController<WhClickConfig, IWhClickConfigService> {
	@Autowired
	private IWhClickConfigService whClickConfigService;
	
	/**
	 * 分页列表查询
	 *
	 * @param whClickConfig
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "点击率配置-分页列表查询")
	@ApiOperation(value="点击率配置-分页列表查询", notes="点击率配置-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<WhClickConfig>> queryPageList(WhClickConfig whClickConfig,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<WhClickConfig> queryWrapper = QueryGenerator.initQueryWrapper(whClickConfig, req.getParameterMap());
		Page<WhClickConfig> page = new Page<WhClickConfig>(pageNo, pageSize);
		IPage<WhClickConfig> pageList = whClickConfigService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param whClickConfig
	 * @return
	 */
	@AutoLog(value = "点击率配置-添加")
	@ApiOperation(value="点击率配置-添加", notes="点击率配置-添加")
	@RequiresPermissions("produce:wh_click_config:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody WhClickConfig whClickConfig) {
		whClickConfigService.save(whClickConfig);
		return Result.OK("添加成功！");
	}

	 @ApiOperation(value="点击率配置-通过id查询", notes="点击率配置-通过id查询")
	 @GetMapping(value = "/getById")
	 public Result<WhClickConfig> getById() {
		 WhClickConfig whClickConfig = whClickConfigService.getById("1826975617534566402");
		 if(whClickConfig==null) {
			 return Result.error("未找到对应数据");
		 }
		 return Result.OK(whClickConfig);
	 }
	
	/**
	 *  编辑
	 *
	 * @param whClickConfig
	 * @return
	 */
	@AutoLog(value = "点击率配置-编辑")
	@ApiOperation(value="点击率配置-编辑", notes="点击率配置-编辑")
	@RequiresPermissions("produce:wh_click_config:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody WhClickConfig whClickConfig) {
		whClickConfigService.updateById(whClickConfig);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "点击率配置-通过id删除")
	@ApiOperation(value="点击率配置-通过id删除", notes="点击率配置-通过id删除")
	@RequiresPermissions("produce:wh_click_config:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		whClickConfigService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "点击率配置-批量删除")
	@ApiOperation(value="点击率配置-批量删除", notes="点击率配置-批量删除")
	@RequiresPermissions("produce:wh_click_config:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.whClickConfigService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "点击率配置-通过id查询")
	@ApiOperation(value="点击率配置-通过id查询", notes="点击率配置-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<WhClickConfig> queryById(@RequestParam(name="id",required=true) String id) {
		WhClickConfig whClickConfig = whClickConfigService.getById(id);
		if(whClickConfig==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(whClickConfig);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param whClickConfig
    */
    @RequiresPermissions("produce:wh_click_config:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, WhClickConfig whClickConfig) {
        return super.exportXls(request, whClickConfig, WhClickConfig.class, "点击率配置");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("produce:wh_click_config:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, WhClickConfig.class);
    }

}
