package org.jeecg.modules.demo.produce.controller;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.demo.produce.entity.WhGenerateClick;
import org.jeecg.modules.demo.produce.service.IWhGenerateClickService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: 生成点击数
 * @Author: jeecg-boot
 * @Date:   2024-08-21
 * @Version: V1.0
 */
@Api(tags="生成点击数")
@RestController
@RequestMapping("/produce/whGenerateClick")
@Slf4j
public class WhGenerateClickController extends JeecgController<WhGenerateClick, IWhGenerateClickService> {
	@Autowired
	private IWhGenerateClickService whGenerateClickService;
	
	/**
	 * 分页列表查询
	 *
	 * @param whGenerateClick
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "生成点击数-分页列表查询")
	@ApiOperation(value="生成点击数-分页列表查询", notes="生成点击数-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<WhGenerateClick>> queryPageList(WhGenerateClick whGenerateClick,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<WhGenerateClick> queryWrapper = QueryGenerator.initQueryWrapper(whGenerateClick, req.getParameterMap());
		Page<WhGenerateClick> page = new Page<WhGenerateClick>(pageNo, pageSize);
		IPage<WhGenerateClick> pageList = whGenerateClickService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param whGenerateClick
	 * @return
	 */
	@AutoLog(value = "生成点击数-添加")
	@ApiOperation(value="生成点击数-添加", notes="生成点击数-添加")
	@RequiresPermissions("produce:wh_generate_click:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody WhGenerateClick whGenerateClick) {
		whGenerateClickService.save(whGenerateClick);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param whGenerateClick
	 * @return
	 */
	@AutoLog(value = "生成点击数-编辑")
	@ApiOperation(value="生成点击数-编辑", notes="生成点击数-编辑")
	@RequiresPermissions("produce:wh_generate_click:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody WhGenerateClick whGenerateClick) {
		whGenerateClickService.updateById(whGenerateClick);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "生成点击数-通过id删除")
	@ApiOperation(value="生成点击数-通过id删除", notes="生成点击数-通过id删除")
	@RequiresPermissions("produce:wh_generate_click:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		whGenerateClickService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "生成点击数-批量删除")
	@ApiOperation(value="生成点击数-批量删除", notes="生成点击数-批量删除")
	@RequiresPermissions("produce:wh_generate_click:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.whGenerateClickService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "生成点击数-通过id查询")
	@ApiOperation(value="生成点击数-通过id查询", notes="生成点击数-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<WhGenerateClick> queryById(@RequestParam(name="id",required=true) String id) {
		WhGenerateClick whGenerateClick = whGenerateClickService.getById(id);
		if(whGenerateClick==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(whGenerateClick);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param whGenerateClick
    */
    @RequiresPermissions("produce:wh_generate_click:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, WhGenerateClick whGenerateClick) {
        return super.exportXls(request, whGenerateClick, WhGenerateClick.class, "生成点击数");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("produce:wh_generate_click:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, WhGenerateClick.class);
    }

}
