package org.jeecg.modules.demo.produce.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 后台配置
 * @Author: jeecg-boot
 * @Date:   2024-08-23
 * @Version: V1.0
 */
@Data
@TableName("wh_backgroun_config")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="wh_backgroun_config对象", description="后台配置")
public class WhBackgrounConfig implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private String id;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private Date updateTime;
	/**总点击数*/
	@Excel(name = "总点击数", width = 15)
    @ApiModelProperty(value = "总点击数")
    private Integer clickSum;
    @ApiModelProperty(value = "累计点击数")
    private Long clickTotal;
	/**合作公司总数*/
	@Excel(name = "合作公司总数", width = 15)
    @ApiModelProperty(value = "合作公司总数")
    private String companySum;
	/**转换率*/
	@Excel(name = "转换率", width = 15)
    @ApiModelProperty(value = "转换率")
    private String conversion;
	/**百分比*/
	@Excel(name = "百分比", width = 15)
    @ApiModelProperty(value = "百分比")
    private String percent;
	/**编码*/
	@Excel(name = "编码", width = 15)
    @ApiModelProperty(value = "编码")
    private String code;
	/**名称*/
	@Excel(name = "名称", width = 15)
    @ApiModelProperty(value = "名称")
    private String name;
	/**活跃产品数/用户停留时长*/
	@Excel(name = "活跃产品数", width = 15)
    @ApiModelProperty(value = "活跃产品数/用户停留时长")
    private String activeProduct;
}
