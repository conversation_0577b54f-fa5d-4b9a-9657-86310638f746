package org.jeecg.modules.demo.produce.job;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.demo.firmInformation.entity.WhFirmInformation;
import org.jeecg.modules.demo.firmInformation.service.IWhFirmInformationService;
import org.jeecg.modules.demo.produce.entity.ClickReport;
import org.jeecg.modules.demo.produce.entity.WhClickConfig;
import org.jeecg.modules.demo.produce.service.IClickReportService;
import org.jeecg.modules.demo.produce.service.IWhClickConfigService;
import org.quartz.*;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
@Slf4j
public class ClickJob implements Job {
    @Autowired
    private IWhFirmInformationService whFirmInformationService;
    @Autowired
    private IClickReportService clickReportService;
    @Autowired
    private IWhClickConfigService whClickConfigService;
    @Override
    public void execute(JobExecutionContext jobExecutionContext) throws JobExecutionException {
        JobDetail jobDetail = jobExecutionContext.getJobDetail();
        JobDataMap mergedJobDataMap = jobDetail.getJobDataMap();
        String parameter = mergedJobDataMap.get("parameter").toString();
        Date startTime = null;
        Date endTime = null;
        if (StrUtil.isNotBlank(parameter)) {
            JSONObject jsonObject = JSON.parseObject(parameter);
             startTime = jsonObject.getDate("startTime");
             endTime = jsonObject.getDate("endTime");
        }


        //1、查询所有合作公司id
        List<WhFirmInformation> list = whFirmInformationService.list();
        if (CollectionUtil.isEmpty(list)) {
            return;
        }
        List<String> companyIdList = list.stream().map(WhFirmInformation::getId).collect(Collectors.toList());

        //2、查询生成比例配置
        WhClickConfig config =null;
        double randomCent = 0;
        double endCent = 0;
        Integer randomNum = 0;
        Integer randomEndNum = 0;

        if (config != null) {
            if (ObjectUtil.equal(config.getCentOff(),"Y")) {
                //按照比例
                randomCent = config.getRandomCent();
                endCent = config.getRandomEndCent();
            } else {
                //按照数额区间
                randomNum = config.getRandomNum();
                randomEndNum = config.getRandomEndNum();
            }
        } else {
            //如果生成比例配置不存在，则按照5000-9000之间的随机数来生成
            randomNum = 5000;
            randomEndNum = 9000;
        }

        //3、判断是否有日期选择
        List<ClickReport> saveCheckList = new ArrayList<>();
        List<ClickReport> saveList = new ArrayList<>();

        if (startTime != null && endTime != null) {
            //3.1 startTime 和 endTime 是否同时存在，存在的话按照日期区间，生成区间内的每天数据
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(startTime);

            while (!calendar.getTime().after(endTime)) {
                Date currentDate = calendar.getTime();

                for (String companyId : companyIdList) {
                    ClickReport clickReport = new ClickReport();
                    clickReport.setCompanyId(companyId);

                    //点击数
                    Integer clickNum;
                    if (config != null && ObjectUtil.equal(config.getCentOff(), "Y")) {
                        clickNum = (int) (Math.random() * (endCent - randomCent) + randomCent);
                    } else {
                        clickNum = (int) (Math.random() * (randomEndNum - randomNum) + randomNum);
                    }
                    clickReport.setClickNum(clickNum);

                    //报表日期
                    clickReport.setStatDate(currentDate);

                    saveCheckList.add(clickReport);
                }

                // 日期加1
                calendar.add(Calendar.DATE, 1);
            }
        } else {
            //3.2 没有传日期参数，按照查询到的合作公司id，生成今日的点击数量与日期即可
            Date currentDate = new Date();

            for (String companyId : companyIdList) {
                ClickReport clickReport = new ClickReport();
                clickReport.setCompanyId(companyId);

                //点击数
                Integer clickNum;
                if (config != null && ObjectUtil.equal(config.getCentOff(), "Y")) {
                    clickNum = (int) (Math.random() * (endCent - randomCent) + randomCent);
                } else {
                    clickNum = (int) (Math.random() * (randomEndNum - randomNum) + randomNum);
                }
                clickReport.setClickNum(clickNum);

                //报表日期
                clickReport.setStatDate(currentDate);

                saveCheckList.add(clickReport);
            }
        }
        List<ClickReport> updateList = new ArrayList<>();
        //判断是更新还是新增
        for (ClickReport clickReport : saveCheckList) {
            // 检查是否已经存在报表
            QueryWrapper<ClickReport> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("company_id", clickReport.getCompanyId());
            queryWrapper.eq("stat_date", clickReport.getStatDate());

            ClickReport existingReport = this.clickReportService.getOne(queryWrapper);
            //ClickReport existingReport = clickReports1.get(0);
            if (existingReport != null) {
                // 更新已存在的记录
                existingReport.setClickNum(clickReport.getClickNum());
                updateList.add(existingReport);
            } else {
                // 新增记录
                saveList.add(clickReport);

            }
        }

        //4.批量保存数据
        // 定义每次保存的批量大小
        int batchSize = 500;
        if (CollectionUtil.isNotEmpty(saveList)){
            // 获取总的数据量
            int totalSize = saveList.size();

            // 循环分批次保存
            for (int i = 0; i < totalSize; i += batchSize) {
                log.info("循环次数为:{}", i);
                // 获取当前批次的结束索引，不能超过 totalSize
                int endIndex = Math.min(i + batchSize, totalSize);

                // 获取当前批次的数据子列表
                List<ClickReport> batchList = saveList.subList(i, endIndex);

                // 批量保存当前批次的数据
                clickReportService.saveBatch(batchList);
            }
        }

        if (CollectionUtil.isNotEmpty(updateList)) {
            clickReportService.saveOrUpdateBatch(updateList);
        }



    }
}
