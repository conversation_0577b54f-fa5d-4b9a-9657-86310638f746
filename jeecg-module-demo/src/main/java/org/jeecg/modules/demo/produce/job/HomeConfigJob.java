package org.jeecg.modules.demo.produce.job;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.modules.demo.firmInformation.service.IWhFirmInformationService;
import org.jeecg.modules.demo.produce.entity.ClickReport;
import org.jeecg.modules.demo.produce.entity.WhBackgrounConfig;
import org.jeecg.modules.demo.produce.service.IClickReportService;
import org.jeecg.modules.demo.produce.service.IWhBackgrounConfigService;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.Objects;

@Slf4j
public class HomeConfigJob implements Job {
    @Autowired
    private IWhBackgrounConfigService whBackgrounConfigService;
    @Autowired
    private IClickReportService clickReportService;
    @Autowired
    private IWhFirmInformationService whFirmInformationService;
    @Override
    public void execute(JobExecutionContext jobExecutionContext) throws JobExecutionException {
        WhBackgrounConfig whBackgrounConfig = whBackgrounConfigService.getOne(new LambdaQueryWrapper<WhBackgrounConfig>()
                .eq(WhBackgrounConfig::getCode, "home_code"));
        if(whBackgrounConfig==null) {
            return;
        }
        //1、修改点击数为当日合计点击数：clickReportService中所有是今日的数据，Integer clickNum;汇总
        // 获取当前日期，忽略时间部分
        LocalDate today = LocalDate.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        String todayString = today.format(formatter);

        // 使用LambdaQueryWrapper查询statDate为今日的所有数据，并计算clickNum的合计
        Integer clickSum = clickReportService.list(
                        new LambdaQueryWrapper<ClickReport>()
                                .eq(ClickReport::getStatDate, todayString)
                ).stream()
                .map(ClickReport::getClickNum)
                .filter(Objects::nonNull)
                .reduce(0, Integer::sum);

        // 将合计的点击数设置到whBackgrounConfig对象
        whBackgrounConfig.setClickSum(clickSum);

        //2、合作公司取所有的公司，查询count：whFirmInformationService
        long count = whFirmInformationService.count();
        whBackgrounConfig.setCompanySum(String.valueOf(count));
        // 3. 百分比取150%-400%之间的随机值
        int minPercent = 150;
        int maxPercent = 400;
        int randomPercent = minPercent + (int)(Math.random() * ((maxPercent - minPercent) + 1));
        whBackgrounConfig.setPercent(String.valueOf(randomPercent));

        // 4. 修改为7以内的随机拼接，长度为5，随机数不为0
        StringBuilder conversionBuilder = new StringBuilder();
        for (int i = 0; i < 5; i++) {
            if (i > 0) {
                conversionBuilder.append(",");
            }
            // 生成1到6之间的随机数（不包含0）
            int randomNum = 1 + (int)(Math.random() * 6);
            conversionBuilder.append(randomNum);
        }
        whBackgrounConfig.setConversion(conversionBuilder.toString());


        // 更新数据库中的记录
        whBackgrounConfigService.updateById(whBackgrounConfig);
    }
}
