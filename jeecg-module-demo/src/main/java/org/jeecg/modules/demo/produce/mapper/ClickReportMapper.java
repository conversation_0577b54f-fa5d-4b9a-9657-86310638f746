package org.jeecg.modules.demo.produce.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.demo.produce.entity.ClickReport;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * @Description: 合作公司点击报表
 * @Author: jeecg-boot
 * @Date:   2024-08-21
 * @Version: V1.0
 */
public interface ClickReportMapper extends BaseMapper<ClickReport> {

    Integer getClickSum();

    Long getClickTotalSum();

}
