<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.demo.produce.mapper.ClickReportMapper">

    <select id="getClickSum" resultType="java.lang.Integer">
        SELECT SUM(click_num)
        FROM click_report
        WHERE stat_date = CURDATE()
    </select>

    <select id="getClickTotalSum" resultType="java.lang.Long">
        SELECT SUM(click_num)
        FROM click_report
    </select>

</mapper>