package org.jeecg.modules.demo.produce.service;

import org.jeecg.modules.demo.produce.dto.WhGenerateClickDto;
import org.jeecg.modules.demo.produce.entity.ClickReport;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * @Description: 合作公司点击报表
 * @Author: jeecg-boot
 * @Date:   2024-08-21
 * @Version: V1.0
 */
public interface IClickReportService extends IService<ClickReport> {

    void generate(WhGenerateClickDto dto);

    Integer getClickSum();

    Long getClickTotalSum();

}
