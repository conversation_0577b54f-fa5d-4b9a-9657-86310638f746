package org.jeecg.modules.demo.produce.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.jeecg.modules.demo.produce.dto.WhGenerateClickDto;
import org.jeecg.modules.demo.produce.entity.ClickReport;
import org.jeecg.modules.demo.produce.mapper.ClickReportMapper;
import org.jeecg.modules.demo.produce.service.IClickReportService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * @Description: 合作公司点击报表
 * @Author: jeecg-boot
 * @Date:   2024-08-21
 * @Version: V1.0
 */
@Service
public class ClickReportServiceImpl extends ServiceImpl<ClickReportMapper, ClickReport> implements IClickReportService {

    @Override
    public void generate(WhGenerateClickDto dto) {
        // 检查参数有效性
        if (dto.getStartTime() == null || dto.getEndTime() == null || dto.getCompanyId() == null || dto.getAccountNum() == null) {
            throw new IllegalArgumentException("开始时间、结束时间、合作公司ID和结算点击数为必填项");
        }

        // 计算总天数
        long diffInMillies = Math.abs(dto.getEndTime().getTime() - dto.getStartTime().getTime());
        long totalDays = TimeUnit.DAYS.convert(diffInMillies, TimeUnit.MILLISECONDS) + 1;

        // 生成点击数列表，总和等于结算点击数（随机分配）
        int totalClickNum = Integer.parseInt(dto.getAccountNum());
        List<Integer> clickNums = distributeClicksRandomly(totalClickNum, (int) totalDays);

        Calendar calendar = Calendar.getInstance();
        calendar.setTime(dto.getStartTime());

        List<ClickReport> clickReports = new ArrayList<>();

        for (int i = 0; i < totalDays; i++) {
            Date currentDate = calendar.getTime();

            // 检查是否已经存在报表
            QueryWrapper<ClickReport> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("company_id", dto.getCompanyId());
            queryWrapper.eq("stat_date", currentDate);

            ClickReport existingReport = this.getOne(queryWrapper);
            //ClickReport existingReport = clickReports1.get(0);
            if (existingReport != null) {
                // 更新已存在的记录
                existingReport.setClickNum(clickNums.get(i));
                this.updateById(existingReport);
            } else {
                // 新增记录
                ClickReport clickReport = new ClickReport();
                clickReport.setCompanyId(dto.getCompanyId());
                clickReport.setStatDate(currentDate);
                clickReport.setClickNum(clickNums.get(i));
                clickReports.add(clickReport);
            }

            // 日期加一天
            calendar.add(Calendar.DAY_OF_MONTH, 1);
        }

        // 批量新增记录
        if (!clickReports.isEmpty()) {
            this.saveBatch(clickReports);
        }
    }

    @Override
    public Integer getClickSum() {
        return this.baseMapper.getClickSum();
    }

    @Override
    public Long getClickTotalSum() {
        return this.baseMapper.getClickTotalSum();
    }

    /**
     * 将总点击数随机分配到每一天
     * @param totalClickNum 总点击数
     * @param totalDays 天数
     * @return 每天的点击数列表
     */
    private List<Integer> distributeClicksRandomly(int totalClickNum, int totalDays) {
        List<Integer> clicks = new ArrayList<>();
        Random random = new Random();

        // 每天的基础点击数
        int baseClicks = 100;

        // 计算基础点击数所需的总点击数
        int baseTotalClicks = baseClicks * totalDays;

        // 如果总点击数小于基础点击数的总和，无法满足要求
        if (totalClickNum < baseTotalClicks) {
            throw new IllegalArgumentException("总点击数不足以满足每天至少100次点击的要求");
        }

        // 初始化所有天的点击数为基础点击数
        for (int i = 0; i < totalDays; i++) {
            clicks.add(baseClicks);
        }

        // 计算剩余的点击数
        int remainingClicks = totalClickNum - baseTotalClicks;

        // 随机分配剩余的点击数
        while (remainingClicks > 0) {
            int index = random.nextInt(totalDays); // 随机选择一天
            int maxAdditionalClicks = remainingClicks / (totalDays - 1) + 1; // 最大额外点击数
            int additionalClicks = random.nextInt(maxAdditionalClicks + 1); // 随机生成额外点击数

            // 将额外点击数添加到选择的天
            clicks.set(index, clicks.get(index) + additionalClicks);
            remainingClicks -= additionalClicks; // 减少剩余点击数
        }

        return clicks;
    }


}
