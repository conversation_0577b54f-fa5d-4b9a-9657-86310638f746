package org.jeecg.modules.demo.produce.service.impl;

import org.jeecg.modules.demo.produce.entity.WhBackgrounConfig;
import org.jeecg.modules.demo.produce.mapper.WhBackgrounConfigMapper;
import org.jeecg.modules.demo.produce.service.IWhBackgrounConfigService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: 后台配置
 * @Author: jeecg-boot
 * @Date:   2024-08-23
 * @Version: V1.0
 */
@Service
public class WhBackgrounConfigServiceImpl extends ServiceImpl<WhBackgrounConfigMapper, WhBackgrounConfig> implements IWhBackgrounConfigService {

}
