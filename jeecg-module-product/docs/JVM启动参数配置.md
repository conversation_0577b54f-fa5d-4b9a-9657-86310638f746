# JVM启动参数配置

## 减少POI反射警告

如果您在使用导出功能时看到以下警告：

```
WARNING: An illegal reflective access operation has occurred
WARNING: Illegal reflective access by org.apache.poi.util.DocumentHelper
WARNING: Please consider reporting this to the maintainers of org.apache.poi.util.DocumentHelper
WARNING: Use --illegal-access=warn to enable warnings of further illegal reflective access operations
WARNING: All illegal access operations will be denied in a future release
```

可以通过以下方式解决：

### 方案1：添加JVM启动参数（推荐）

在应用启动时添加以下JVM参数：

```bash
# 减少反射访问警告
--add-opens java.base/java.lang=ALL-UNNAMED
--add-opens java.base/java.util=ALL-UNNAMED
--add-opens java.base/java.text=ALL-UNNAMED
--add-opens java.desktop/java.awt.font=ALL-UNNAMED

# 或者简单粗暴的方式（不推荐生产环境）
--illegal-access=permit
```

### 方案2：IDEA启动配置

如果使用IDEA开发，在Run Configuration中添加VM options：

```
-Dfile.encoding=UTF-8
--add-opens java.base/java.lang=ALL-UNNAMED
--add-opens java.base/java.util=ALL-UNNAMED
--add-opens java.base/java.text=ALL-UNNAMED
--add-opens java.desktop/java.awt.font=ALL-UNNAMED
```

### 方案3：Docker部署配置

如果使用Docker部署，在Dockerfile或docker-compose.yml中配置：

```dockerfile
# Dockerfile
ENV JAVA_OPTS="-Dfile.encoding=UTF-8 --add-opens java.base/java.lang=ALL-UNNAMED --add-opens java.base/java.util=ALL-UNNAMED"
```

```yaml
# docker-compose.yml
services:
  app:
    environment:
      - JAVA_OPTS=-Dfile.encoding=UTF-8 --add-opens java.base/java.lang=ALL-UNNAMED --add-opens java.base/java.util=ALL-UNNAMED
```

### 方案4：Spring Boot配置

在application.yml中配置：

```yaml
# application.yml
spring:
  application:
    name: your-app
  jpa:
    properties:
      java:
        opts: "--add-opens java.base/java.lang=ALL-UNNAMED --add-opens java.base/java.util=ALL-UNNAMED"
```

## 大数据导出优化参数

针对百万级数据导出，建议的JVM参数配置：

```bash
# 内存配置
-Xms2g
-Xmx4g
-XX:NewRatio=1
-XX:SurvivorRatio=8

# GC配置（推荐G1GC）
-XX:+UseG1GC
-XX:MaxGCPauseMillis=200
-XX:G1HeapRegionSize=16m

# 减少反射警告
--add-opens java.base/java.lang=ALL-UNNAMED
--add-opens java.base/java.util=ALL-UNNAMED
--add-opens java.base/java.text=ALL-UNNAMED
--add-opens java.desktop/java.awt.font=ALL-UNNAMED

# 其他优化参数
-Dfile.encoding=UTF-8
-Djava.awt.headless=true
-Dspring.profiles.active=prod
```

## 完整启动命令示例

```bash
java -Xms2g -Xmx4g \
     -XX:+UseG1GC \
     -XX:MaxGCPauseMillis=200 \
     --add-opens java.base/java.lang=ALL-UNNAMED \
     --add-opens java.base/java.util=ALL-UNNAMED \
     --add-opens java.base/java.text=ALL-UNNAMED \
     --add-opens java.desktop/java.awt.font=ALL-UNNAMED \
     -Dfile.encoding=UTF-8 \
     -Djava.awt.headless=true \
     -jar your-application.jar
```

## 注意事项

1. **Java版本兼容性**：`--add-opens`参数仅适用于Java 9+
2. **生产环境**：避免使用`--illegal-access=permit`，应该使用具体的`--add-opens`参数
3. **内存配置**：根据实际服务器配置调整`-Xms`和`-Xmx`参数
4. **监控**：建议配置JVM监控，观察GC情况和内存使用
