package org.jeecg.modules.corp.controller;

import cn.hutool.core.util.StrUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.modules.corp.entity.PdCompanyInfo;
import org.jeecg.modules.corp.service.IPdCompanyInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;

@Api(tags="一期移动端-小程序关于我们")
@RestController
@RequestMapping("/api/corp/pdCompanyInfo")
@Slf4j
public class ApiPdCompanyInfoController {
    @Autowired
    private IPdCompanyInfoService pdCompanyInfoService;

    @ApiOperation(value="公司商务信息", notes="公司商务信息")
    @GetMapping(value = "/query")
    public Result<PdCompanyInfo> query() {
        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        String tenantId = request.getHeader("tenant-id-link");
        //log.info("报价租户,游客id:{},{}",tenantId,guestId);
        if (StrUtil.isEmpty(tenantId)) {
            throw new RuntimeException("未找到租户 id");
        }
        PdCompanyInfo pdCompanyInfo = pdCompanyInfoService.lambdaQuery().eq(PdCompanyInfo::getTenantId, tenantId).last("limit 1").one();
        if(pdCompanyInfo==null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(pdCompanyInfo);
    }

}
