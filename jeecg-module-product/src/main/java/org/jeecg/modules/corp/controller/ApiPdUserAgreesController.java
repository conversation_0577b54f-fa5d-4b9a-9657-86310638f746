package org.jeecg.modules.corp.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.modules.corp.entity.PdUserAgrees;
import org.jeecg.modules.corp.service.IPdUserAgreesService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

@Api(tags="一期移动端-用户同意协议")
@RestController
@RequestMapping("/api/corp/pdUserAgrees")
@Slf4j
public class ApiPdUserAgreesController {
    @Autowired
    private IPdUserAgreesService pdUserAgreesService;

    @ApiOperation(value="用户同意协议-分页列表查询", notes="用户同意协议-分页列表查询")
    @GetMapping(value = "/list")
    public Result<List<PdUserAgrees>> queryPageList() {
        return Result.OK(pdUserAgreesService.list());
    }

    @ApiOperation(value="链接类型(0-车险;1-财险;2-增值服务)-用户同意协议", notes="链接类型(0-车险;1-财险;2-增值服务)-用户同意协议")
    @GetMapping(value = "/queryByType")
    public Result<PdUserAgrees> queryById(@RequestParam(name="linkType",required=true) String linkType) {
        PdUserAgrees pdUserAgrees = pdUserAgreesService.lambdaQuery().eq(PdUserAgrees::getLinkType, linkType).last(" limit 1").one();
        if(pdUserAgrees==null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(pdUserAgrees);
    }
}
