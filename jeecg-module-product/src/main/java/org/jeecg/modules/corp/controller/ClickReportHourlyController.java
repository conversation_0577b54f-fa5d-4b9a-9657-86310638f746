package org.jeecg.modules.corp.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.corp.entity.ClickReportHourly;
import org.jeecg.modules.corp.service.IClickReportHourlyService;
import org.jeecg.modules.corp.vo.TodayClickCountVo;
import org.jeecg.modules.system.entity.SysUser;
import org.jeecg.modules.system.service.ISysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
* <p>
 * 按小时统计点击报表前端控制器
 * </p>
*
* <AUTHOR>
* @since 2025-05-12
*/
@Api(tags = "按小时统计点击报表")
@RestController
@Slf4j
@RequestMapping("click-report-hourly")
public class ClickReportHourlyController {
    @Autowired
    private IClickReportHourlyService iClickReportHourlyService;

    @Autowired
    private ISysUserService sysUserService;

//    @ApiOperation("新增按小时统计点击报表")
//    @PostMapping("/add")
//    public DataResult<ClickReportHourly> addClickReportHourly(@Valid @RequestBody ClickReportHourly dto) {
//        return SysHttpResult.SUCCESS.getDataResult(iClickReportHourlyService.add(dto));
//    }
//
//    @ApiOperation("修改按小时统计点击报表")
//    @PutMapping("/edit/{id}")
//    @ApiImplicitParam(value = "按小时统计点击报表id",name = "id",dataTypeClass = String.class)
//    public DataResult<ClickReportHourly> editClickReportHourly(@PathVariable String id, @Valid @RequestBody ClickReportHourly dto) {
//        dto.setId(id);
//        return SysHttpResult.SUCCESS.getDataResult(iClickReportHourlyService.edit(dto));
//    }
//
//
//    @ApiOperation("删除按小时统计点击报表")
//    @DeleteMapping("/delete/{id}")
//    @ApiImplicitParam(value = "按小时统计点击报表id",name = "id",dataTypeClass = String.class)
//    public DataResult deleteClickReportHourly(@PathVariable String id) {
//        iClickReportHourlyService.deleteById(id);
//        return SysHttpResult.SUCCESS.getDataResult();
//    }





    /**
     * 获取今日统计数据（点击数和预约总数）
     *
     * @param tenantId 租户ID（可选参数，如果不传则根据当前用户的bindTenant获取）
     * @return 今日统计数据结果
     */
    @ApiOperation("获取今日统计数据（点击数和预约总数）")
    @GetMapping("/getTodayClickCount")
    @ApiImplicitParam(name = "tenantId", value = "租户ID", dataType = "Integer", paramType = "query", required = false)
    public Result<TodayClickCountVo> getTodayClickCount(@RequestParam(value = "tenantId", required = false) Integer tenantId) {
        try {
            // 获取当前登录用户
            LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
            if (loginUser == null) {
                return Result.error("用户未登录");
            }

            // 确定要查询的租户ID
            Integer queryTenantId = tenantId;

            // 如果没有传入租户ID，则根据当前用户的bindTenant获取
            if (queryTenantId == null) {
                // 通过用户名获取完整的用户信息（包含bindTenant）
                SysUser sysUser = sysUserService.getUserByName(loginUser.getUsername());
                if (sysUser != null && oConvertUtils.isNotEmpty(sysUser.getBindTenant())) {
                    queryTenantId = Integer.valueOf(sysUser.getBindTenant());
                } else {
                    // 如果用户没有绑定租户，则设置为0（查询所有租户）
                    queryTenantId = 0;
                }
            }

            // 调用Service获取今日点击数
            Integer todayClickCount = iClickReportHourlyService.getTodayClickCount(queryTenantId);

            // 调用Service获取今日预约总数
            Integer todayReservationCount = iClickReportHourlyService.getTodayReservationCount(queryTenantId);

            // 构造返回结果
            TodayClickCountVo result = new TodayClickCountVo(todayClickCount, todayReservationCount, queryTenantId);

            log.info("获取今日统计成功，租户ID: {}, 点击数: {}, 预约总数: {}", queryTenantId, todayClickCount, todayReservationCount);

            return Result.ok(result);

        } catch (Exception e) {
            log.error("获取今日统计数据失败", e);
            return Result.error("获取今日统计数据失败: " + e.getMessage());
        }
    }

    /**
     * 根据租户ID添加点击数
     *
     * @param tenantId 租户ID
     * @return 操作结果
     */
    @ApiOperation("根据租户ID添加点击数")
    @PostMapping("/addClickByTenantId")
    @ApiImplicitParam(value = "租户ID", name = "tenantId", dataTypeClass = Integer.class, required = true)
    public Result<String> addClickByTenantId(@RequestParam Integer tenantId) {
        try {
            if (tenantId == null) {
                return Result.error("租户ID不能为空");
            }

            // 调用Service添加点击数
            iClickReportHourlyService.addClickByTenantId(tenantId);

            return Result.ok("点击数添加成功");

        } catch (Exception e) {
            log.error("添加点击数失败，租户ID: " + tenantId, e);
            return Result.error("添加点击数失败: " + e.getMessage());
        }
    }

}