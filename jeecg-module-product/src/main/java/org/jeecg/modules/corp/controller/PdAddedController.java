package org.jeecg.modules.corp.controller;

import java.util.*;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.corp.dto.LedgerListDto;
import org.jeecg.modules.corp.entity.PdAdded;
import org.jeecg.modules.corp.service.IPdAddedService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.modules.info.entity.PdCasualtyInfo;
import org.jeecg.modules.info.entity.PdGuestUsers;
import org.jeecg.modules.info.entity.PdGuestUsersRel;
import org.jeecg.modules.info.service.IPdGuestUsersRelService;
import org.jeecg.modules.info.service.IPdGuestUsersService;
import org.jeecg.modules.info.service.TenantFilter;
import org.jeecg.modules.wechat.entity.PdIntegrated;
import org.jeecg.modules.wechat.service.IPdIntegratedService;
import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: 增值服务预约记录
 * @Author: jeecg-boot
 * @Date:   2024-11-14
 * @Version: V1.0
 */
@Api(tags="增值服务预约记录")
@RestController
@RequestMapping("/corp/pdAdded")
@Slf4j
public class PdAddedController extends JeecgController<PdAdded, IPdAddedService> {
	@Autowired
	private IPdAddedService pdAddedService;
	 @Autowired
	 private IPdIntegratedService pdIntegratedService;
	 @Autowired
	 private IPdGuestUsersService pdGuestUsersService;
	 @Autowired
	 private IPdGuestUsersRelService guestUsersRelService;
	
	/**
	 * 分页列表查询
	 *
	 * @return
	 */
	//@AutoLog(value = "增值服务预约记录-分页列表查询")
	@ApiOperation(value="增值服务预约记录-分页列表查询", notes="增值服务预约记录-分页列表查询")
	@PostMapping(value = "/list/{pageNum}/{pageSize}")
	public Result<IPage<PdAdded>> queryPageList(@PathVariable(name = "pageNum")Long pageNum,
												@PathVariable(name = "pageSize")Long pageSize,
												@RequestBody LedgerListDto dto) {
		Page<PdAdded> page = new Page<>(pageNum, pageSize);
		IPage<PdAdded> pageList = pdAddedService.pageList(page,dto);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param pdAdded
	 * @return
	 */
	@AutoLog(value = "增值服务预约记录-添加")
	@ApiOperation(value="增值服务预约记录-添加", notes="增值服务预约记录-添加")
	@RequiresPermissions("corp:pd_added:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody PdAdded pdAdded) {
		pdAddedService.save(pdAdded);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param pdAdded
	 * @return
	 */
	@AutoLog(value = "增值服务预约记录-编辑")
	@ApiOperation(value="增值服务预约记录-编辑", notes="增值服务预约记录-编辑")
	@RequiresPermissions("corp:pd_added:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody PdAdded pdAdded) {
		pdAddedService.updateById(pdAdded);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "增值服务预约记录-通过id删除")
	@ApiOperation(value="增值服务预约记录-通过id删除", notes="增值服务预约记录-通过id删除")
	@RequiresPermissions("corp:pd_added:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		pdAddedService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "增值服务预约记录-批量删除")
	@ApiOperation(value="增值服务预约记录-批量删除", notes="增值服务预约记录-批量删除")
	@RequiresPermissions("corp:pd_added:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.pdAddedService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "增值服务预约记录-通过id查询")
	@ApiOperation(value="增值服务预约记录-通过id查询", notes="增值服务预约记录-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<PdAdded> queryById(@RequestParam(name="id",required=true) String id) {
		PdAdded pdAdded = pdAddedService.getById(id);
		if(pdAdded==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(pdAdded);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param pdAdded
    */
    @RequiresPermissions("corp:pd_added:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, PdAdded pdAdded) {
        return super.exportXls(request, pdAdded, PdAdded.class, "增值服务预约记录");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("corp:pd_added:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, PdAdded.class);
    }

}
