package org.jeecg.modules.corp.controller;

import java.io.IOException;
import java.util.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.apache.commons.lang.StringUtils;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.config.ProvinceIpGenerator;
import org.jeecg.modules.corp.dto.ChatDto;
import org.jeecg.modules.corp.dto.LedgerListDto;
import org.jeecg.modules.corp.entity.PdAdded;
import org.jeecg.modules.corp.entity.PdAddedLedger;
import org.jeecg.modules.corp.service.IPdAddedLedgerService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.modules.corp.service.IPdAddedService;
import org.jeecg.modules.corp.util.GenerateRecords;
import org.jeecg.modules.info.entity.PdGuestUsers;
import org.jeecg.modules.info.service.IPdGuestUsersService;
import org.jeecg.modules.info.service.TenantFilter;
import org.jeecg.modules.wechat.entity.PdIntegrated;
import org.jeecg.modules.wechat.service.IPdIntegratedService;
import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: 增值服务台账
 * @Author: jeecg-boot
 * @Date:   2024-11-25
 * @Version: V1.0
 */
@Api(tags="增值服务台账")
@RestController
@RequestMapping("/corp/pdAddedLedger")
@Slf4j
public class PdAddedLedgerController extends JeecgController<PdAddedLedger, IPdAddedLedgerService> {
	@Autowired
	private IPdAddedLedgerService pdAddedLedgerService;
	 @Autowired
	 private IPdIntegratedService pdIntegratedService;
	 @Autowired
	 private IPdGuestUsersService guestUsersService;
	 @Autowired
	 private GenerateRecords generateRecordUtil;
	 @Autowired
	 private IPdAddedService pdAddedService;
	
	/**
	 * 分页列表查询
	 *
	 * @return
	 */
	//@AutoLog(value = "增值服务台账-分页列表查询")
	@ApiOperation(value="增值服务台账-分页列表查询", notes="增值服务台账-分页列表查询")
	@PostMapping(value = "/list/{pageNum}/{pageSize}")
	public Result<IPage<PdAddedLedger>> queryPageList(@PathVariable(name = "pageNum")Long pageNum,
													  @PathVariable(name = "pageSize")Long pageSize,
													  @RequestBody LedgerListDto dto) {
		Page<PdAddedLedger> page = new Page<PdAddedLedger>(pageNum, pageSize);
		IPage<PdAddedLedger> pageList = pdAddedLedgerService.pageList(page, dto);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param pdAddedLedger
	 * @return
	 */
	@AutoLog(value = "增值服务台账-添加")
	@ApiOperation(value="增值服务台账-添加", notes="增值服务台账-添加")
	@RequiresPermissions("corp:pd_added_ledger:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody PdAddedLedger pdAddedLedger) {
		pdAddedLedgerService.save(pdAddedLedger);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param pdAddedLedger
	 * @return
	 */
	@AutoLog(value = "增值服务台账-编辑")
	@ApiOperation(value="增值服务台账-编辑", notes="增值服务台账-编辑")
	@RequiresPermissions("corp:pd_added_ledger:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody PdAddedLedger pdAddedLedger) {
		pdAddedLedgerService.updateById(pdAddedLedger);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "增值服务台账-通过id删除")
	@ApiOperation(value="增值服务台账-通过id删除", notes="增值服务台账-通过id删除")
	@RequiresPermissions("corp:pd_added_ledger:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		pdAddedLedgerService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "增值服务台账-批量删除")
	@ApiOperation(value="增值服务台账-批量删除", notes="增值服务台账-批量删除")
	@RequiresPermissions("corp:pd_added_ledger:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		pdAddedLedgerService.deleteBatch(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "增值服务台账-通过id查询")
	@ApiOperation(value="增值服务台账-通过id查询", notes="增值服务台账-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<PdAddedLedger> queryById(@RequestParam(name="id",required=true) String id) {
		PdAddedLedger pdAddedLedger = pdAddedLedgerService.getById(id);
		if(pdAddedLedger==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(pdAddedLedger);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param pdAddedLedger
    */
    @RequiresPermissions("corp:pd_added_ledger:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, PdAddedLedger pdAddedLedger) {
        return super.exportXls(request, pdAddedLedger, PdAddedLedger.class, "增值服务台账");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
	public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
		MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
		Map<String, MultipartFile> fileMap = multipartRequest.getFileMap();

		for (Map.Entry<String, MultipartFile> entity : fileMap.entrySet()) {
			MultipartFile file = entity.getValue();
			ImportParams params = new ImportParams();
			params.setTitleRows(2);
			params.setHeadRows(1);
			params.setNeedSave(true);

			try {
				// 从 Excel 文件中导入数据
				List<PdAddedLedger> list = ExcelImportUtil.importExcel(file.getInputStream(), PdAddedLedger.class, params);


				// 初始化保存列表和游客用户绑定关系
				List<PdAdded> saveList = new ArrayList<>();
				List<PdAddedLedger> saveListLedger = new ArrayList<>();


				// 遍历每条记录
				for (PdAddedLedger pdLedger : list) {
					// 生成手机号并设置到 pdLedger
					String maskedPhone = ProvinceIpGenerator.generateMaskedPhone();
					pdLedger.setPhone(maskedPhone).setOrderDate( new Date()).setIsVied(0);

					// 初始化 PdAdded 对象
					PdAdded pdAdded = new PdAdded();
					pdAdded.setPhone(maskedPhone).setName(pdLedger.getName()).setIsVied(0);

					// 随机生成 IP 地址
					String ipForProvince = ProvinceIpGenerator.getRandomIpForProvinceByRandom();

					// 查询 PdIntegrated
					LambdaQueryWrapper<PdIntegrated> queryWrapper = new LambdaQueryWrapper<>();
					queryWrapper.eq(PdIntegrated::getType, 2)
							.last(" ORDER BY RAND() LIMIT 1");
					PdIntegrated one = pdIntegratedService.getOne(queryWrapper);
					if (one == null) {
						log.error("未找到对应的服务：" + pdLedger.getUserItem());
						continue;
					}

					// 设置 pdAdded 属性
					pdAdded.setServe(one.getId()).setIpAddress(ipForProvince).setLinkType(1);
					// 添加到保存列表

					pdAddedLedgerService.save(pdLedger);
					pdAdded.setLedgerId(pdLedger.getId());
					saveList.add(pdAdded);
				}

				// 批量保存
				if (!saveList.isEmpty()) {
					pdAddedService.saveBatch(saveList);
				}
				return Result.ok("文件导入成功！数据行数：" + list.size());
			} catch (Exception e) {
				log.error("导入失败", e);
				String msg = e.getMessage();
				if (msg != null && msg.contains("Duplicate entry")) {
					return Result.error("文件导入失败: 存在重复数据！");
				}
				return Result.error("文件导入失败: " + e.getMessage());
			} finally {
				try {
					file.getInputStream().close();
				} catch (IOException e) {
					log.error("关闭文件流失败", e);
				}
			}
		}

		return Result.error("文件导入失败！");



	}

	 @Async
	 public void processChatRecordsInBatch(List<PdAddedLedger> ledgers,int totalRecords,int chatRecordThreshold,Map<String, PdGuestUsers> guestUserMap) {

		 Random random = new Random();

		 for (PdAddedLedger pdLedger : ledgers) {
			 // 按比例随机生成聊天记录
			 if (random.nextInt(totalRecords) < chatRecordThreshold) {
				 // 获取与导入记录绑定的游客用户
				 PdGuestUsers guestUser = guestUserMap.get(pdLedger.getUserItem());

				 if (guestUser != null) {
					 // 准备聊天记录参数
					 String ipForProvince = ProvinceIpGenerator.getRandomIpForProvinceByRandom();
					 try {
						 Thread.sleep(1000);
					 // 异步调用聊天记录生成方法
						 ChatDto chatDto = new ChatDto();
						 chatDto.setIp(ipForProvince)
								 .setUser(guestUser)
								 .setLinkType(2)
								 .setTenantId(pdLedger.getTenantId())
								 .setUserItem(pdLedger.getUserItem())
								 .setDate(null);
					 generateRecordUtil.processChatRecords(chatDto);

					 // 每次间隔 5 秒

					 } catch (InterruptedException e) {
						 Thread.currentThread().interrupt(); // 恢复中断状态
						 log.error("聊天记录生成任务被中断", e);
					 }
				 } else {
					 log.warn("未找到与记录绑定的游客用户: " + pdLedger.getUserItem());
				 }
			 }
		 }
	 }

}
