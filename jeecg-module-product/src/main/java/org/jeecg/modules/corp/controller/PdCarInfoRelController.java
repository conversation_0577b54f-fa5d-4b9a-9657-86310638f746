package org.jeecg.modules.corp.controller;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.corp.entity.PdCarInfoRel;
import org.jeecg.modules.corp.service.IPdCarInfoRelService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: 服务关系表
 * @Author: jeecg-boot
 * @Date:   2024-11-18
 * @Version: V1.0
 */
@Api(tags="服务关系表")
@RestController
@RequestMapping("/corp/pdCarInfoRel")
@Slf4j
public class PdCarInfoRelController extends JeecgController<PdCarInfoRel, IPdCarInfoRelService> {
	@Autowired
	private IPdCarInfoRelService pdCarInfoRelService;
	
	/**
	 * 分页列表查询
	 *
	 * @param pdCarInfoRel
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "服务关系表-分页列表查询")
	@ApiOperation(value="服务关系表-分页列表查询", notes="服务关系表-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<PdCarInfoRel>> queryPageList(PdCarInfoRel pdCarInfoRel,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<PdCarInfoRel> queryWrapper = QueryGenerator.initQueryWrapper(pdCarInfoRel, req.getParameterMap());
		Page<PdCarInfoRel> page = new Page<PdCarInfoRel>(pageNo, pageSize);
		IPage<PdCarInfoRel> pageList = pdCarInfoRelService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param pdCarInfoRel
	 * @return
	 */
	@AutoLog(value = "服务关系表-添加")
	@ApiOperation(value="服务关系表-添加", notes="服务关系表-添加")
	@RequiresPermissions("corp:pd_car_info_rel:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody PdCarInfoRel pdCarInfoRel) {
		pdCarInfoRelService.save(pdCarInfoRel);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param pdCarInfoRel
	 * @return
	 */
	@AutoLog(value = "服务关系表-编辑")
	@ApiOperation(value="服务关系表-编辑", notes="服务关系表-编辑")
	@RequiresPermissions("corp:pd_car_info_rel:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody PdCarInfoRel pdCarInfoRel) {
		pdCarInfoRelService.updateById(pdCarInfoRel);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "服务关系表-通过id删除")
	@ApiOperation(value="服务关系表-通过id删除", notes="服务关系表-通过id删除")
	@RequiresPermissions("corp:pd_car_info_rel:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		pdCarInfoRelService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "服务关系表-批量删除")
	@ApiOperation(value="服务关系表-批量删除", notes="服务关系表-批量删除")
	@RequiresPermissions("corp:pd_car_info_rel:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.pdCarInfoRelService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "服务关系表-通过id查询")
	@ApiOperation(value="服务关系表-通过id查询", notes="服务关系表-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<PdCarInfoRel> queryById(@RequestParam(name="id",required=true) String id) {
		PdCarInfoRel pdCarInfoRel = pdCarInfoRelService.getById(id);
		if(pdCarInfoRel==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(pdCarInfoRel);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param pdCarInfoRel
    */
    @RequiresPermissions("corp:pd_car_info_rel:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, PdCarInfoRel pdCarInfoRel) {
        return super.exportXls(request, pdCarInfoRel, PdCarInfoRel.class, "服务关系表");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("corp:pd_car_info_rel:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, PdCarInfoRel.class);
    }

}
