package org.jeecg.modules.corp.controller;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.corp.entity.PdCompany;
import org.jeecg.modules.corp.service.IPdCompanyService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.modules.info.service.TenantFilter;
import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: app 合作公司
 * @Author: jeecg-boot
 * @Date:   2024-11-06
 * @Version: V1.0
 */
@Api(tags="app 合作公司")
@RestController
@RequestMapping("/corp/pdCompany")
@Slf4j
public class PdCompanyController extends JeecgController<PdCompany, IPdCompanyService> {
	@Autowired
	private IPdCompanyService pdCompanyService;


	 /**
	  * 分页列表查询
	  *
	  * @return
	  */
	 //@AutoLog(value = "app 合作公司-分页列表查询")
	 @ApiOperation(value="app 合作公司", notes="app 合作公司")
	 @GetMapping(value = "api/list")
	 public Result<List<PdCompany>> queryPageList() {
		 QueryWrapper<PdCompany> queryWrapper = new QueryWrapper<>();
		 queryWrapper.eq("enable_type", "Y");
		 queryWrapper.eq("is_delete", 0);
		 return Result.OK(pdCompanyService.list());
	 }
	
	/**
	 * 分页列表查询
	 *
	 * @param pageNo
	 * @param pageSize
	 * @return
	 */
	//@AutoLog(value = "app 合作公司-分页列表查询")
	@ApiOperation(value="app 合作公司-分页列表查询", notes="app 合作公司-分页列表查询")
	@GetMapping(value = "/list")
	@TenantFilter
	public Result<IPage<PdCompany>> queryPageList(
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize) {

		QueryWrapper<PdCompany> queryWrapper = new QueryWrapper<>();
		queryWrapper.eq("is_delete", 0);
		LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
		queryWrapper.in("tenant_id", Arrays.asList(sysUser.getRelTenantIds().split(",")));
		Page<PdCompany> page = new Page<PdCompany>(pageNo, pageSize);
		IPage<PdCompany> pageList = pdCompanyService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param pdCompany
	 * @return
	 */
	@AutoLog(value = "app 合作公司-添加")
	@ApiOperation(value="app 合作公司-添加", notes="app 合作公司-添加")
	@RequiresPermissions("corp:pd_company:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody PdCompany pdCompany) {
		pdCompanyService.save(pdCompany);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param pdCompany
	 * @return
	 */
	@AutoLog(value = "app 合作公司-编辑")
	@ApiOperation(value="app 合作公司-编辑", notes="app 合作公司-编辑")
	@RequiresPermissions("corp:pd_company:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody PdCompany pdCompany) {
		pdCompanyService.updateById(pdCompany);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "app 合作公司-通过id删除")
	@ApiOperation(value="app 合作公司-通过id删除", notes="app 合作公司-通过id删除")
	@RequiresPermissions("corp:pd_company:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		pdCompanyService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "app 合作公司-批量删除")
	@ApiOperation(value="app 合作公司-批量删除", notes="app 合作公司-批量删除")
	@RequiresPermissions("corp:pd_company:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.pdCompanyService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "app 合作公司-通过id查询")
	@ApiOperation(value="app 合作公司-通过id查询", notes="app 合作公司-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<PdCompany> queryById(@RequestParam(name="id",required=true) String id) {
		PdCompany pdCompany = pdCompanyService.getById(id);
		if(pdCompany==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(pdCompany);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param pdCompany
    */
    @RequiresPermissions("corp:pd_company:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, PdCompany pdCompany) {
        return super.exportXls(request, pdCompany, PdCompany.class, "app 合作公司");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("corp:pd_company:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, PdCompany.class);
    }

}
