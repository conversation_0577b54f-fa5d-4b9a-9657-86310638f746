package org.jeecg.modules.corp.controller;

import java.util.Arrays;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.modules.corp.entity.PdCompanyInfo;
import org.jeecg.modules.corp.service.IPdCompanyInfoService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.modules.info.service.TenantFilter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: 公司商务信息
 * @Author: jeecg-boot
 * @Date:   2024-11-06
 * @Version: V1.0
 */
@Api(tags="公司商务信息")
@RestController
@RequestMapping("/corp/pdCompanyInfo")
@Slf4j
public class PdCompanyInfoController extends JeecgController<PdCompanyInfo, IPdCompanyInfoService> {
	@Autowired
	private IPdCompanyInfoService pdCompanyInfoService;
	
	/**
	 * 分页列表查询
	 *
	 * @param pdCompanyInfo
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "公司商务信息-分页列表查询")
	@ApiOperation(value="公司商务信息-分页列表查询", notes="公司商务信息-分页列表查询")
	@GetMapping(value = "/list")
	@TenantFilter
	public Result<IPage<PdCompanyInfo>> queryPageList(PdCompanyInfo pdCompanyInfo,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<PdCompanyInfo> queryWrapper = QueryGenerator.initQueryWrapper(pdCompanyInfo, req.getParameterMap());
		LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
		queryWrapper.in("tenant_id", Arrays.asList(sysUser.getRelTenantIds().split(",")));
		Page<PdCompanyInfo> page = new Page<PdCompanyInfo>(pageNo, pageSize);
		IPage<PdCompanyInfo> pageList = pdCompanyInfoService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param pdCompanyInfo
	 * @return
	 */
	@AutoLog(value = "公司商务信息-添加")
	@ApiOperation(value="公司商务信息-添加", notes="公司商务信息-添加")
	@RequiresPermissions("corp:pd_company_info:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody PdCompanyInfo pdCompanyInfo) {
		pdCompanyInfoService.save(pdCompanyInfo);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param pdCompanyInfo
	 * @return
	 */
	@AutoLog(value = "公司商务信息-编辑")
	@ApiOperation(value="公司商务信息-编辑", notes="公司商务信息-编辑")
	@RequiresPermissions("corp:pd_company_info:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody PdCompanyInfo pdCompanyInfo) {
		pdCompanyInfoService.updateById(pdCompanyInfo);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "公司商务信息-通过id删除")
	@ApiOperation(value="公司商务信息-通过id删除", notes="公司商务信息-通过id删除")
	@RequiresPermissions("corp:pd_company_info:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		pdCompanyInfoService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "公司商务信息-批量删除")
	@ApiOperation(value="公司商务信息-批量删除", notes="公司商务信息-批量删除")
	@RequiresPermissions("corp:pd_company_info:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.pdCompanyInfoService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "公司商务信息-通过id查询")
	@ApiOperation(value="公司商务信息-通过id查询", notes="公司商务信息-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<PdCompanyInfo> queryById(@RequestParam(name="id",required=true) String id) {
		PdCompanyInfo pdCompanyInfo = pdCompanyInfoService.getById(id);
		if(pdCompanyInfo==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(pdCompanyInfo);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param pdCompanyInfo
    */
    @RequiresPermissions("corp:pd_company_info:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, PdCompanyInfo pdCompanyInfo) {
        return super.exportXls(request, pdCompanyInfo, PdCompanyInfo.class, "公司商务信息");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("corp:pd_company_info:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, PdCompanyInfo.class);
    }

}
