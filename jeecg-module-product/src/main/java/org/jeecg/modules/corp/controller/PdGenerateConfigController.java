package org.jeecg.modules.corp.controller;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.corp.entity.PdGenerateConfig;
import org.jeecg.modules.corp.service.IPdGenerateConfigService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.modules.info.service.TenantFilter;
import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: 生成规则配置
 * @Author: jeecg-boot
 * @Date:   2024-11-14
 * @Version: V1.0
 */
@Api(tags="生成规则配置")
@RestController
@RequestMapping("/corp/pdGenerateConfig")
@Slf4j
public class PdGenerateConfigController extends JeecgController<PdGenerateConfig, IPdGenerateConfigService> {
	@Autowired
	private IPdGenerateConfigService pdGenerateConfigService;
	
	/**
	 * 分页列表查询
	 *
	 * @param pdGenerateConfig
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "生成规则配置-分页列表查询")
	@ApiOperation(value="生成规则配置-分页列表查询", notes="生成规则配置-分页列表查询")
	@GetMapping(value = "/list")
	@TenantFilter
	public Result<IPage<PdGenerateConfig>> queryPageList(PdGenerateConfig pdGenerateConfig,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<PdGenerateConfig> queryWrapper = QueryGenerator.initQueryWrapper(pdGenerateConfig, req.getParameterMap());
		LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
		queryWrapper.in("tenant_id", Arrays.asList(sysUser.getRelTenantIds().split(",")));
		Page<PdGenerateConfig> page = new Page<PdGenerateConfig>(pageNo, pageSize);
		IPage<PdGenerateConfig> pageList = pdGenerateConfigService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param pdGenerateConfig
	 * @return
	 */
	@AutoLog(value = "生成规则配置-添加")
	@ApiOperation(value="生成规则配置-添加", notes="生成规则配置-添加")
	@RequiresPermissions("corp:pd_generate_config:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody PdGenerateConfig pdGenerateConfig) {
		pdGenerateConfigService.save(pdGenerateConfig);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param pdGenerateConfig
	 * @return
	 */
	@AutoLog(value = "生成规则配置-编辑")
	@ApiOperation(value="生成规则配置-编辑", notes="生成规则配置-编辑")
	@RequiresPermissions("corp:pd_generate_config:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody PdGenerateConfig pdGenerateConfig) {
		pdGenerateConfigService.updateById(pdGenerateConfig);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "生成规则配置-通过id删除")
	@ApiOperation(value="生成规则配置-通过id删除", notes="生成规则配置-通过id删除")
	@RequiresPermissions("corp:pd_generate_config:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		pdGenerateConfigService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "生成规则配置-批量删除")
	@ApiOperation(value="生成规则配置-批量删除", notes="生成规则配置-批量删除")
	@RequiresPermissions("corp:pd_generate_config:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.pdGenerateConfigService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "生成规则配置-通过id查询")
	@ApiOperation(value="生成规则配置-通过id查询", notes="生成规则配置-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<PdGenerateConfig> queryById(@RequestParam(name="id",required=true) String id) {
		PdGenerateConfig pdGenerateConfig = pdGenerateConfigService.getById(id);
		if(pdGenerateConfig==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(pdGenerateConfig);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param pdGenerateConfig
    */
    @RequiresPermissions("corp:pd_generate_config:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, PdGenerateConfig pdGenerateConfig) {
        return super.exportXls(request, pdGenerateConfig, PdGenerateConfig.class, "生成规则配置");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("corp:pd_generate_config:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, PdGenerateConfig.class);
    }

}
