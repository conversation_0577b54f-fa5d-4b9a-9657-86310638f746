package org.jeecg.modules.corp.controller;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.corp.entity.PdLinkInfo;
import org.jeecg.modules.corp.service.IPdLinkInfoService;
import org.jeecg.modules.corp.service.ISysCityPlatePrefixService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.modules.info.service.TenantFilter;
import org.jeecg.modules.wechat.service.IEaRegionService;
import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: 链接对应租户关系
 * @Author: jeecg-boot
 * @Date:   2024-11-12
 * @Version: V1.0
 */
@Api(tags="链接对应租户关系")
@RestController
@RequestMapping("/corp/pdLinkInfo")
@Slf4j
public class PdLinkInfoController extends JeecgController<PdLinkInfo, IPdLinkInfoService> {
	@Autowired
	private IPdLinkInfoService pdLinkInfoService;
	 @Autowired
	 private IEaRegionService eaRegionService;

	@Autowired
	private ISysCityPlatePrefixService sysCityPlatePrefixService;

	/**
	 * 分页列表查询
	 *
	 * @param pdLinkInfo
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "链接对应租户关系-分页列表查询")
	@ApiOperation(value="链接对应租户关系-分页列表查询", notes="链接对应租户关系-分页列表查询")
	@GetMapping(value = "/list")
	@TenantFilter
	public Result<IPage<PdLinkInfo>> queryPageList(PdLinkInfo pdLinkInfo,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<PdLinkInfo> queryWrapper = QueryGenerator.initQueryWrapper(pdLinkInfo, req.getParameterMap());
		LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
		queryWrapper.in("tenant_id", Arrays.asList(sysUser.getRelTenantIds().split(",")));
		Page<PdLinkInfo> page = new Page<PdLinkInfo>(pageNo, pageSize);
		IPage<PdLinkInfo> pageList = pdLinkInfoService.page(page, queryWrapper);
		return Result.OK(pageList);
	}


	/**
	 *   添加
	 *
	 * @param pdLinkInfo
	 * @return
	 */
	@AutoLog(value = "链接对应租户关系-添加")
	@ApiOperation(value="链接对应租户关系-添加", notes="链接对应租户关系-添加")
	@RequiresPermissions("corp:pd_link_info:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody PdLinkInfo pdLinkInfo) {
		pdLinkInfoService.save(pdLinkInfo);
		return Result.OK("添加成功！");
	}

	/**
	 *  编辑
	 *
	 * @param pdLinkInfo
	 * @return
	 */
	@AutoLog(value = "链接对应租户关系-编辑")
	@ApiOperation(value="链接对应租户关系-编辑", notes="链接对应租户关系-编辑")
	@RequiresPermissions("corp:pd_link_info:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody PdLinkInfo pdLinkInfo) {
		pdLinkInfoService.updateById(pdLinkInfo);
		return Result.OK("编辑成功!");
	}

	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "链接对应租户关系-通过id删除")
	@ApiOperation(value="链接对应租户关系-通过id删除", notes="链接对应租户关系-通过id删除")
	@RequiresPermissions("corp:pd_link_info:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		pdLinkInfoService.removeById(id);
		return Result.OK("删除成功!");
	}

	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "链接对应租户关系-批量删除")
	@ApiOperation(value="链接对应租户关系-批量删除", notes="链接对应租户关系-批量删除")
	@RequiresPermissions("corp:pd_link_info:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.pdLinkInfoService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}

	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "链接对应租户关系-通过id查询")
	@ApiOperation(value="链接对应租户关系-通过id查询", notes="链接对应租户关系-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<PdLinkInfo> queryById(@RequestParam(name="id",required=true) String id) {
		PdLinkInfo pdLinkInfo = pdLinkInfoService.getById(id);
		if(pdLinkInfo==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(pdLinkInfo);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param pdLinkInfo
    */
    @RequiresPermissions("corp:pd_link_info:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, PdLinkInfo pdLinkInfo) {
        return super.exportXls(request, pdLinkInfo, PdLinkInfo.class, "链接对应租户关系");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("corp:pd_link_info:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, PdLinkInfo.class);
    }

    /**
     * 初始化城市车牌前缀数据
     *
     * @return 初始化结果
     */
    @AutoLog(value = "初始化城市车牌前缀数据")
    @ApiOperation(value="初始化城市车牌前缀数据", notes="初始化城市车牌前缀数据")
    @GetMapping(value = "/initCityPlatePrefixData")
    public Result<String> initCityPlatePrefixData() {

        boolean result = eaRegionService.initCityPlatePrefixData();
        if (result) {
            return Result.OK("初始化城市车牌前缀数据成功！");
        } else {
            return Result.error("初始化城市车牌前缀数据失败！");
        }
    }

}
