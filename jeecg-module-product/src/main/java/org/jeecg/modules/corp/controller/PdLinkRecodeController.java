package org.jeecg.modules.corp.controller;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.alibaba.fastjson.JSONObject;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.corp.dto.PdLinkRecodeDto;
import org.jeecg.modules.corp.entity.PdLinkRecode;
import org.jeecg.modules.corp.service.IPdLinkRecodeService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.modules.corp.vo.PdLinkRecodeVo;
import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: 链接快照
 * @Author: jeecg-boot
 * @Date:   2024-11-12
 * @Version: V1.0
 */
@Api(tags="链接快照")
@RestController
@RequestMapping("/corp/pdLinkRecode")
@Slf4j
public class PdLinkRecodeController extends JeecgController<PdLinkRecode, IPdLinkRecodeService> {
	@Autowired
	private IPdLinkRecodeService pdLinkRecodeService;
	
	/**
	 * 分页列表查询
	 *
	 * @param pdLinkRecode
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "链接快照-分页列表查询")
	@ApiOperation(value="链接快照-分页列表查询", notes="链接快照-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<PdLinkRecode>> queryPageList(PdLinkRecode pdLinkRecode,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<PdLinkRecode> queryWrapper = QueryGenerator.initQueryWrapper(pdLinkRecode, req.getParameterMap());
		Page<PdLinkRecode> page = new Page<PdLinkRecode>(pageNo, pageSize);
		IPage<PdLinkRecode> pageList = pdLinkRecodeService.page(page, queryWrapper);
		return Result.OK(pageList);
	}

	 @ApiOperation(value="链接快照-报表查询", notes="链接快照-报表查询")
	 @PostMapping(value = "/list/recode")
	 public Result<JSONObject> listRecode(@RequestBody PdLinkRecodeDto dto) {
		 JSONObject pageList = pdLinkRecodeService.listRecode(dto);
		 return Result.OK(pageList);
	 }
	
	/**
	 *   添加
	 *
	 * @param pdLinkRecode
	 * @return
	 */
	@AutoLog(value = "链接快照-添加")
	@ApiOperation(value="链接快照-添加", notes="链接快照-添加")
	@RequiresPermissions("corp:pd_link_recode:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody PdLinkRecode pdLinkRecode) {
		pdLinkRecodeService.save(pdLinkRecode);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param pdLinkRecode
	 * @return
	 */
	@AutoLog(value = "链接快照-编辑")
	@ApiOperation(value="链接快照-编辑", notes="链接快照-编辑")
	@RequiresPermissions("corp:pd_link_recode:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody PdLinkRecode pdLinkRecode) {
		pdLinkRecodeService.updateById(pdLinkRecode);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "链接快照-通过id删除")
	@ApiOperation(value="链接快照-通过id删除", notes="链接快照-通过id删除")
	@RequiresPermissions("corp:pd_link_recode:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		pdLinkRecodeService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "链接快照-批量删除")
	@ApiOperation(value="链接快照-批量删除", notes="链接快照-批量删除")
	@RequiresPermissions("corp:pd_link_recode:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.pdLinkRecodeService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "链接快照-通过id查询")
	@ApiOperation(value="链接快照-通过id查询", notes="链接快照-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<PdLinkRecode> queryById(@RequestParam(name="id",required=true) String id) {
		PdLinkRecode pdLinkRecode = pdLinkRecodeService.getById(id);
		if(pdLinkRecode==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(pdLinkRecode);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param pdLinkRecode
    */
    @RequiresPermissions("corp:pd_link_recode:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, PdLinkRecode pdLinkRecode) {
        return super.exportXls(request, pdLinkRecode, PdLinkRecode.class, "链接快照");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("corp:pd_link_recode:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, PdLinkRecode.class);
    }

}
