package org.jeecg.modules.corp.controller;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.corp.entity.PdUserAgrees;
import org.jeecg.modules.corp.service.IPdUserAgreesService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.modules.info.service.TenantFilter;
import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: 用户同意协议
 * @Author: jeecg-boot
 * @Date:   2024-11-14
 * @Version: V1.0
 */
@Api(tags="用户同意协议")
@RestController
@RequestMapping("/corp/pdUserAgrees")
@Slf4j
public class PdUserAgreesController extends JeecgController<PdUserAgrees, IPdUserAgreesService> {
	@Autowired
	private IPdUserAgreesService pdUserAgreesService;
	
	/**
	 * 分页列表查询
	 *
	 * @param pdUserAgrees
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "用户同意协议-分页列表查询")
	@ApiOperation(value="用户同意协议-分页列表查询", notes="用户同意协议-分页列表查询")
	@GetMapping(value = "/list")
	@TenantFilter
	public Result<IPage<PdUserAgrees>> queryPageList(PdUserAgrees pdUserAgrees,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<PdUserAgrees> queryWrapper = QueryGenerator.initQueryWrapper(pdUserAgrees, req.getParameterMap());
		LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
		queryWrapper.in("tenant_id", Arrays.asList(sysUser.getRelTenantIds().split(",")));
		Page<PdUserAgrees> page = new Page<PdUserAgrees>(pageNo, pageSize);
		IPage<PdUserAgrees> pageList = pdUserAgreesService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param pdUserAgrees
	 * @return
	 */
	@AutoLog(value = "用户同意协议-添加")
	@ApiOperation(value="用户同意协议-添加", notes="用户同意协议-添加")
	@RequiresPermissions("corp:pd_user_agrees:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody PdUserAgrees pdUserAgrees) {
		pdUserAgreesService.save(pdUserAgrees);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param pdUserAgrees
	 * @return
	 */
	@AutoLog(value = "用户同意协议-编辑")
	@ApiOperation(value="用户同意协议-编辑", notes="用户同意协议-编辑")
	@RequiresPermissions("corp:pd_user_agrees:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody PdUserAgrees pdUserAgrees) {
		pdUserAgreesService.updateById(pdUserAgrees);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "用户同意协议-通过id删除")
	@ApiOperation(value="用户同意协议-通过id删除", notes="用户同意协议-通过id删除")
	@RequiresPermissions("corp:pd_user_agrees:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		pdUserAgreesService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "用户同意协议-批量删除")
	@ApiOperation(value="用户同意协议-批量删除", notes="用户同意协议-批量删除")
	@RequiresPermissions("corp:pd_user_agrees:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.pdUserAgreesService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "用户同意协议-通过id查询")
	@ApiOperation(value="用户同意协议-通过id查询", notes="用户同意协议-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<PdUserAgrees> queryById(@RequestParam(name="id",required=true) String id) {
		PdUserAgrees pdUserAgrees = pdUserAgreesService.getById(id);
		if(pdUserAgrees==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(pdUserAgrees);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param pdUserAgrees
    */
    @RequiresPermissions("corp:pd_user_agrees:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, PdUserAgrees pdUserAgrees) {
        return super.exportXls(request, pdUserAgrees, PdUserAgrees.class, "用户同意协议");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("corp:pd_user_agrees:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, PdUserAgrees.class);
    }

}
