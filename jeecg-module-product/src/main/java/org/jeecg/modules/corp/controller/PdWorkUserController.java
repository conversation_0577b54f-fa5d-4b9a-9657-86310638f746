package org.jeecg.modules.corp.controller;

import java.util.Arrays;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.modules.corp.entity.PdWorkUser;
import org.jeecg.modules.corp.service.IPdWorkUserService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.modules.info.service.TenantFilter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: 内勤人员管理
 * @Author: jeecg-boot
 * @Date:   2024-11-06
 * @Version: V1.0
 */
@Api(tags="内勤人员管理")
@RestController
@RequestMapping("/corp/pdWorkUser")
@Slf4j
public class PdWorkUserController extends JeecgController<PdWorkUser, IPdWorkUserService> {
	@Autowired
	private IPdWorkUserService pdWorkUserService;
	
	/**
	 * 分页列表查询
	 *
	 * @param pdWorkUser
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "内勤人员管理-分页列表查询")
	@ApiOperation(value="内勤人员管理-分页列表查询", notes="内勤人员管理-分页列表查询")
	@GetMapping(value = "/list")
	@TenantFilter
	public Result<IPage<PdWorkUser>> queryPageList(PdWorkUser pdWorkUser,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<PdWorkUser> queryWrapper = QueryGenerator.initQueryWrapper(pdWorkUser, req.getParameterMap());
		LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
		queryWrapper.in("tenant_id", Arrays.asList(sysUser.getRelTenantIds().split(",")));
		Page<PdWorkUser> page = new Page<PdWorkUser>(pageNo, pageSize);
		IPage<PdWorkUser> pageList = pdWorkUserService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param pdWorkUser
	 * @return
	 */
	@AutoLog(value = "内勤人员管理-添加")
	@ApiOperation(value="内勤人员管理-添加", notes="内勤人员管理-添加")
	@RequiresPermissions("corp:pd_work_user:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody PdWorkUser pdWorkUser) {
		pdWorkUserService.save(pdWorkUser);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param pdWorkUser
	 * @return
	 */
	@AutoLog(value = "内勤人员管理-编辑")
	@ApiOperation(value="内勤人员管理-编辑", notes="内勤人员管理-编辑")
	@RequiresPermissions("corp:pd_work_user:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody PdWorkUser pdWorkUser) {
		pdWorkUserService.updateById(pdWorkUser);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "内勤人员管理-通过id删除")
	@ApiOperation(value="内勤人员管理-通过id删除", notes="内勤人员管理-通过id删除")
	@RequiresPermissions("corp:pd_work_user:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		pdWorkUserService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "内勤人员管理-批量删除")
	@ApiOperation(value="内勤人员管理-批量删除", notes="内勤人员管理-批量删除")
	@RequiresPermissions("corp:pd_work_user:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.pdWorkUserService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "内勤人员管理-通过id查询")
	@ApiOperation(value="内勤人员管理-通过id查询", notes="内勤人员管理-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<PdWorkUser> queryById(@RequestParam(name="id",required=true) String id) {
		PdWorkUser pdWorkUser = pdWorkUserService.getById(id);
		if(pdWorkUser==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(pdWorkUser);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param pdWorkUser
    */
    @RequiresPermissions("corp:pd_work_user:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, PdWorkUser pdWorkUser) {
        return super.exportXls(request, pdWorkUser, PdWorkUser.class, "内勤人员管理");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("corp:pd_work_user:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, PdWorkUser.class);
    }

}
