package org.jeecg.modules.corp.dto;

import lombok.Data;
import lombok.experimental.Accessors;
import org.jeecg.modules.info.entity.PdGuestUsers;

import java.io.Serializable;
import java.util.Date;

@Data
@Accessors(chain = true)
public class ChatDto implements Serializable {
    private String ip;
    private String kid;
    private PdGuestUsers user;
    private Integer linkType;
    private Integer tenantId;
    private String userItem;
    private Date date;
}
