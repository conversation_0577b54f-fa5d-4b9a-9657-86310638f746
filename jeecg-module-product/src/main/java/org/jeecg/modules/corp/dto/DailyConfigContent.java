package org.jeecg.modules.corp.dto;
import java.util.List;
import lombok.Data;
import io.swagger.annotations.ApiModelProperty;

@Data
public class DailyConfigContent {

    @ApiModelProperty("财险台账区间")
    private Range financeLedger;

    @ApiModelProperty("增值服务台账区间")
    private Range valueAddedLedger;

    @ApiModelProperty("车险台账区间")
    private Range carLedger;

    @ApiModelProperty("城市列表")
    private List<String> cityList;  // 修改为 List<String>，存储城市编码

    /**
     * 获取随机城市
     */
    public String getRandomCity() {
        if (cityList == null || cityList.isEmpty()) return null;
        return cityList.get(new java.util.Random().nextInt(cityList.size()));  // 从城市列表中随机选择一个城市编码
    }

    @Data
    public static class Range {
        @ApiModelProperty("台账区间开始")
        private Double ledgerStart;

        @ApiModelProperty("台账区间结束")
        private Double ledgerEnd;

        @ApiModelProperty("聊天用户区间开始")
        private Double chatUserStart;

        @ApiModelProperty("聊天用户区间结束")
        private Double chatUserEnd;
    }
}
