package org.jeecg.modules.corp.dto;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.sql.Timestamp;
import java.time.LocalDate;

/**
* 租户配置
*
* <AUTHOR>
* @since 2025-04-12
*/
@Getter
@Setter
@Accessors(chain = true)
public class DailyConfigDto implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("每日配置 JSON 内容（包含：台账区间、聊天用户区间、城市范围）")
    private String configJson;

    @ApiModelProperty("点击数")
    private Integer clickNum;

    @ApiModelProperty(value = "链接类型(0-车险;1-财险;2-增值服务)")
    private Integer linkType;

    @ApiModelProperty("租户id")
    private Integer tenantId;

    @ApiModelProperty("日期开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate monthDataStart;
    @ApiModelProperty("日期结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate monthDataEnd;

}

