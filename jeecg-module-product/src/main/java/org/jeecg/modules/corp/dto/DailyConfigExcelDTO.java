package org.jeecg.modules.corp.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import org.jeecgframework.poi.excel.annotation.Excel;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;

/**
* 每日租户配置表
*
* <AUTHOR>
* @since 2025-04-12
*/
@Data
public class DailyConfigExcelDTO {

    @Excel(name = "每日点击数开始", width = 15)
    private Integer dailyClickStart;

    @Excel(name = "每日点击数结束", width = 15)
    private Integer dailyClickEnd;


    //add start
    @Excel(name = "日期开始时间", width = 15)
    private String monthDataStartStr;

    @Excel(name = "日期结束时间", width = 15)
    private String monthDataEndStr;

    @Excel(name = "点击数", width = 15)
    private Integer clickNum;
    //add end

    // 日期转换方法
    public LocalDate getMonthDataStart() {
        return parseDate(monthDataStartStr);
    }

    public LocalDate getMonthDataEnd() {
        return parseDate(monthDataEndStr);
    }

    // 解析多种格式的日期字符串
    private LocalDate parseDate(String dateStr) {
        if (dateStr == null || dateStr.trim().isEmpty()) {
            return LocalDate.now(); // 默认返回当前日期
        }

        // 首先尝试解析Excel数字格式的日期（从1900年1月1日起的天数）
        if (dateStr.matches("\\d+")) {
            try {
                int days = Integer.parseInt(dateStr);
                // Excel的日期系统有一个特殊情况：Excel错误地认为1900年是闰年
                // 如果天数大于59（1900年2月28日之后），需要减去1天进行修正
                if (days > 59) {
                    days -= 1;
                }
                // 从1900年1月1日开始计算
                return LocalDate.of(1900, 1, 1).plusDays(days - 1);
            } catch (NumberFormatException e) {
                // 如果不是有效的数字，继续尝试其他格式
            }
        }

        // 尝试不同的日期格式
        String[] patterns = {
            "yyyy-MM-dd", "yyyy/MM/dd",
            "yyyy-M-d", "yyyy/M/d",
            "yyyy年MM月dd日", "yyyy年M月d日"
        };

        for (String pattern : patterns) {
            try {
                return LocalDate.parse(dateStr, DateTimeFormatter.ofPattern(pattern));
            } catch (DateTimeParseException e) {
                // 继续尝试下一个格式
            }
        }

        // 如果所有格式都失败，返回当前日期并记录错误
        System.err.println("无法解析日期: " + dateStr + "，使用当前日期代替");
        return LocalDate.now();
    }

    @Excel(name = "车险台账(开关)", width = 15)
    private Integer carInsuranceSwitch;

    @Excel(name = "车险台账占比开始", width = 15)
    private Double carInsuranceStart;

    @Excel(name = "车险台账占比结束", width = 15)
    private Double carInsuranceEnd;

    @Excel(name = "聊天占比开始", width = 15)
    private Double chatStart;

    @Excel(name = "聊天占比结束", width = 15)
    private Double chatEnd;

    @Excel(name = "财险台账(开关)", width = 15)
    private Integer propertyInsuranceSwitch;

    @Excel(name = "财险台账占比开始", width = 15)
    private Double propertyInsuranceStart;

    @Excel(name = "财险台账占比结束", width = 15)
    private Double propertyInsuranceEnd;

    @Excel(name = "财险聊天占比开始", width = 15)
    private Double propertyChatStart;

    @Excel(name = "财险聊天占比结束", width = 15)
    private Double propertyChatEnd;

    @Excel(name = "增值服务台账(开关)", width = 15)
    private Integer valueServiceSwitch;

    @Excel(name = "增值服务台账占比开始", width = 15)
    private Double valueServiceStart;

    @Excel(name = "增值服务台账占比结束", width = 15)
    private Double valueServiceEnd;

    @Excel(name = "增值服务聊天占比开始", width = 15)
    private Double valueChatStart;

    @Excel(name = "增值服务聊天占比结束", width = 15)
    private Double valueChatEnd;

    @Excel(name = "城市", width = 15)
    private String cities;

    @Excel(name = "租户", width = 15)
    private Integer tenantId;
}
