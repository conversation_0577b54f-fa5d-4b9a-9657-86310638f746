package org.jeecg.modules.corp.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;
import java.util.List;

/**
 * @Description: 按类型删除数据DTO
 * @Author: jeecg-boot
 * @Date: 2024-11-08
 * @Version: V1.0
 */
@Data
@ApiModel(value="DeleteByTypeDTO", description="按类型删除数据参数")
public class DeleteByTypeDTO {

    @ApiModelProperty(value = "删除类型：0-车险，1-财险，2-增值服务", required = true)
    private Integer type;

    @ApiModelProperty(value = "租户ID列表", required = true)
    private List<String> tenantIds;

    @ApiModelProperty(value = "开始日期", required = true)
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate startDate;

    @ApiModelProperty(value = "结束日期", required = true)
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate endDate;
}
