package org.jeecg.modules.corp.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.List;

@Data
@Accessors(chain = true)
public class LedgerListDto implements Serializable {
    private static final long serialVersionUID = 1L;


    /**创建人*/
    @ApiModelProperty(value = "聊天:0未生成,1生成中,2已生成")
    private java.lang.Integer chatStatus;
    /**创建日期*/
    @ApiModelProperty(value = "查询日期")
    private java.lang.String dateRange;
    @ApiModelProperty(value = "查询关键字keyword")
    private java.lang.String keyword;
    @ApiModelProperty(value = "名称")
    private java.lang.String name;
    @ApiModelProperty(value = "开始时间")
    private java.lang.String startDate;
    @ApiModelProperty(value = "结束时间")
    private java.lang.String endDate;
    @ApiModelProperty(value = "多租户id")
    private String tenantId;
    @ApiModelProperty(value = "排序")
    private String orderBy;
    @ApiModelProperty(value = "多租户id集合")
    private List<String> tenantIds;

    @ApiModelProperty(value = "多租户id集合-前端传参")
    private List<String> tenantList;

    @ApiModelProperty(value = "是否使用 0否 1 是")
    private java.lang.Integer isUse;

    @ApiModelProperty(value = "类型 0 车险 1 财险 2 增值服务")
    private java.lang.Integer  chatType;

}
