
package org.jeecg.modules.corp.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.sql.Timestamp;
import java.time.LocalDate;
import java.util.Objects;

@Data
public class PdAddedLedgerDTO {

    @ExcelProperty("日期")
    private java.sql.Date orderDate;

    private Timestamp clickTime;

    @ExcelProperty("姓名")
    private String name;

    @ExcelProperty("手机号")
    private String phone;

    @ExcelProperty("服务")
    private String userItem;

    @ExcelProperty("租户")
    private Integer tenantId;
    /**是否存在聊天用户（0=否，1=是）*/
    private Integer hasChatUser;
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof PdAddedLedgerDTO)) return false;
        PdAddedLedgerDTO that = (PdAddedLedgerDTO) o;
        return Objects.equals(name, that.name)
                && Objects.equals(userItem, that.userItem)
                && Objects.equals(tenantId, that.tenantId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(name, userItem, tenantId);
    }
}
