package org.jeecg.modules.corp.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.sql.Timestamp;

@Data
public class PdInsuranceLedgerDTO {
    @ExcelProperty("日期")
    private java.sql.Date orderDate;

    private Timestamp clickTime;

    @ExcelProperty("姓名")
    @NotBlank(message = "姓名不能为空")
    private String name;

    @ExcelProperty("手机号")
    private String phone;

    @ExcelProperty("服务")
    @NotBlank(message = "服务不能为空")
    private String userItem;

    @ExcelProperty("多租户")
    @NotBlank(message = "多租户不能为空")
    private Integer tenantId;
    /**是否存在聊天用户（0=否，1=是）*/
    private Integer hasChatUser;
}

