package org.jeecg.modules.corp.dto;

import lombok.Data;

/**
 * @Description: 链接快照查询DTO
 * @Author: jeecg-boot
 * @Date: 2024-05-16
 */
@Data
public class PdLinkRecodeDto {

    /**
     * 开始时间
     */
    private String startTime;

    /**
     * 结束时间
     */
    private String endTime;

    /**
     * 查询类型：0-车险 1-财险 2-增值服务
     */
    private Integer queryType;

    /**
     * 租户ID
     */
    private Integer tenantId;
}
