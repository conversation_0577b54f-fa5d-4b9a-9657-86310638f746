package org.jeecg.modules.corp.entity;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import java.sql.Timestamp;

/**
* 
*
* <AUTHOR>
* @since 2025-04-11
*/
@Getter
@Setter
@Accessors(chain = true)
@ApiModel(value="ClickAutoPre对象", description="")
public class ClickAutoPre implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    @ApiModelProperty("点击时间")
    private Timestamp clickTime;

    @ApiModelProperty("多租户")
    private Integer tenantId;

    @ApiModelProperty("台账类型(1.财险台账 2.增值服务台账 3.车险台账)")
    private Integer ledgerType;

    @ApiModelProperty("是否进行台账（0=否，1=是）")
    private Integer autoCreate;

    @ApiModelProperty("是否存在聊天用户（0=否，1=是）")
    private Integer hasChatUser;

    @ApiModelProperty("城市")
    private String city;

    @ApiModelProperty("创建人")
    private String createBy;

    @ApiModelProperty("创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Timestamp createTime;



}

