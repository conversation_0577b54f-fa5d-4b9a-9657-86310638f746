package org.jeecg.modules.corp.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description: 按小时统计点击报表
 * @Author: jeecg-boot
 * @Date: 2024-05-16
 */
@Data
@TableName("click_report_hourly")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class ClickReportHourly implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 点击数
     */
    private Integer clickNum;

    /**
     * 点击PV数
     */
    private Integer clickPv;

    /**
     * 统计日期
     */
    private Date statDate;

    /**
     * 小时 (0-23)
     */
    private Integer hour;

    /**
     * 租户ID
     */
    private Integer tenantId;

    /**
     * 0车险 1财险 2增值服务
     */
    private Integer configType;
}

