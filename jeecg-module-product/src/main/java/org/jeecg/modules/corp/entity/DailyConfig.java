package org.jeecg.modules.corp.entity;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import java.sql.Timestamp;

/**
* 每日租户配置表
*
* <AUTHOR>
* @since 2025-04-12
*/
@Getter
@Setter
@Accessors(chain = true)
@ApiModel(value="DailyConfig对象", description="每日租户配置表")
public class DailyConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键 ID")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    @ApiModelProperty("每日配置 JSON 内容（包含：台账区间、聊天用户区间、城市范围）")
    private String configJson;

    @ApiModelProperty("每日点击数开始")
    private Integer clickStart;

    @ApiModelProperty("每日点击数结束")
    private Integer clickEnd;

    @ApiModelProperty("多租户")
    private Integer tenantId;

    @ApiModelProperty("更新人")
    private String updateBy;

    @ApiModelProperty("更新日期")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Timestamp updateTime;

    @ApiModelProperty("创建人")
    private String createBy;

    @ApiModelProperty("创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Timestamp createTime;



}

