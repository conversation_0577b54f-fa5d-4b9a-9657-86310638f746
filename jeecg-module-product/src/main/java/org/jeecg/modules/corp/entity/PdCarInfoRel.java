package org.jeecg.modules.corp.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 服务关系表
 * @Author: jeecg-boot
 * @Date:   2024-11-18
 * @Version: V1.0
 */
@Data
@TableName("pd_car_info_rel")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="pd_car_info_rel对象", description="服务关系表")
public class PdCarInfoRel implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private String id;
	/**用户 id*/
	@Excel(name = "用户 id", width = 15)
    @ApiModelProperty(value = "用户 id")
    private String userId;
	/**服务主键*/
	@Excel(name = "服务主键", width = 15)
    @ApiModelProperty(value = "服务主键")
    private String mercialId;
	/**车险主键*/
	@Excel(name = "车险主键", width = 15)
    @ApiModelProperty(value = "车险主键")
    private String carInfoId;
	/**输入金额*/
    @ApiModelProperty(value = "输入金额")
    private Integer amount;
}
