package org.jeecg.modules.corp.entity;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.jeecg.common.aspect.annotation.Dict;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 聊天用户
 * @Author: jeecg-boot
 * @Date:   2024-11-09
 * @Version: V1.0
 */
@Data
@TableName("pd_chat_user")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="pd_chat_user对象", description="聊天用户")
public class PdChatUser implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private java.lang.String id;
	/**广告类型*/
	@Excel(name = "广告类型(0-车险;1-财险;2-增值服务)", width = 15)
    @ApiModelProperty(value = "广告类型")
    @Dict(dicCode = "service_code")
    private java.lang.Integer wideType;
	/**ip地址*/
	@Excel(name = "ip地址", width = 15)
    @ApiModelProperty(value = "ip地址")
    private java.lang.String ipAddress;
	/**引流日期*/
	@Excel(name = "引流日期", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "引流日期")
    private java.util.Date diverData;
	/**用户id*/
	@Excel(name = "用户id", width = 15)
    @ApiModelProperty(value = "用户id")
    private java.lang.String userId;
	/**用户名称*/
	@Excel(name = "用户名称", width = 15)
    @ApiModelProperty(value = "用户名称")
    private java.lang.String userName;
	/**用户来源*/
	@Excel(name = "用户来源", width = 15)
    @ApiModelProperty(value = "用户来源")
    private java.lang.Integer source;
	/**对应台账主键*/
	@Excel(name = "对应台账主键", width = 15)
    @ApiModelProperty(value = "对应台账主键")
    private java.lang.String keyId;
	/**租户*/
	@Excel(name = "租户", width = 15)
    @ApiModelProperty(value = "租户")
    private java.lang.Integer tenantId;
    @ApiModelProperty(value = "多租户名称")
    private transient String tenantName;
}
