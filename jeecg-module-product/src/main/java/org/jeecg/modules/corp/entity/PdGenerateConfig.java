package org.jeecg.modules.corp.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 生成规则配置
 * @Author: jeecg-boot
 * @Date:   2024-11-14
 * @Version: V1.0
 */
@Data
@TableName("pd_generate_config")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="pd_generate_config对象", description="生成规则配置")
public class PdGenerateConfig implements Serializable {
    private static final long serialVersionUID = 1L;

    /**主键*/
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private String id;

    @ApiModelProperty(value = "创建人")
    private String createBy;

    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private Date createTime;

    @ApiModelProperty(value = "更新人")
    private String updateBy;

    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private Date updateTime;

    @ApiModelProperty(value = "链接类型(0-车险;1-财险;2-增值服务)")
    private Integer configType;

    @ApiModelProperty(value = "跳出率开始区间(%)")
    private BigDecimal bounceRateStart;

    @ApiModelProperty(value = "跳出率结束区间(%)")
    private BigDecimal bounceRateEnd;

    @ApiModelProperty(value = "转化率开始区间(%)")
    private BigDecimal conversionRateStart;

    @ApiModelProperty(value = "转化率结束区间(%)")
    private BigDecimal conversionRateEnd;

    @ApiModelProperty(value = "表单提交数开始区间")
    private Integer formSubmissionsStart;

    @ApiModelProperty(value = "表单提交数结束区间")
    private Integer formSubmissionsEnd;

    @ApiModelProperty(value = "点击率开始区间(%)")
    private BigDecimal ctrStart;

    @ApiModelProperty(value = "点击率结束区间(%)")
    private BigDecimal ctrEnd;

    @ApiModelProperty(value = "平均停留时长开始区间(秒)")
    private Integer avgStayTimeStart;

    @ApiModelProperty(value = "平均停留时长结束区间(秒)")
    private Integer avgStayTimeEnd;

    @ApiModelProperty(value = "返回率开始区间(%)")
    private BigDecimal returnRateStart;

    @ApiModelProperty(value = "返回率结束区间(%)")
    private BigDecimal returnRateEnd;

    @ApiModelProperty(value = "内容完成率开始区间(%)")
    private BigDecimal completionRateStart;

    @ApiModelProperty(value = "内容完成率结束区间(%)")
    private BigDecimal completionRateEnd;

    @ApiModelProperty(value = "首屏CTR值开始区间(%)")
    private BigDecimal firstScreenCtrStart;

    @ApiModelProperty(value = "首屏CTR值结束区间(%)")
    private BigDecimal firstScreenCtrEnd;

    @ApiModelProperty(value = "内容Jump率开始区间(%)")
    private BigDecimal contentJumpRateStart;

    @ApiModelProperty(value = "内容Jump率结束区间(%)")
    private BigDecimal contentJumpRateEnd;

    @ApiModelProperty(value = "内容Return率开始区间(%)")
    private BigDecimal contentReturnRateStart;

    @ApiModelProperty(value = "内容Return率结束区间(%)")
    private BigDecimal contentReturnRateEnd;

    @ApiModelProperty(value = "Click深度分布开始区间(层)")
    private BigDecimal clickDepthStart;

    @ApiModelProperty(value = "Click深度分布结束区间(层)")
    private BigDecimal clickDepthEnd;

    @ApiModelProperty(value = "Click间隔时间开始区间(秒)")
    private BigDecimal clickIntervalTimeStart;

    @ApiModelProperty(value = "Click间隔时间结束区间(秒)")
    private BigDecimal clickIntervalTimeEnd;

    @ApiModelProperty(value = "Engagement得分开始区间(分)")
    private BigDecimal engagementScoreStart;

    @ApiModelProperty(value = "Engagement得分结束区间(分)")
    private BigDecimal engagementScoreEnd;

    @ApiModelProperty(value = "翻页率比例开始")
    private Integer pageStartNum;

    @ApiModelProperty(value = "翻页率结束")
    private Integer pageEndNum;

    @ApiModelProperty(value = "独立Query数开始区间")
    private Integer uniqueQueryStart;

    @ApiModelProperty(value = "独立Query数结束区间")
    private Integer uniqueQueryEnd;

    @ApiModelProperty(value = "TOP3 PV-CTR开始区间(%)")
    private BigDecimal top3PvCtrStart;

    @ApiModelProperty(value = "TOP3 PV-CTR结束区间(%)")
    private BigDecimal top3PvCtrEnd;

    @ApiModelProperty(value = "租户id")
    private Integer tenantId;

    private transient String tenantName;
}