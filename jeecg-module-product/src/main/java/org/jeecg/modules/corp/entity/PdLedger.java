package org.jeecg.modules.corp.entity;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 台账
 * @Author: jeecg-boot
 * @Date:   2024-11-08
 * @Version: V1.0
 */
@Data
@TableName("pd_ledger")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="pd_ledger对象", description="台账")
public class PdLedger implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private java.lang.String id;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private java.lang.String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private java.util.Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private java.lang.String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private java.util.Date updateTime;
	/**险种名称*/
	@Excel(name = "险种", width = 15)
    @ApiModelProperty(value = "险种")
    private java.lang.String insuranceName;
    @Excel(name = "手机号", width = 15)
    @ApiModelProperty(value = "手机号")
    private java.lang.String phoneNumber;
	/**签单日期*/
    @Excel(name = "签单日期", width = 15)
    @ApiModelProperty(value = "签单日期")
    private String signDate;
    private transient Date signDateTime;
    private transient String city;
	/**投保人*/
	@Excel(name = "投保人", width = 15)
    @ApiModelProperty(value = "投保人")
    private java.lang.String policyholder;
    private transient java.lang.String ipAddress;
	/**被保人*/
	@Excel(name = "被保人", width = 15)
    @ApiModelProperty(value = "被保人")
    private java.lang.String insured;
	/**车牌号*/
	@Excel(name = "车牌号", width = 15)
    @ApiModelProperty(value = "车牌号")
    private java.lang.String licensePlate;
	/**厂牌型号*/
	@Excel(name = "厂牌型号", width = 15)
    @ApiModelProperty(value = "厂牌型号")
    private java.lang.String brandModel;
	/**车架号*/
	@Excel(name = "车架号", width = 15)
    @ApiModelProperty(value = "车架号")
    private java.lang.String vin;
	/**删除*/
    private java.lang.String isDelete;
    @ApiModelProperty(value = "是否生成;0-未生成;1生成中;2是")
    private  Integer chatStatus;
	/**多租户*/
	@Excel(name = "多租户", width = 15)
    @ApiModelProperty(value = "多租户")
    private java.lang.Integer tenantId;
    @ApiModelProperty(value = "多租户名称")
    private transient String tenantName;
    /**是否存在聊天用户（0=否，1=是）*/
    private transient Integer hasChatUser;
}
