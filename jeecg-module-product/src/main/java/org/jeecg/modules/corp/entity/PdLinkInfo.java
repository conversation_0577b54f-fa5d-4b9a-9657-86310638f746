package org.jeecg.modules.corp.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 链接对应租户关系
 * @Author: jeecg-boot
 * @Date:   2024-11-12
 * @Version: V1.0
 */
@Data
@TableName("pd_link_info")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="pd_link_info对象", description="链接对应租户关系")
public class PdLinkInfo implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private java.lang.String id;
	/**链接名称*/
	@Excel(name = "链接名称", width = 15)
    @ApiModelProperty(value = "链接名称")
    private java.lang.String name;
    @ApiModelProperty(value = "表格状态")
    @Dict(dicCode = "excel_code")
    private java.lang.Integer excelStatus;
	/**链接类型*/
	@Excel(name = "链接类型(0-车险;1-财险;2-增值服务)", width = 15)
    @ApiModelProperty(value = "链接类型")
    @Dict(dicCode = "service_code")
    private java.lang.Integer linkType;
    @ApiModelProperty(value = "预约日期与当日间隔")
    private java.lang.Integer makeDate;
    @ApiModelProperty(value = "预约成功第一段")
    private java.lang.String makeOne;
    @ApiModelProperty(value = "预约成功第二段前")
    private java.lang.String makeTwoStart;
    @ApiModelProperty(value = "预约成功第二段尾")
    private java.lang.String makeTwoEnd;
    @ApiModelProperty(value = "预约成功第三段")
    private java.lang.String makeThree;
	/**租户 id*/
	@Excel(name = "租户 id", width = 15)
    @ApiModelProperty(value = "租户 id")
    private java.lang.Integer tenantId;
    private transient String tenantName;
}
