package org.jeecg.modules.corp.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 链接快照
 * @Author: jeecg-boot
 * @Date:   2024-11-12
 * @Version: V1.0
 */
@Data
@TableName("pd_link_recode")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "pd_link_recode对象", description = "链接快照")
public class PdLinkRecode implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 主键 */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private String id;

    /** 日期 */
    @Excel(name = "日期", width = 15, format = "yyyy-MM-dd")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "日期")
    private Date linkDate;

    /**更新日期*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private java.util.Date updateTime;



    /** 配置类型 */
    @ApiModelProperty(value = "配置类型(0-车险;1-财险;2-增值服务)")
    private Integer configType;

    @ApiModelProperty(value = "跳出率(%)")
    private BigDecimal bounceRate;

    @ApiModelProperty(value = "转化率(%)")
    private BigDecimal conversionRate;

    @ApiModelProperty(value = "表单提交数")
    private Integer formSubmissions;

    @ApiModelProperty(value = "点击率(%)")
    private BigDecimal ctr;

    @ApiModelProperty(value = "平均停留时长(秒)")
    private Integer avgStayTime;

    @ApiModelProperty(value = "返回率(%)")
    private BigDecimal returnRate;

    @ApiModelProperty(value = "内容完成率(%)")
    private BigDecimal completionRate;

    @ApiModelProperty(value = "首屏CTR值(%)")
    private BigDecimal firstScreenCtr;

    @ApiModelProperty(value = "内容Jump率(%)")
    private BigDecimal contentJumpRate;

    @ApiModelProperty(value = "内容Return率(%)")
    private BigDecimal contentReturnRate;

    @ApiModelProperty(value = "Click深度分布(层)")
    private BigDecimal clickDepth;

    @ApiModelProperty(value = "Click间隔时间(秒)")
    private BigDecimal clickIntervalTime;

    @ApiModelProperty(value = "Engagement得分(分)")
    private BigDecimal engagementScore;

    @ApiModelProperty(value = "翻页率比例")
    private BigDecimal pageRate;

    @ApiModelProperty(value = "独立Query数")
    private Integer uniqueQuery;

    @ApiModelProperty(value = "TOP3 PV-CTR(%)")
    private BigDecimal top3PvCtr;

    /** 租户id */
    @ApiModelProperty(value = "租户id")
    private Integer tenantId;
}