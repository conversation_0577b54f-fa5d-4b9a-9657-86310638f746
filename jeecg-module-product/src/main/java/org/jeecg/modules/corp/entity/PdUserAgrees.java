package org.jeecg.modules.corp.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 用户同意协议
 * @Author: jeecg-boot
 * @Date:   2024-11-14
 * @Version: V1.0
 */
@Data
@TableName("pd_user_agrees")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="pd_user_agrees对象", description="用户同意协议")
public class PdUserAgrees implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private java.lang.String id;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private java.lang.String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private java.util.Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private java.lang.String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private java.util.Date updateTime;
	/**标题*/
	@Excel(name = "标题", width = 15)
    @ApiModelProperty(value = "标题")
    private java.lang.String name;
	/**提醒标语*/
	@Excel(name = "提醒标语", width = 15)
    @ApiModelProperty(value = "提醒标语")
    private java.lang.String slogan;
	/**前须知名称*/
	@Excel(name = "前须知名称", width = 15)
    @ApiModelProperty(value = "前须知名称")
    private java.lang.String firstInfo;
	/**后须知名称*/
	@Excel(name = "后须知名称", width = 15)
    @ApiModelProperty(value = "后须知名称")
    private java.lang.String latterInfo;
	/**前段条款*/
	@Excel(name = "前段条款", width = 15)
    @ApiModelProperty(value = "前段条款")
    private java.lang.String firstContent;
	/**后段条款*/
	@Excel(name = "后段条款", width = 15)
    @ApiModelProperty(value = "后段条款")
    private java.lang.String latterContent;
	/**类型*/
	@Excel(name = "类型", width = 15)
    @ApiModelProperty(value = "类型")
    @Dict(dicCode = "service_code")
    private java.lang.Integer linkType;
	/**租户 id*/
	@Excel(name = "租户 id", width = 15)
    @ApiModelProperty(value = "租户 id")
    private java.lang.Integer tenantId;
    private transient String tenantName;
}
