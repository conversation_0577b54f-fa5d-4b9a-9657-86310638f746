package org.jeecg.modules.corp.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;

import java.io.Serializable;

/**
 * @Description: 城市车牌前缀关系表
 * @Author: jeecg-boot
 * @Date: 2024-11-12
 * @Version: V1.0
 */
@Data
@TableName("sys_city_plate_prefix")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="sys_city_plate_prefix对象", description="城市车牌前缀关系表")
public class SysCityPlatePrefix implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 主键 */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private String id;

    /** 城市编码 */
    @Excel(name = "城市编码", width = 15)
    @ApiModelProperty(value = "城市编码")
    private String cityCode;

    /** 完整城市名称 */
    @Excel(name = "完整城市名称", width = 15)
    @ApiModelProperty(value = "完整城市名称")
    private String allCityName;

    /** 顶级城市名称 */
    @Excel(name = "顶级城市名称", width = 15)
    @ApiModelProperty(value = "顶级城市名称")
    private String topCityName;

    /** 车牌前缀 */
    @Excel(name = "车牌前缀", width = 15)
    @ApiModelProperty(value = "车牌前缀")
    private String platePrefix;
}
