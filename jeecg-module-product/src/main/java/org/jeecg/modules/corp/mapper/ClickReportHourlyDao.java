package org.jeecg.modules.corp.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * @Description: 按小时统计点击报表DAO
 * @Author: jeecg-boot
 * @Date: 2024-05-16
 */
@Mapper
public interface ClickReportHourlyDao {
    
    /**
     * 根据日期范围获取点击统计数据
     * 
     * @param params 参数Map，包含startDate, endDate, configType, tenantId等参数
     * @return 统计数据列表
     */
    List<Map<String, Object>> getClickStatsByDateRange(@Param("params") Map<String, Object> params);
}