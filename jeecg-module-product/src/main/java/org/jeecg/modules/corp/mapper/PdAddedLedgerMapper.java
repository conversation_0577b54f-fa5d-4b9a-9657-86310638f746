package org.jeecg.modules.corp.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.corp.dto.LedgerListDto;
import org.jeecg.modules.corp.entity.PdAddedLedger;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * @Description: 增值服务台账
 * @Author: jeecg-boot
 * @Date:   2024-11-25
 * @Version: V1.0
 */
public interface PdAddedLedgerMapper extends BaseMapper<PdAddedLedger> {

    IPage<PdAddedLedger> pageList(Page<PdAddedLedger> page, @Param("dto") LedgerListDto dto);
}
