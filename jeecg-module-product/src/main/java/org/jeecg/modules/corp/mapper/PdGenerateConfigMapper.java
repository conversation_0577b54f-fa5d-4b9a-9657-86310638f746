package org.jeecg.modules.corp.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.corp.entity.PdGenerateConfig;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.jeecg.modules.wechat.dto.config.CarInsuranceConfigVO;

/**
 * @Description: 生成规则配置
 * @Author: jeecg-boot
 * @Date:   2024-11-14
 * @Version: V1.0
 */
public interface PdGenerateConfigMapper extends BaseMapper<PdGenerateConfig> {

    List<CarInsuranceConfigVO> getTenantConfig(@Param("ids") List<Integer> tenantIds);
}
