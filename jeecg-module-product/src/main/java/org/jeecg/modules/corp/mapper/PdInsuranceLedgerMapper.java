package org.jeecg.modules.corp.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.corp.dto.LedgerListDto;
import org.jeecg.modules.corp.entity.PdInsuranceLedger;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * @Description: 财险台账
 * @Author: jeecg-boot
 * @Date:   2024-11-25
 * @Version: V1.0
 */
public interface PdInsuranceLedgerMapper extends BaseMapper<PdInsuranceLedger> {

    IPage<PdInsuranceLedger> pageList(Page<PdInsuranceLedger> page, @Param("dto") LedgerListDto dto);
}
