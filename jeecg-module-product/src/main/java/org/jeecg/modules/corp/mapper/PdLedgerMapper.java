package org.jeecg.modules.corp.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.corp.dto.LedgerListDto;
import org.jeecg.modules.corp.entity.PdLedger;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * @Description: 台账
 * @Author: jeecg-boot
 * @Date:   2024-11-08
 * @Version: V1.0
 */
public interface PdLedgerMapper extends BaseMapper<PdLedger> {

    IPage<PdLedger> pageList(Page<PdLedger> page, @Param("dto") LedgerListDto dto);
}
