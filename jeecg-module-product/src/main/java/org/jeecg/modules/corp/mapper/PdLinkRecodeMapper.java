package org.jeecg.modules.corp.mapper;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.corp.dto.PdLinkRecodeDto;
import org.jeecg.modules.corp.entity.PdLinkRecode;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.jeecg.modules.corp.vo.PdLinkRecodeVo;

/**
 * @Description: 链接快照
 * @Author: jeecg-boot
 * @Date:   2024-11-12
 * @Version: V1.0
 */
public interface PdLinkRecodeMapper extends BaseMapper<PdLinkRecode> {

    /**
     * 查询链接快照列表
     * @param dto 查询参数
     * @return 链接快照列表
     */
    List<PdLinkRecodeVo> listRecode(PdLinkRecodeDto dto);

    /**
     * 按日期汇总点击数据
     * @param tenantId 租户ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 点击数据列表
     */
    List<Map<String, Object>> listClickData(@Param("tenantId") Integer tenantId,
                                           @Param("startTime") String startTime,
                                           @Param("endTime") String endTime);



    /**
     * 联合查询 pd_link_recode 和 click_report_hourly 表
     * @param tenantId 租户ID
     * @param queryType 查询类型
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 联合查询结果列表
     */
    List<Map<String, Object>> listRecodeWithClick(@Param("tenantId") Integer tenantId,
                                                @Param("queryType") String queryType,
                                                @Param("startTime") String startTime,
                                                @Param("endTime") String endTime);

    /**
     * 联合查询对比周期的 pd_link_recode 和 click_report_hourly 表
     * @param tenantId 租户ID
     * @param queryType 查询类型
     * @param compareStartTime 对比开始时间
     * @param compareEndTime 对比结束时间
     * @return 联合查询结果列表
     */
    List<Map<String, Object>> listCompareRecodeWithClick(@Param("tenantId") Integer tenantId,
                                                       @Param("queryType") String queryType,
                                                       @Param("compareStartTime") String compareStartTime,
                                                       @Param("compareEndTime") String compareEndTime);

    /**
     * 计算平均值的联合查询
     * @param tenantId 租户ID
     * @param queryType 查询类型
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 平均值查询结果
     */
    Map<String, Object> listRecodeWithClickAvg(@Param("tenantId") Integer tenantId,
                                             @Param("queryType") String queryType,
                                             @Param("startTime") String startTime,
                                             @Param("endTime") String endTime);

    /**
     * 计算对比周期平均值的联合查询
     * @param tenantId 租户ID
     * @param queryType 查询类型
     * @param compareStartTime 对比开始时间
     * @param compareEndTime 对比结束时间
     * @return 对比周期平均值查询结果
     */
    Map<String, Object> listCompareRecodeWithClickAvg(@Param("tenantId") Integer tenantId,
                                                    @Param("queryType") String queryType,
                                                    @Param("compareStartTime") String compareStartTime,
                                                    @Param("compareEndTime") String compareEndTime);
}
