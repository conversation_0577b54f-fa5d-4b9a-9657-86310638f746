package org.jeecg.modules.corp.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.corp.entity.SysCityPlatePrefix;

/**
 * @Description: 城市车牌前缀关系表
 * @Author: jeecg-boot
 * @Date: 2024-11-12
 * @Version: V1.0
 */
public interface SysCityPlatePrefixMapper extends BaseMapper<SysCityPlatePrefix> {

    /**
     * 根据城市编码查询车牌前缀
     * @param cityCode 城市编码
     * @return 车牌前缀
     */
    String getPlatePrefixByCityCode(@Param("cityCode") String cityCode);
}
