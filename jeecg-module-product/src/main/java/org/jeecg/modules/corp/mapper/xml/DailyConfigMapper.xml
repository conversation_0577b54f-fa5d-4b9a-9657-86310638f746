<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="org.jeecg.modules.corp.mapper.DailyConfigDao">
    <sql id="AllColumns">
        t.id as id,
        t.config_json as configJson,
        t.click_start as clickStart,
        t.click_end as clickEnd,
        t.company_id as companyId,
        t.update_by as updateBy,
        t.update_time as updateTime,
        t.create_by as createBy,
        t.create_time as createTime
    </sql>


</mapper>