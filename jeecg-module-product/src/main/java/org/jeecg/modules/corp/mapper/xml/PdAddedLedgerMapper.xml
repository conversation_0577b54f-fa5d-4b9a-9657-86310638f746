<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.corp.mapper.PdAddedLedgerMapper">

    <select id="pageList" resultType="org.jeecg.modules.corp.entity.PdAddedLedger">
        SELECT
               ld.id,
               ld.order_date AS orderDate,ld.name,ld.phone,ld.user_item AS userItem,ld.tenant_id AS tenantId
        ,car.is_vied AS isVied,st.name AS tenantName
        FROM pd_added_ledger ld
        LEFT JOIN pd_added car ON ld.id = car.ledger_id
        LEFT JOIN sys_tenant st ON st.id = ld.tenant_id
        <where>
            <if test="dto.tenantIds != null and dto.tenantIds.size() > 0">
                AND ld.tenant_id IN
                <foreach collection="dto.tenantIds" item="tenantId" open="(" separator="," close=")">
                    #{tenantId}
                </foreach>
            </if>
            <if test="dto.tenantList != null and dto.tenantList.size() > 0">
                AND ld.tenant_id IN
                <foreach collection="dto.tenantList" item="tenantId" open="(" separator="," close=")">
                    #{tenantId}
                </foreach>
            </if>

            <if test="dto.chatStatus != null">
                AND car.is_vied = #{dto.chatStatus}
            </if>

            <if test="dto.startDate != null">
                AND ld.order_date &gt;= #{dto.startDate}
            </if>
            <if test="dto.endDate != null">
                AND ld.order_date &lt;= #{dto.endDate}
            </if>
            <if test="dto.name != null">
                AND ld.name LIKE CONCAT('%',#{dto.name},'%')
            </if>
        </where>
        <if test="dto.orderBy != null">
            ORDER BY ${dto.orderBy}
        </if>
        <if test="dto.orderBy == null">
            ORDER BY car.create_time DESC
        </if>
    </select>
</mapper>