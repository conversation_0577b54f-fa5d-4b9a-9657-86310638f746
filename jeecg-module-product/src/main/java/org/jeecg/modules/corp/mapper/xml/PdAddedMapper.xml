<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.corp.mapper.PdAddedMapper">

    <select id="pageList" resultType="org.jeecg.modules.corp.entity.PdAdded">
        SELECT
        car.create_time AS createTime,
        car.ip_address AS ipAddress,
        car.guest_name AS guestName,
        st.name AS tenantName,
        car.phone AS phone,
        car.name AS name,
        integ.name AS serve
        FROM pd_added car
        LEFT JOIN pd_integrated integ ON car.serve = integ.id
        LEFT JOIN sys_tenant st ON car.tenant_id = st.id
        <where>
            <if test="dto.tenantIds != null and dto.tenantIds.size() > 0">
                AND car.tenant_id IN
                <foreach collection="dto.tenantIds" item="tid" open="(" separator="," close=")">
                    #{tid}
                </foreach>
            </if>
            <if test="dto.startDate != null">
                AND car.create_time &gt;= #{dto.startDate}
            </if>
            <if test="dto.endDate != null">
                AND car.create_time &lt;= #{dto.endDate}
            </if>
        </where>
        GROUP BY car.id
        <if test="dto.orderBy != null">
            ORDER BY ${dto.orderBy}
        </if>
        <if test="dto.orderBy == null">
            ORDER BY car.create_time DESC
        </if>
    </select>
</mapper>