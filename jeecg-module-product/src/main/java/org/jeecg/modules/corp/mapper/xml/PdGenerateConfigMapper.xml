<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.corp.mapper.PdGenerateConfigMapper">

    <select id="getTenantConfig" resultType="org.jeecg.modules.wechat.dto.config.CarInsuranceConfigVO">
        SELECT
        config_type AS configType,
        bounce_rate_start AS bounceRateStart,
        bounce_rate_end AS bounceRateEnd,
        conversion_rate_start AS conversionRateStart,
        conversion_rate_end AS conversionRateEnd,
        form_submissions_start AS formSubmissionsStart,
        form_submissions_end AS formSubmissionsEnd,
        ctr_start AS ctrStart,
        ctr_end AS ctrEnd,
        avg_stay_time_start AS avgStayTimeStart,
        avg_stay_time_end AS avgStayTimeEnd,
        return_rate_start AS returnRateStart,
        return_rate_end AS returnRateEnd,
        completion_rate_start AS completionRateStart,
        completion_rate_end AS completionRateEnd,
        first_screen_ctr_start AS firstScreenCtrStart,
        first_screen_ctr_end AS firstScreenCtrEnd,
        content_jump_rate_start AS contentJumpRateStart,
        content_jump_rate_end AS contentJumpRateEnd,
        content_return_rate_start AS contentReturnRateStart,
        content_return_rate_end AS contentReturnRateEnd,
        click_depth_start AS clickDepthStart,
        click_depth_end AS clickDepthEnd,
        click_interval_time_start AS clickIntervalTimeStart,
        click_interval_time_end AS clickIntervalTimeEnd,
        engagement_score_start AS engagementScoreStart,
        engagement_score_end AS engagementScoreEnd,
        page_start_num AS pageStartNum,
        page_end_num AS pageEndNum,
        unique_query_start AS uniqueQueryStart,
        unique_query_end AS uniqueQueryEnd,
        top3_pv_ctr_start AS top3PvCtrStart,
        top3_pv_ctr_end AS top3PvCtrEnd,
        tenant_id AS tenantId
        FROM pd_generate_config
        WHERE tenant_id IN
        <foreach item="id" index="index" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

</mapper>