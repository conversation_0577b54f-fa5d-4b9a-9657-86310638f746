<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.corp.mapper.PdLedgerMapper">

    <select id="pageList" resultType="org.jeecg.modules.corp.entity.PdLedger">
        SELECT
        ld.id,
        ld.sign_date AS signDate,
        ld.insurance_name AS insuranceName,
        ld.phone_number AS phoneNumber,
        ld.policyholder,
        ld.insured,
        ld.license_plate AS licensePlate,
        ld.brand_model AS brandModel,
        ld.vin,
        ld.chat_status AS chatStatus,
        st.name AS tenantName
        FROM pd_ledger ld
        LEFT JOIN sys_tenant st ON st.id = ld.tenant_id
        <where>
            1 = 1

            <if test="dto.tenantIds != null and dto.tenantIds.size() > 0">
                AND ld.tenant_id IN
                <foreach collection="dto.tenantIds" item="tenantId" open="(" separator="," close=")">
                    #{tenantId}
                </foreach>
            </if>
            <if test="dto.tenantList != null and dto.tenantList.size() > 0">
                AND ld.tenant_id IN
                <foreach collection="dto.tenantList" item="tenantId" open="(" separator="," close=")">
                    #{tenantId}
                </foreach>
            </if>

            <if test="dto.chatStatus != null">
                AND ld.chat_status = #{dto.chatStatus}
            </if>

            <if test="dto.startDate != null">
                AND ld.sign_date &gt;= #{dto.startDate}
            </if>
            <if test="dto.endDate != null">
                AND ld.sign_date &lt;= #{dto.endDate}
            </if>
        </where>

        <if test="dto.orderBy != null">
            ORDER BY ${dto.orderBy}
        </if>
        <if test="dto.orderBy == null">
            ORDER BY ld.create_time DESC
        </if>
    </select>
</mapper>