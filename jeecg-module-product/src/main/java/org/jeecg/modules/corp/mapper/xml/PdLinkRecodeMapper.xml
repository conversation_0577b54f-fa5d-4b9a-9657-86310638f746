<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.corp.mapper.PdLinkRecodeMapper">

    <select id="listRecode" resultType="org.jeecg.modules.corp.vo.PdLinkRecodeVo">
        SELECT
            r.id,
            r.link_date AS linkDate,
            r.config_type AS configType,
            r.tenant_id AS tenantId,
            r.bounce_rate AS bounceRate,
            r.conversion_rate AS conversionRate,
            r.form_submissions AS formSubmissions,
            r.ctr,
            r.avg_stay_time AS avgStayTime,
            r.return_rate AS returnRate,
            r.completion_rate AS completionRate,
            r.first_screen_ctr AS firstScreenCtr,
            r.content_jump_rate AS contentJumpRate,
            r.content_return_rate AS contentReturnRate,
            r.click_depth AS clickDepth,
            r.click_interval_time AS clickIntervalTime,
            r.engagement_score AS engagementScore,
            r.page_rate AS pageRate,
            r.unique_query AS uniqueQuery,
            r.top3_pv_ctr AS top3PvCtr
        FROM
            pd_link_recode r
        WHERE
            r.tenant_id = #{tenantId}
            AND r.config_type = #{queryType}
            AND r.link_date BETWEEN #{startTime} AND #{endTime}
        ORDER BY
            r.link_date ASC
    </select>

    <!-- 新增：按日期汇总点击数据 -->
    <select id="listClickData" resultType="java.util.Map">
        SELECT
            DATE_FORMAT(stat_date, '%Y-%m-%d') AS date,
            SUM(click_num) AS clickNum,
            SUM(click_pv) AS clickPv
        FROM
            click_report_hourly
        WHERE
            tenant_id = #{tenantId}
            AND stat_date BETWEEN #{startTime} AND #{endTime}
        GROUP BY
            DATE_FORMAT(stat_date, '%Y-%m-%d')
        ORDER BY
            date ASC
    </select>



    <!-- 新增：联合查询 pd_link_recode 和 click_report_hourly 表 -->
    <select id="listRecodeWithClick" resultType="java.util.Map">
        SELECT
            r.id,
            r.link_date AS linkDate,
            r.config_type AS configType,
            r.tenant_id AS tenantId,
            r.bounce_rate AS bounceRate,
            r.conversion_rate AS conversionRate,
            r.form_submissions AS formSubmissions,
            r.ctr,
            r.avg_stay_time AS avgStayTime,
            r.return_rate AS returnRate,
            r.completion_rate AS completionRate,
            r.first_screen_ctr AS firstScreenCtr,
            r.content_jump_rate AS contentJumpRate,
            r.content_return_rate AS contentReturnRate,
            r.click_depth AS clickDepth,
            r.click_interval_time AS clickIntervalTime,
            r.engagement_score AS engagementScore,
            r.page_rate AS pageRate,
            r.unique_query AS uniqueQuery,
            r.top3_pv_ctr AS top3PvCtr,
            COALESCE(SUM(c.click_num), 0) AS clickNum,
            COALESCE(SUM(c.click_pv), 0) AS clickPv
        FROM
            pd_link_recode r
        LEFT JOIN
            click_report_hourly c ON DATE(r.link_date) = DATE(c.stat_date) AND r.tenant_id = c.tenant_id
        WHERE
            r.tenant_id = #{tenantId}
            AND r.config_type = #{queryType}
            AND r.link_date BETWEEN #{startTime} AND #{endTime}
        GROUP BY
            r.id, r.link_date, r.config_type, r.tenant_id, r.bounce_rate, r.conversion_rate,
            r.form_submissions, r.ctr, r.avg_stay_time, r.return_rate, r.completion_rate,
            r.first_screen_ctr, r.content_jump_rate, r.content_return_rate, r.click_depth,
            r.click_interval_time, r.engagement_score, r.page_rate, r.unique_query, r.top3_pv_ctr
        ORDER BY
            r.link_date ASC
    </select>

    <!-- 新增：联合查询对比周期的 pd_link_recode 和 click_report_hourly 表 -->
    <select id="listCompareRecodeWithClick" resultType="java.util.Map">
        SELECT
            r.id,
            r.link_date AS linkDate,
            r.config_type AS configType,
            r.tenant_id AS tenantId,
            r.bounce_rate AS bounceRate,
            r.conversion_rate AS conversionRate,
            r.form_submissions AS formSubmissions,
            r.ctr,
            r.avg_stay_time AS avgStayTime,
            r.return_rate AS returnRate,
            r.completion_rate AS completionRate,
            r.first_screen_ctr AS firstScreenCtr,
            r.content_jump_rate AS contentJumpRate,
            r.content_return_rate AS contentReturnRate,
            r.click_depth AS clickDepth,
            r.click_interval_time AS clickIntervalTime,
            r.engagement_score AS engagementScore,
            r.page_rate AS pageRate,
            r.unique_query AS uniqueQuery,
            r.top3_pv_ctr AS top3PvCtr,
            COALESCE(SUM(c.click_num), 0) AS clickNum,
            COALESCE(SUM(c.click_pv), 0) AS clickPv
        FROM
            pd_link_recode r
        LEFT JOIN
            click_report_hourly c ON DATE(r.link_date) = DATE(c.stat_date) AND r.tenant_id = c.tenant_id
        WHERE
            r.tenant_id = #{tenantId}
            AND r.config_type = #{queryType}
            AND r.link_date BETWEEN #{compareStartTime} AND #{compareEndTime}
        GROUP BY
            r.id, r.link_date, r.config_type, r.tenant_id, r.bounce_rate, r.conversion_rate,
            r.form_submissions, r.ctr, r.avg_stay_time, r.return_rate, r.completion_rate,
            r.first_screen_ctr, r.content_jump_rate, r.content_return_rate, r.click_depth,
            r.click_interval_time, r.engagement_score, r.page_rate, r.unique_query, r.top3_pv_ctr
        ORDER BY
            r.link_date ASC
    </select>

    <!-- 新增：计算平均值的联合查询 -->
    <select id="listRecodeWithClickAvg" resultType="java.util.Map">
        SELECT
            r.tenant_id AS tenantId,
            r.config_type AS configType,
            AVG(r.bounce_rate) AS bounceRate,
            AVG(r.conversion_rate) AS conversionRate,
            AVG(r.form_submissions) AS formSubmissions,
            AVG(r.ctr) AS ctr,
            AVG(r.avg_stay_time) AS avgStayTime,
            AVG(r.return_rate) AS returnRate,
            AVG(r.completion_rate) AS completionRate,
            AVG(r.first_screen_ctr) AS firstScreenCtr,
            AVG(r.content_jump_rate) AS contentJumpRate,
            AVG(r.content_return_rate) AS contentReturnRate,
            AVG(r.click_depth) AS clickDepth,
            AVG(r.click_interval_time) AS clickIntervalTime,
            AVG(r.engagement_score) AS engagementScore,
            AVG(r.page_rate) AS pageRate,
            AVG(r.unique_query) AS uniqueQuery,
            AVG(r.top3_pv_ctr) AS top3PvCtr,
            COALESCE(SUM(c.click_num), 0) AS clickNum,
            COALESCE(SUM(c.click_pv), 0) AS clickPv,
            COUNT(DISTINCT DATE(r.link_date)) AS dayCount
        FROM
            pd_link_recode r
        LEFT JOIN
            click_report_hourly c ON DATE(r.link_date) = DATE(c.stat_date) AND r.tenant_id = c.tenant_id
        WHERE
            r.tenant_id = #{tenantId}
            AND r.config_type = #{queryType}
            AND r.link_date BETWEEN #{startTime} AND #{endTime}
        GROUP BY
            r.tenant_id, r.config_type
    </select>

    <!-- 新增：计算对比周期平均值的联合查询 -->
    <select id="listCompareRecodeWithClickAvg" resultType="java.util.Map">
        SELECT
            r.tenant_id AS tenantId,
            r.config_type AS configType,
            AVG(r.bounce_rate) AS bounceRate,
            AVG(r.conversion_rate) AS conversionRate,
            AVG(r.form_submissions) AS formSubmissions,
            AVG(r.ctr) AS ctr,
            AVG(r.avg_stay_time) AS avgStayTime,
            AVG(r.return_rate) AS returnRate,
            AVG(r.completion_rate) AS completionRate,
            AVG(r.first_screen_ctr) AS firstScreenCtr,
            AVG(r.content_jump_rate) AS contentJumpRate,
            AVG(r.content_return_rate) AS contentReturnRate,
            AVG(r.click_depth) AS clickDepth,
            AVG(r.click_interval_time) AS clickIntervalTime,
            AVG(r.engagement_score) AS engagementScore,
            AVG(r.page_rate) AS pageRate,
            AVG(r.unique_query) AS uniqueQuery,
            AVG(r.top3_pv_ctr) AS top3PvCtr,
            COALESCE(SUM(c.click_num), 0) AS clickNum,
            COALESCE(SUM(c.click_pv), 0) AS clickPv,
            COUNT(DISTINCT DATE(r.link_date)) AS dayCount
        FROM
            pd_link_recode r
        LEFT JOIN
            click_report_hourly c ON DATE(r.link_date) = DATE(c.stat_date) AND r.tenant_id = c.tenant_id
        WHERE
            r.tenant_id = #{tenantId}
            AND r.config_type = #{queryType}
            AND r.link_date BETWEEN #{compareStartTime} AND #{compareEndTime}
        GROUP BY
            r.tenant_id, r.config_type
    </select>
</mapper>