package org.jeecg.modules.corp.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fasterxml.jackson.core.JsonProcessingException;
import org.jeecg.modules.corp.dto.DailyConfigDto;
import org.jeecg.modules.corp.entity.ClickAutoPre;
import org.jeecg.modules.corp.entity.DailyConfig;
import org.springframework.web.multipart.MultipartFile;

import java.text.ParseException;
import java.util.List;

/**
* 服务类
*
* <AUTHOR>
* @since 2025-04-11
*/
public interface IClickAutoPreService extends IService<ClickAutoPre> {
    /**
    * 新增
    */
    ClickAutoPre add(ClickAutoPre dto);

    /**
    * 修改
    */
    ClickAutoPre edit(ClickAutoPre dto);

    /**
    * 删除
    */
    void deleteById(String id);

    /**
    * 根据id获取 详情
    */
    ClickAutoPre queryById(String id);


    /**
    * 分页查询
    */
    IPage<ClickAutoPre> findPage(IPage<ClickAutoPre> page, ClickAutoPre dto);

    /**
    * 根据id获取 实体
    */
    ClickAutoPre checkEntity(String id);

    /**
     * 每日定时预生成方法，跑第二天的
     * @throws ParseException
     */
    void autoCreateClickPreAll() ;

    /**
     * 根据配置预生成数据
     * @param dailyConfig
     */
    void autoCreateClickPre(DailyConfig dailyConfig) throws JsonProcessingException;

    /**
     * 每日定时生成方法
     * @throws ParseException
     */
    void autoCreateClickAll() throws ParseException;

    void autoCreateClick(DailyConfig dailyConfig) throws ParseException;

    void importLedgers(List<MultipartFile> files, DailyConfigDto dto) throws JsonProcessingException;

    void uploadMultipleFiles(MultipartFile files);

    /**
     * 获取批量处理任务的进度信息
     * @return 包含总任务数、已完成任务数、剩余任务数和处理状态的Map
     */
    java.util.Map<String, Object> getBatchProcessProgress();

    /**
     * 异步处理租户配置
     * 从Redis队列中逐个取出租户配置并处理
     */
    void processTenantConfigsAsync();

    /**
     * 获取点击数预生成批量处理任务的进度信息
     * @return 包含总任务数、已完成任务数、剩余任务数和处理状态的Map
     */
    java.util.Map<String, Object> getClickPreBatchProcessProgress();
}


