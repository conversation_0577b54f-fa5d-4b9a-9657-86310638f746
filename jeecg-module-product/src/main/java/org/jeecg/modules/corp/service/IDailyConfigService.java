package org.jeecg.modules.corp.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.corp.entity.DailyConfig;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

/**
* 每日租户配置表服务类
*
* <AUTHOR>
* @since 2025-04-12
*/
public interface IDailyConfigService extends IService<DailyConfig> {
    /**
    * 新增每日租户配置表
    */
    DailyConfig add(DailyConfig dto);

    /**
    * 修改每日租户配置表
    */
    DailyConfig edit(DailyConfig dto);

    /**
    * 根据id获取每日租户配置表 详情
    */
    DailyConfig queryById(Integer tenantId);

    void uploadMultipleFile(MultipartFile file) throws Exception;
}


