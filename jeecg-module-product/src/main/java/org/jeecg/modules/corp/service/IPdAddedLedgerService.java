package org.jeecg.modules.corp.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.jeecg.modules.corp.dto.LedgerListDto;
import org.jeecg.modules.corp.entity.PdAddedLedger;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * @Description: 增值服务台账
 * @Author: jeecg-boot
 * @Date:   2024-11-25
 * @Version: V1.0
 */
public interface IPdAddedLedgerService extends IService<PdAddedLedger> {

    /**
     * 删除主子表
     * @param list
     */
    void deleteBatch(List<String> list);

    IPage<PdAddedLedger> pageList(Page<PdAddedLedger> page, LedgerListDto dto);
}
