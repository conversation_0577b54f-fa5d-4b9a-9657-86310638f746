package org.jeecg.modules.corp.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.jeecg.modules.corp.dto.LedgerListDto;
import org.jeecg.modules.corp.entity.PdAdded;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * @Description: 增值服务预约记录
 * @Author: jeecg-boot
 * @Date:   2024-11-14
 * @Version: V1.0
 */
public interface IPdAddedService extends IService<PdAdded> {

    IPage<PdAdded> pageList(Page<PdAdded> page, LedgerListDto dto);
}
