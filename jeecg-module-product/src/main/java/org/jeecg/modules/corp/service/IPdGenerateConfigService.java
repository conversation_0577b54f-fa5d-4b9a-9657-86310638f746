package org.jeecg.modules.corp.service;

import org.jeecg.modules.corp.entity.PdGenerateConfig;
import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.wechat.dto.config.CarInsuranceConfigVO;

import java.util.List;

/**
 * @Description: 生成规则配置
 * @Author: jeecg-boot
 * @Date:   2024-11-14
 * @Version: V1.0
 */
public interface IPdGenerateConfigService extends IService<PdGenerateConfig> {

    List<CarInsuranceConfigVO> getTenantConfig(List<Integer> tenantIds);
}
