package org.jeecg.modules.corp.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.jeecg.modules.corp.dto.LedgerListDto;
import org.jeecg.modules.corp.entity.PdInsuranceLedger;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * @Description: 财险台账
 * @Author: jeecg-boot
 * @Date:   2024-11-25
 * @Version: V1.0
 */
public interface IPdInsuranceLedgerService extends IService<PdInsuranceLedger> {
    /**
     * 批量删除
     * @param ids
     */
    void deleteBatch(List<String> ids);

    IPage<PdInsuranceLedger> pageList(Page<PdInsuranceLedger> page, LedgerListDto dto);
}
