package org.jeecg.modules.corp.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.jeecg.modules.corp.dto.DeleteByTypeDTO;
import org.jeecg.modules.corp.dto.LedgerListDto;
import org.jeecg.modules.corp.entity.PdLedger;
import com.baomidou.mybatisplus.extension.service.IService;

import java.time.LocalDate;

/**
 * @Description: 台账
 * @Author: jeecg-boot
 * @Date:   2024-11-08
 * @Version: V1.0
 */
public interface IPdLedgerService extends IService<PdLedger> {

    IPage<PdLedger> pageList(Page<PdLedger> page, LedgerListDto dto);

    /**
     * 根据类型、租户ID和时间范围批量删除数据
     * @param dto 删除参数DTO
     */
    void deleteByTypeAndDateRange(DeleteByTypeDTO dto);
}
