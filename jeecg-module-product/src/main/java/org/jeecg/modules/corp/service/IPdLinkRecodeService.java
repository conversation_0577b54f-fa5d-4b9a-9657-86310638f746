package org.jeecg.modules.corp.service;

import com.alibaba.fastjson.JSONObject;
import org.jeecg.modules.corp.dto.PdLinkRecodeDto;
import org.jeecg.modules.corp.entity.PdLinkRecode;
import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.corp.vo.PdLinkRecodeVo;

import java.util.Date;
import java.util.List;

/**
 * @Description: 链接快照
 * @Author: jeecg-boot
 * @Date:   2024-11-12
 * @Version: V1.0
 */
public interface IPdLinkRecodeService extends IService<PdLinkRecode> {

    JSONObject listRecode(PdLinkRecodeDto dto);

    /**
     * 根据租户ID和日期范围删除链接快照数据
     *
     * @param tenantId 租户ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 删除的记录数量
     */
     void deleteByTenantAndDateRange(Integer tenantId, Date startDate, Date endDate);

    /**
     * 批量补全链接快照数据
     * 根据租户ID和日期范围，先删除已有数据，然后重新生成
     *
     * @param tenantId 租户ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 生成的记录数量
     */
    void batchCompleteRecode(Integer tenantId, Date startDate, Date endDate);
}
