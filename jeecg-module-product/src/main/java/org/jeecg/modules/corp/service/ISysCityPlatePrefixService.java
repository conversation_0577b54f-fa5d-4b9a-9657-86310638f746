package org.jeecg.modules.corp.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.corp.entity.SysCityPlatePrefix;

/**
 * @Description: 城市车牌前缀关系表
 * @Author: jeecg-boot
 * @Date: 2024-11-12
 * @Version: V1.0
 */
public interface ISysCityPlatePrefixService extends IService<SysCityPlatePrefix> {

    /**
     * 根据城市编码查询车牌前缀
     * @param cityCode 城市编码
     * @return 车牌前缀
     */
    String getPlatePrefixByCityCode(String cityCode);

}
