package org.jeecg.modules.corp.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.corp.dto.DailyConfigContent;
import org.jeecg.modules.corp.dto.DailyConfigExcelDTO;
import org.jeecg.modules.corp.dto.PdInsuranceLedgerDTO;
import org.jeecg.modules.corp.entity.DailyConfig;
import org.jeecg.modules.corp.mapper.DailyConfigDao;
import org.jeecg.modules.corp.service.IDailyConfigService;
import org.jeecg.modules.corp.util.ExcelUtils;
import org.jeecg.modules.demo.produce.entity.ClickReport;
import org.jeecg.modules.wechat.entity.EaRegion;
import org.jeecg.modules.wechat.service.IEaRegionService;
import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 每日租户配置表服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-12
 */
@Service
@Slf4j
public class DailyConfigServiceImpl extends ServiceImpl<DailyConfigDao, DailyConfig> implements IDailyConfigService {

    @Autowired
    private ObjectMapper objectMapper;
    @Autowired
    private IEaRegionService eaRegionService;

    /**
    * 新增每日租户配置表
    *
    * @param dto 参数
    */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public DailyConfig add(DailyConfig dto){
        save(dto);
        return dto;
    }

    /**
    * 修改每日租户配置表
    *
    * @param dto 参数
    */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public DailyConfig edit(DailyConfig dto){
        updateById(dto);
        return dto;
    }


    /**
    * 根据id获取每日租户配置表 详情
    *
    * @param tenantId 主键
    */
    @Override
    public DailyConfig queryById(Integer tenantId){
        return lambdaQuery().eq(DailyConfig::getTenantId, tenantId).last("limit 1").one();
    }

    @Override
    public void uploadMultipleFile(MultipartFile file) throws Exception {

        List<DailyConfig> dailyConfigList = new ArrayList<>();
        // 2. 读取 Excel 内容，使用 ExcelImportUtil 进行导入
        InputStream inputStream = file.getInputStream();
        // 设置导入参数：跳过标题行，标题行行数为2行
        ImportParams params = new ImportParams();
        params.setTitleRows(2);  // 跳过前两行（标题行）
        params.setHeadRows(1);   // 第一行作为头行
        params.setNeedSave(true);

        // 读取 Excel 内容为 List<DailyConfigExcelDTO> 类型
        List<DailyConfigExcelDTO> rowList = ExcelImportUtil.importExcel(inputStream, DailyConfigExcelDTO.class, params);


        if (rowList.isEmpty()) {
            throw new IOException("导入的 Excel 文件没有数据");
        }

        List<Integer> tenantIdList = rowList.stream().map(DailyConfigExcelDTO::getTenantId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(tenantIdList)){
            return;
        }
        this.lambdaUpdate()
                .in(DailyConfig::getTenantId, tenantIdList)
                .remove();

        for (DailyConfigExcelDTO row : rowList) {
            DailyConfig config = new DailyConfig();
            config.setClickStart(row.getDailyClickStart());
            config.setClickEnd(row.getDailyClickEnd());
            config.setTenantId(row.getTenantId());

            Map<String, Object> configJson = new HashMap<>();

            // 车险
            Map<String, Double> carLedger = new HashMap<>();
            carLedger.put("ledgerStart", row.getCarInsuranceStart());
            carLedger.put("ledgerEnd", row.getCarInsuranceEnd());
            carLedger.put("chatUserStart", row.getChatStart());
            carLedger.put("chatUserEnd", row.getChatEnd());
            configJson.put("carLedger", carLedger);

            // 财险
            Map<String, Double> financeLedger = new HashMap<>();
            financeLedger.put("ledgerStart", row.getPropertyInsuranceStart());
            financeLedger.put("ledgerEnd", row.getPropertyInsuranceEnd());
            financeLedger.put("chatUserStart", row.getPropertyChatStart());
            financeLedger.put("chatUserEnd", row.getPropertyChatEnd());
            configJson.put("financeLedger", financeLedger);

            // 增值服务
            Map<String, Double> valueAddedLedger = new HashMap<>();
            valueAddedLedger.put("ledgerStart", row.getValueServiceStart());
            valueAddedLedger.put("ledgerEnd", row.getValueServiceEnd());
            valueAddedLedger.put("chatUserStart", row.getValueChatStart());
            valueAddedLedger.put("chatUserEnd", row.getValueChatEnd());
            configJson.put("valueAddedLedger", valueAddedLedger);
            // 城市配置
            if (row.getCities() != null && !row.getCities().isEmpty()) {
                // 将逗号分隔的城市字符串转换为List<String>类型，存储城市编码
                List<String> cityList = new ArrayList<>();

                // 按逗号分割城市名称
                String[] cityNames = row.getCities().split(",");
                Map<String, Object> cityCodeList = new HashMap<>();

                // 遍历每个城市名称，查询对应的城市编码
                for (String cityName : cityNames) {
                    // 去除可能的空格
                    cityName = cityName.trim();
                    if (!cityName.isEmpty()) {
                        // 根据城市名称模糊查询城市信息
                        List<EaRegion> regions = eaRegionService.findByCityNameLike(cityName);

                        // 如果找到匹配的城市，添加其编码到列表中
                        if (regions != null && !regions.isEmpty()) {
                            // 取第一个匹配的城市编码
                            String cityCode = regions.get(0).getCode();
                            cityList.add(cityCode);
                            log.info("城市 [{}] 匹配到编码: {}", cityName, cityCode);
                        } else {
                            log.warn("城市 [{}] 未找到匹配的编码", cityName);
                        }
                    }
                }
                configJson.put("cityList", cityList);
            }

            config.setConfigJson(JSON.toJSONString(configJson));
            dailyConfigList.add(config);

        }
        if (!dailyConfigList.isEmpty()) {
            saveBatch(dailyConfigList);
        }

    }


}
