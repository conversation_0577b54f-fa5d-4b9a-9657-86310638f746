package org.jeecg.modules.corp.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import org.jeecg.config.ProvinceIpGenerator;
import org.jeecg.modules.corp.mapper.PdChatUserMapper;
import org.jeecg.modules.corp.entity.PdChatUser;
import org.jeecg.modules.corp.service.IPdChatUserService;
import org.jeecg.modules.info.dto.PdBatchChatDto;
import org.jeecg.modules.info.entity.PdChatSource;
import org.jeecg.modules.info.entity.PdChatSourceDet;
import org.jeecg.modules.info.entity.PdGuestUsers;
import org.jeecg.modules.info.service.IPdChatSourceDetService;
import org.jeecg.modules.info.service.IPdChatSourceService;
import org.jeecg.modules.wechat.entity.PdChat;
import org.jeecg.modules.wechat.service.IPdChatService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ThreadLocalRandom;
import java.util.stream.Collectors;

/**
 * @Description: 聊天用户
 * @Author: jeecg-boot
 * @Date:   2024-11-09
 * @Version: V1.0
 */
@Service
public class PdChatUserServiceImpl extends ServiceImpl<PdChatUserMapper, PdChatUser> implements IPdChatUserService {

}
