package org.jeecg.modules.corp.service.impl;

import org.jeecg.modules.corp.entity.PdGenerateConfig;
import org.jeecg.modules.corp.mapper.PdGenerateConfigMapper;
import org.jeecg.modules.corp.service.IPdGenerateConfigService;
import org.jeecg.modules.wechat.dto.config.CarInsuranceConfigVO;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.util.List;

/**
 * @Description: 生成规则配置
 * @Author: jeecg-boot
 * @Date:   2024-11-14
 * @Version: V1.0
 */
@Service
public class PdGenerateConfigServiceImpl extends ServiceImpl<PdGenerateConfigMapper, PdGenerateConfig> implements IPdGenerateConfigService {

    @Override
    public List<CarInsuranceConfigVO> getTenantConfig(List<Integer> tenantIds) {
        return this.baseMapper.getTenantConfig(tenantIds);
    }
}
