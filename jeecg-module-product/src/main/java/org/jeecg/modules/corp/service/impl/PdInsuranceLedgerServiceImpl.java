package org.jeecg.modules.corp.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.modules.corp.dto.LedgerListDto;
import org.jeecg.modules.corp.entity.PdAdded;
import org.jeecg.modules.corp.entity.PdInsuranceLedger;
import org.jeecg.modules.corp.mapper.PdInsuranceLedgerMapper;
import org.jeecg.modules.corp.service.IPdInsuranceLedgerService;
import org.jeecg.modules.info.entity.PdCasualtyInfo;
import org.jeecg.modules.info.service.IPdCasualtyInfoService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * @Description: 财险台账
 * @Author: jeecg-boot
 * @Date:   2024-11-25
 * @Version: V1.0
 */
@Service
public class PdInsuranceLedgerServiceImpl extends ServiceImpl<PdInsuranceLedgerMapper, PdInsuranceLedger> implements IPdInsuranceLedgerService {
    @Resource
    private IPdCasualtyInfoService casualtyInfoService;

    @Override
    public void deleteBatch(List<String> ids) {
        if (CollectionUtil.isEmpty(ids)) {
            return;
        }
        //1.删除子表对应的 pid 为 list
        casualtyInfoService.remove(new LambdaQueryWrapper<PdCasualtyInfo>().in(PdCasualtyInfo::getLedgerId, ids));
        //删除主表
        this.removeByIds(ids);
    }

    @Override
    public IPage<PdInsuranceLedger> pageList(Page<PdInsuranceLedger> page, LedgerListDto dto) {
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();

        // 检查用户是否有权限查看所有租户数据
        boolean hasAllDataPermission = sysUser != null && sysUser.getAllCheck() != null && sysUser.getAllCheck() == 1;

        // 如果用户有权限查看所有租户数据，则不需要限制租户ID
        if (!hasAllDataPermission) {
            // 用户没有权限查看所有租户数据，按照原来的逻辑限制租户ID
            List<String> tenantIds = Arrays.asList(sysUser.getRelTenantIds().split(","));
            //需要查询用户是否有这个租户的权限
            if (!StrUtil.isEmpty(dto.getTenantId())) {
                // 如果传入的租户ID不在当前用户的租户权限中，则抛出异常或返回错误
                if (!tenantIds.contains(dto.getTenantId())) {
                    dto.setTenantId(null);
                }
                // 如果用户有该租户权限，将其加入dto
                dto.setTenantIds(Collections.singletonList(dto.getTenantId()));
            } else {
                // 如果没有传入租户ID，使用当前用户的所有租户权限
                dto.setTenantIds(tenantIds);
            }
        } else {
            // 用户有权限查看所有租户数据
            // 如果传入了特定的租户ID，则只查询该租户的数据
            if (!StrUtil.isEmpty(dto.getTenantId())) {
                dto.setTenantIds(Collections.singletonList(dto.getTenantId()));
            } else {
                // 如果没有传入租户ID，则不限制租户ID，查询所有租户的数据
                dto.setTenantIds(null);
            }
        }
        if (StrUtil.isNotEmpty(dto.getDateRange())) {
            //将时间字段拆分dateRange
            String[] split = dto.getDateRange().split(",");
            dto.setStartDate(split[0]);
            dto.setEndDate(split[1]);
        }
        return this.baseMapper.pageList(page,dto);
    }
}
