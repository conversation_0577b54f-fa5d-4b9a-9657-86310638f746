package org.jeecg.modules.corp.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.util.SpringContextUtils;
import org.jeecg.common.util.TokenUtils;
import org.jeecg.modules.corp.dto.PdLinkRecodeDto;
import org.jeecg.modules.corp.entity.PdLinkInfo;
import org.jeecg.modules.corp.entity.PdLinkRecode;
import org.jeecg.modules.corp.mapper.ClickReportHourlyMapper;
import org.jeecg.modules.corp.mapper.PdLinkRecodeMapper;
import org.jeecg.modules.corp.service.IPdGenerateConfigService;
import org.jeecg.modules.corp.service.IPdLinkInfoService;
import org.jeecg.modules.corp.service.IPdLinkRecodeService;
import org.jeecg.modules.corp.vo.PdLinkRecodeVo;
import org.jeecg.modules.info.service.IPdCarInfoService;
import org.jeecg.modules.info.service.IPdCasualtyInfoService;
import org.jeecg.modules.info.util.RandomRangeUtil;
import org.jeecg.modules.system.entity.SysTenant;
import org.jeecg.modules.system.service.ISysTenantService;
import org.jeecg.modules.wechat.dto.config.CarInsuranceConfigVO;
import org.jeecg.modules.wechat.service.ISysDeployConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Date;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.TreeSet;
import java.util.stream.Collectors;

/**
 * @Description: 链接快照
 * @Author: jeecg-boot
 * @Date:   2024-11-12
 * @Version: V1.0
 */
@Service
@Slf4j
public class PdLinkRecodeServiceImpl extends ServiceImpl<PdLinkRecodeMapper, PdLinkRecode> implements IPdLinkRecodeService {
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Autowired
    private ISysTenantService sysTenantService;

    @Autowired
    private IPdGenerateConfigService pdGenerateConfigService;

    @Autowired
    private ISysDeployConfigService sysDeployConfigService;

    @Autowired
    private ClickReportHourlyMapper clickReportHourlyDao;

    // 浮动百分比，控制变化幅度
    private static final double FLUCTUATION_PERCENT = 0.05;
    // 小数位精度
    private static final int SCALE = 2;

    @Override
    public JSONObject listRecode(PdLinkRecodeDto dto) {
        // 获取查询参数
        String startTime = dto.getStartTime();
        String endTime = dto.getEndTime();
        Integer queryType = dto.getQueryType();
        Integer tenantId = dto.getTenantId();

        // 解析日期
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        LocalDateTime startDateTime = LocalDateTime.parse(startTime, formatter);
        LocalDateTime endDateTime = LocalDateTime.parse(endTime, formatter);
        
        // 转换为java.sql.Date用于数据库查询
        java.sql.Date startDate = java.sql.Date.valueOf(startDateTime.toLocalDate());
        java.sql.Date endDate = java.sql.Date.valueOf(endDateTime.toLocalDate());
        
        // 构建查询条件
        Map<String, Object> params = new HashMap<>();
        params.put("startDate", startDate);
        params.put("endDate", endDate);
        params.put("configType", queryType);
        
        // 如果传入了指定租户ID，则按租户查询
        if (tenantId != null && tenantId > 0) {
            params.put("tenantId", tenantId);
        } else {
            // 获取当前登录用户的租户ID
            LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
            if (sysUser != null) {
                // 从登录用户中获取租户ID
                try {
                    // 尝试从对象中获取租户ID（通常命名不同，可能是tenantId或relTenantIds）
                    Integer loginTenantId = null;
                    
                    // 通过反射获取LoginUser的tenant相关字段
                    try {
                        java.lang.reflect.Method method = sysUser.getClass().getMethod("getRelTenantIds");
                        Object tenantIds = method.invoke(sysUser);
                        if (tenantIds != null) {
                            String tenantIdStr = tenantIds.toString();
                            if (tenantIdStr.contains(",")) {
                                // 如果是多个租户ID，取第一个
                                tenantIdStr = tenantIdStr.split(",")[0];
                            }
                            loginTenantId = Integer.parseInt(tenantIdStr);
                        }
                    } catch (Exception e1) {
                        try {
                            // 尝试获取其他可能的租户ID字段
                            java.lang.reflect.Field field = sysUser.getClass().getDeclaredField("tenantId");
                            field.setAccessible(true);
                            Object fieldValue = field.get(sysUser);
                            if (fieldValue != null) {
                                loginTenantId = Integer.parseInt(fieldValue.toString());
                            }
                        } catch (Exception e2) {
                            // 忽略错误，使用系统默认值或当前操作者设置的值
                        }
                    }

                    if (loginTenantId != null && loginTenantId > 0) {
                        params.put("tenantId", loginTenantId);
                    }
                } catch (Exception e) {
                    // 当获取租户ID出错时，忽略错误，不设置租户ID条件
                    log.warn("获取当前登录用户租户ID失败: {}", e.getMessage());
                }
            }
        }
        
        // 查询日期区间内的数据
        List<Map<String, Object>> clickStats = clickReportHourlyDao.getClickStatsByDateRange(params);
        
        // 处理日期列表，作为X轴数据
        List<String> dateList = new ArrayList<>();
        LocalDate current = startDateTime.toLocalDate();
        while (!current.isAfter(endDateTime.toLocalDate())) {
            dateList.add(current.format(DateTimeFormatter.ofPattern("MM-dd")));
            current = current.plusDays(1);
        }
        
        // 初始化PV和UV数据，与日期列表一一对应
        List<Integer> pvData = new ArrayList<>(dateList.size());
        List<Integer> uvData = new ArrayList<>(dateList.size());
        
        // 填充初始值为0
        for (int i = 0; i < dateList.size(); i++) {
            pvData.add(0);
            uvData.add(0);
        }
        
        // 根据查询结果填充数据
        if (clickStats != null && !clickStats.isEmpty()) {
            for (Map<String, Object> stat : clickStats) {
                Date statDate = (Date) stat.get("stat_date");
                if (statDate != null) {
                    String dateStr = new SimpleDateFormat("MM-dd").format(statDate);
                    int index = dateList.indexOf(dateStr);
                    
                    if (index >= 0) {
                        // 更新PV数据
                        Object pvObj = stat.get("total_pv");
                        if (pvObj != null) {
                            Integer pv = Integer.valueOf(pvObj.toString());
                            pvData.set(index, pv);
                        }
                            
                        // 更新UV数据
                        Object uvObj = stat.get("total_uv");
                        if (uvObj != null) {
                            Integer uv = Integer.valueOf(uvObj.toString());
                            uvData.set(index, uv);
                        }
                    }
                }
            }
        }
        
        // 构建返回数据
        JSONObject result = new JSONObject();
        
        // 设置X轴数据
        result.put("xAxis", dateList);
        
        // 设置曲线数据
        JSONArray series = new JSONArray();
        
        // PV曲线
        JSONObject pvSeries = new JSONObject();
        pvSeries.put("type", "pvNum");
        pvSeries.put("data", pvData);
        series.add(pvSeries);
        
        // UV曲线
        JSONObject uvSeries = new JSONObject();
        uvSeries.put("type", "uvNum");
        uvSeries.put("data", uvData);
        series.add(uvSeries);
        
        result.put("series", series);
        return result;
    }



    /**
     * 根据租户ID和日期范围删除链接快照数据
     *
     * @param tenantId 租户ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 删除的记录数量
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteByTenantAndDateRange(Integer tenantId, java.util.Date startDate, java.util.Date endDate) {
        if (tenantId == null || startDate == null || endDate == null) {
            return ;
        }

        // 构建删除条件
        LambdaQueryWrapper<PdLinkRecode> deleteWrapper = new LambdaQueryWrapper<>();
        deleteWrapper.eq(PdLinkRecode::getTenantId, tenantId)
                .ge(PdLinkRecode::getLinkDate, startDate)
                .le(PdLinkRecode::getLinkDate, endDate);

        // 先查询符合条件的记录数量
        this.count(deleteWrapper);

        // 执行删除操作
        this.remove(deleteWrapper);
    }

    /**
     * 批量补全链接快照数据
     * 根据租户ID和日期范围，先删除已有数据，然后重新生成
     *
     * @param tenantId 租户ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 生成的记录数量
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchCompleteRecode(Integer tenantId, java.util.Date startDate, java.util.Date endDate) {
        if (tenantId == null || startDate == null || endDate == null) {
            return ;
        }

        try {
            // 1. 先删除指定租户和日期范围内的已有数据
            this.deleteByTenantAndDateRange(tenantId, startDate, endDate);

            // 2. 获取租户信息
            SysTenant tenant = sysTenantService.getById(tenantId);
            if (tenant == null) {
                return ;
            }

            // 3. 获取租户配置
            List<CarInsuranceConfigVO> configList = pdGenerateConfigService.getTenantConfig(Arrays.asList(tenantId));

            // 按照配置类型分组
            Map<Integer, CarInsuranceConfigVO> tenantConfigMap = new HashMap<>();
            if (!CollectionUtil.isEmpty(configList)) {
                for (CarInsuranceConfigVO config : configList) {
                    tenantConfigMap.put(config.getConfigType(), config);
                }
            }

            // 4. 获取默认配置
            Map<Integer, CarInsuranceConfigVO> defaultConfigMap = new HashMap<>();
            // 0-车险、1-财险、2-增值服务
            List<CarInsuranceConfigVO> defaultConfigs = new ArrayList<>();

            // 车险默认配置
            CarInsuranceConfigVO carConfig = sysDeployConfigService.getDeployConfigByType(2, CarInsuranceConfigVO.class);
            if (carConfig != null) {
                carConfig.setConfigType(0); // 确保类型正确
                defaultConfigs.add(carConfig);
            }

            // 财险默认配置
            CarInsuranceConfigVO propertyConfig = sysDeployConfigService.getDeployConfigByType(3, CarInsuranceConfigVO.class);
            if (propertyConfig != null && ObjectUtil.isNotEmpty(propertyConfig.getAvgStayTimeEnd())) {
                propertyConfig.setConfigType(1);
                defaultConfigs.add(propertyConfig);
            }

            // 增值服务默认配置
            CarInsuranceConfigVO valueAddedConfig = sysDeployConfigService.getDeployConfigByType(4, CarInsuranceConfigVO.class);
            if (valueAddedConfig != null && ObjectUtil.isNotEmpty(valueAddedConfig.getAvgStayTimeEnd())) {
                valueAddedConfig.setConfigType(2);
                defaultConfigs.add(valueAddedConfig);
            }

            // 转换为Map
            if (!defaultConfigs.isEmpty()) {
                for (CarInsuranceConfigVO config : defaultConfigs) {
                    defaultConfigMap.put(config.getConfigType(), config);
                }
            }

            // 5. 获取租户的业务类型
            String busType = tenant.getBusType();
            List<Integer> busTypeList = new ArrayList<>();
            if (StrUtil.isNotBlank(busType)) {
                // 将业务类型字符串转换为列表
                busTypeList = Arrays.stream(busType.split(","))
                        .map(Integer::parseInt)
                        .collect(Collectors.toList());
            } else {
                // 如果租户没有设置业务类型，默认处理所有类型
                busTypeList.add(0); // 车险
                busTypeList.add(1); // 财险
                busTypeList.add(2); // 增值服务
            }

            // 6. 遍历日期范围，为每一天生成数据
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(startDate);
            Calendar endCalendar = Calendar.getInstance();
            endCalendar.setTime(endDate);

            while (!calendar.after(endCalendar)) {
                java.util.Date currentDate = DateUtil.beginOfDay(calendar.getTime());

                // 处理每个业务类型
                for (Integer configType : busTypeList) {
                    // 获取该类型的配置
                    CarInsuranceConfigVO config = null;

                    // 首先尝试获取租户特定的配置
                    if (tenantConfigMap.containsKey(configType)) {
                        config = tenantConfigMap.get(configType);
                    }

                    // 如果没有租户特定配置，使用默认配置
                    if (config == null && defaultConfigMap.containsKey(configType)) {
                        config = defaultConfigMap.get(configType);
                    }

                    if (config == null) {
                        continue; // 跳过没有配置的类型
                    }

                    // 创建新记录
                    PdLinkRecode newRecode = new PdLinkRecode();
                    newRecode.setTenantId(tenantId);
                    newRecode.setLinkDate(currentDate);
                    newRecode.setUpdateTime(new java.util.Date());
                    newRecode.setConfigType(configType);

                    // 设置初始值，从配置的区间中随机生成
                    setRandomValuesFromConfig(newRecode, config);

                    // 保存新记录
                    this.save(newRecode);
                }

                // 移动到下一天
                calendar.add(Calendar.DAY_OF_MONTH, 1);
            }
        } catch (Exception e) {
            throw new RuntimeException("批量补全链接快照数据失败", e);
        }
    }

    /**
     * 从配置区间中随机设置值
     */
    private void setRandomValuesFromConfig(PdLinkRecode recode, CarInsuranceConfigVO config) {
        // 跳出率
        recode.setBounceRate(RandomRangeUtil.randomPercent(
                config.getBounceRateStart(), config.getBounceRateEnd(), SCALE));

        // 转化率
        recode.setConversionRate(RandomRangeUtil.randomPercent(
                config.getConversionRateStart(), config.getConversionRateEnd(), SCALE));

        // 表单提交数
        recode.setFormSubmissions(RandomRangeUtil.randomInRange(
                config.getFormSubmissionsStart(), config.getFormSubmissionsEnd()));

        // 点击率
        recode.setCtr(RandomRangeUtil.randomPercent(
                config.getCtrStart(), config.getCtrEnd(), SCALE));

        // 平均停留时长
        recode.setAvgStayTime(RandomRangeUtil.randomInRange(
                config.getAvgStayTimeStart(), config.getAvgStayTimeEnd()));

        // 返回率
        recode.setReturnRate(RandomRangeUtil.randomPercent(
                config.getReturnRateStart(), config.getReturnRateEnd(), SCALE));

        // 内容完成率
        recode.setCompletionRate(RandomRangeUtil.randomPercent(
                config.getCompletionRateStart(), config.getCompletionRateEnd(), SCALE));

        // 首屏CTR值
        recode.setFirstScreenCtr(RandomRangeUtil.randomPercent(
                config.getFirstScreenCtrStart(), config.getFirstScreenCtrEnd(), SCALE));

        // 内容Jump率
        recode.setContentJumpRate(RandomRangeUtil.randomPercent(
                config.getContentJumpRateStart(), config.getContentJumpRateEnd(), SCALE));

        // 内容Return率
        recode.setContentReturnRate(RandomRangeUtil.randomPercent(
                config.getContentReturnRateStart(), config.getContentReturnRateEnd(), SCALE));

        // Click深度分布
        recode.setClickDepth(RandomRangeUtil.randomInRange(
                config.getClickDepthStart(), config.getClickDepthEnd(), SCALE));

        // Click间隔时间
        recode.setClickIntervalTime(RandomRangeUtil.randomInRange(
                config.getClickIntervalTimeStart(), config.getClickIntervalTimeEnd(), SCALE));

        // Engagement得分
        recode.setEngagementScore(RandomRangeUtil.randomInRange(
                config.getEngagementScoreStart(), config.getEngagementScoreEnd(), SCALE));

        // 翻页率比例 (注意这里的字段名与其他不同)
        recode.setPageRate(RandomRangeUtil.randomPercent(
                new BigDecimal(config.getPageStartNum()), new BigDecimal(config.getPageEndNum()), SCALE));

        // 独立Query数
        recode.setUniqueQuery(RandomRangeUtil.randomInRange(
                config.getUniqueQueryStart(), config.getUniqueQueryEnd()));

        // TOP3 PV-CTR
        recode.setTop3PvCtr(RandomRangeUtil.randomPercent(
                config.getTop3PvCtrStart(), config.getTop3PvCtrEnd(), SCALE));
    }
}
