package org.jeecg.modules.corp.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.jeecg.modules.corp.entity.SysCityPlatePrefix;
import org.jeecg.modules.corp.mapper.SysCityPlatePrefixMapper;
import org.jeecg.modules.corp.service.ISysCityPlatePrefixService;
import org.jeecg.modules.wechat.entity.EaRegion;
import org.jeecg.modules.wechat.service.IEaRegionService;

import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * @Description: 城市车牌前缀关系表
 * @Author: jeecg-boot
 * @Date: 2024-11-12
 * @Version: V1.0
 */
@Service
@Slf4j
public class SysCityPlatePrefixServiceImpl extends ServiceImpl<SysCityPlatePrefixMapper, SysCityPlatePrefix> implements ISysCityPlatePrefixService {


    @Override
    public String getPlatePrefixByCityCode(String cityCode) {
        return baseMapper.getPlatePrefixByCityCode(cityCode);
    }


}
