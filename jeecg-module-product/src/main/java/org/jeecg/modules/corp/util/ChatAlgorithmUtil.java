package org.jeecg.modules.corp.util;

import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.google.gson.Gson;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import org.apache.commons.lang3.RandomUtils;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.modules.wechat.config.PdChatConfig;
import org.jeecg.modules.wechat.service.IPdChatConfigService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ThreadLocalRandom;
import java.util.stream.Collectors;

@Component
public class ChatAlgorithmUtil {
    @Resource
    private IPdChatConfigService chatConfigService;
    
    private final Gson gson = new Gson();
    
    // 记录已生成组合的频率，用于降低重复率
    private final Map<String, Integer> generatedCombinations = new HashMap<>();
    
    /**
     * 默认配置类，提供代码中的默认属性值配置
     */
    private static class DefaultChatConfigs {
        // 默认属性值映射表
        private static final Map<String, List<String>> DEFAULT_ATTRIBUTES = new HashMap<>();
        
        // 静态初始化块
        static {
            // 聊天基本属性
            DEFAULT_ATTRIBUTES.put("chatLengthRate", Arrays.asList("1回合", "2回合", "3回合", "4回合", "5回合以上"));
            DEFAULT_ATTRIBUTES.put("chatTitle", Arrays.asList("车损险", "交强险", "商业险", "第三者责任险", "车上人员险", "盗抢险", "自燃险", "玻璃单独破碎险"));
            DEFAULT_ATTRIBUTES.put("chatMoodRate", Arrays.asList("困惑", "焦虑", "平静", "满意", "好奇", "谨慎", "急切"));
            
            // 用户相关属性
            DEFAULT_ATTRIBUTES.put("userType", Arrays.asList("新车主", "老车主", "出险车主", "企业用户"));
            DEFAULT_ATTRIBUTES.put("userKnowledgeLevel", Arrays.asList("完全不懂", "初级", "中级", "专业"));
            DEFAULT_ATTRIBUTES.put("userPriceAttention", Arrays.asList("极高", "高", "中等", "低", "不关注"));
            DEFAULT_ATTRIBUTES.put("userDecisionStage", Arrays.asList("浏览阶段", "比较阶段", "即将购买", "已购买有疑问", "续保考虑中"));
            
            // 车辆相关属性
            DEFAULT_ATTRIBUTES.put("carType", Arrays.asList("家用轿车", "SUV", "新能源车"));
            DEFAULT_ATTRIBUTES.put("carValue", Arrays.asList("豪华", "高档", "中档", "经济型", "入门级"));
            DEFAULT_ATTRIBUTES.put("carAge", Arrays.asList("新车", "1-3年", "3-6年", "6年以上"));
            DEFAULT_ATTRIBUTES.put("carUsage", Arrays.asList("日常通勤", "商务用车", "家庭出行", "网约车"));
            
            // 场景相关属性
            DEFAULT_ATTRIBUTES.put("scenarioDetail", Arrays.asList("刚买新车", "车辆出险", "保险到期", "车辆贷款", "异地用车"));
            DEFAULT_ATTRIBUTES.put("concernPoint", Arrays.asList("保障范围", "价格", "理赔流程", "免赔政策", "附加服务", "其他服务"));
            DEFAULT_ATTRIBUTES.put("timeConstraint", Arrays.asList("紧急", "常规", "从容", "无限制"));
            DEFAULT_ATTRIBUTES.put("locationFactor", Arrays.asList("一线城市", "二线城市", "三四线城市", "农村地区", "高风险地区"));
            
            // 咨询内容相关属性
            DEFAULT_ATTRIBUTES.put("focusArea", Arrays.asList("基础咨询", "价格比较", "保障详情", "理赔流程", "特殊需求"));
            DEFAULT_ATTRIBUTES.put("detailLevel", Arrays.asList("简略", "标准", "详细", "专业"));
            DEFAULT_ATTRIBUTES.put("questionComplexity", Arrays.asList("简单", "中等", "复杂", "专业"));
            DEFAULT_ATTRIBUTES.put("followupType", Arrays.asList("追问细节", "顾虑表达", "价格确认", "保障确认", "无"));
            
            // 交互方式相关属性
            DEFAULT_ATTRIBUTES.put("interactionStyle", Arrays.asList("谨慎", "直接", "友好", "专业", "随意"));
            DEFAULT_ATTRIBUTES.put("questionStyle", Arrays.asList("开放式", "封闭式", "混合式"));
            DEFAULT_ATTRIBUTES.put("responsePreference", Arrays.asList("简洁", "标准", "详细", "专业术语"));
            DEFAULT_ATTRIBUTES.put("emotionalTone", Arrays.asList("积极", "中性", "担忧", "不满", "满意"));
            
            // 其他因素
            DEFAULT_ATTRIBUTES.put("seasonalFactor", Arrays.asList("常规", "春节", "暑假", "年末", "促销期"));
            DEFAULT_ATTRIBUTES.put("eventTrigger", Arrays.asList("无", "政策变动", "价格调整", "车辆出险", "促销活动"));
        }
        
        /**
         * 获取某个属性的默认配置值
         */
        public static List<String> getDefaultValues(String attributeKey) {
            return DEFAULT_ATTRIBUTES.getOrDefault(attributeKey, Collections.emptyList());
        }
        
        /**
         * 判断是否有默认配置
         */
        public static boolean hasDefaultConfig(String attributeKey) {
            return DEFAULT_ATTRIBUTES.containsKey(attributeKey);
        }
        
        /**
         * 获取所有默认配置的属性键
         */
        public static Set<String> getAllKeys() {
            return DEFAULT_ATTRIBUTES.keySet();
        }
    }
    
    /**
     * 主方法：生成指定数量的多样化聊天配置
     * @return 生成的聊天配置JSON列表
     */
    public List<String> generateDiverseChatConfigs() {
        // 1. 获取数据库配置
        PdChatConfig pdChatConfig = chatConfigService.lambdaQuery().eq(PdChatConfig::getSettingType, 1).one();
        String settingJson = pdChatConfig.getSettingJson();
        Integer numberGenerated = pdChatConfig.getNumberGenerated();
        
        // 2. 解析配置JSON
        JsonObject templateConfig = JsonParser.parseString(settingJson).getAsJsonObject();
        Map<String, List<String>> dbAttributes = parseAttributes(templateConfig);
        
        // 3. 合并默认配置与数据库配置
        Map<String, List<String>> attributesMap = mergeConfigurations(dbAttributes);
        
        // 4. 生成多样化配置
        List<String> results = new ArrayList<>(numberGenerated);
        for (int i = 0; i < numberGenerated; i++) {
            JsonObject chatConfig = generateChatConfig(attributesMap, i, numberGenerated);
            results.add(gson.toJson(chatConfig));
        }
        
        // 5. 重置状态，以备下次使用
        generatedCombinations.clear();
        
        return results;
    }
    
    /**
     * 解析JSON模板中的所有可用属性
     */
    private Map<String, List<String>> parseAttributes(JsonObject template) {
        Map<String, List<String>> attributesMap = new HashMap<>();
        
        for (Map.Entry<String, JsonElement> entry : template.entrySet()) {
            String key = entry.getKey();
            JsonElement element = entry.getValue();
            
            // 如果是数组，提取所有可能的值
            if (element != null && element.isJsonArray()) {
                List<String> values = new ArrayList<>();
                element.getAsJsonArray().forEach(item -> {
                    if (!item.isJsonNull()) {
                        values.add(item.getAsString());
                    }
                });
                if (!values.isEmpty()) {
                    attributesMap.put(key, values);
                }
            }
        }
        
        return attributesMap;
    }
    
    /**
     * 合并数据库配置与默认配置
     */
    private Map<String, List<String>> mergeConfigurations(Map<String, List<String>> dbAttributes) {
        Map<String, List<String>> mergedAttributes = new HashMap<>(dbAttributes);
        
        // 策略1: 添加默认配置中存在但数据库配置中不存在的属性
        for (String key : DefaultChatConfigs.getAllKeys()) {
            if (!mergedAttributes.containsKey(key) || mergedAttributes.get(key) == null || mergedAttributes.get(key).isEmpty()) {
                mergedAttributes.put(key, DefaultChatConfigs.getDefaultValues(key));
            }
        }
        
        // 策略2: 扩展数据库配置中已存在但不完整的属性值集合
        // 注意: 这里可根据业务需求决定是否启用此策略
        // 当前设置为不启用，保持数据库配置的优先级
        
        return mergedAttributes;
    }
    
    /**
     * 生成单个聊天配置JSON
     */
    private JsonObject generateChatConfig(Map<String, List<String>> attributesMap, int currentIndex, int totalNeeded) {
        JsonObject result = new JsonObject();
        boolean thankAtEnd = ThreadLocalRandom.current().nextBoolean();
        result.addProperty("thankAtEndRate", thankAtEnd);
        
        // 选择属性组合
        Map<String, String> selectedAttributes = selectAttributes(attributesMap, currentIndex, totalNeeded);
        
        // 将选择的属性添加到结果JSON
        for (Map.Entry<String, String> entry : selectedAttributes.entrySet()) {
            result.addProperty(entry.getKey(), entry.getValue());
        }
        
        return result;
    }
    
    /**
     * 智能选择属性组合，确保多样性
     */
    private Map<String, String> selectAttributes(Map<String, List<String>> attributesMap, int currentIndex, int totalNeeded) {
        Map<String, String> selected = new HashMap<>();
        
        // 基于当前生成进度调整选择策略
        double progress = (double) currentIndex / totalNeeded;
        
        for (Map.Entry<String, List<String>> entry : attributesMap.entrySet()) {
            String key = entry.getKey();
            List<String> values = entry.getValue();
            
            if (values == null || values.isEmpty()) {
                continue;
            }
            
            // 根据已生成组合的频率调整选择概率
            String value = selectValueWithBias(key, values, progress);
            selected.put(key, value);
            
            // 记录此属性值的使用，用于后续偏好计算
            String combinationKey = key + ":" + value;
            generatedCombinations.put(combinationKey, generatedCombinations.getOrDefault(combinationKey, 0) + 1);
        }
        
        // 确保某些相关属性之间的一致性
        applyContextualConstraints(selected);
        
        return selected;
    }
    
    /**
     * 带偏好的属性值选择，降低高频组合的出现概率
     */
    private String selectValueWithBias(String key, List<String> values, double progress) {
        if (values.size() == 1) {
            return values.get(0);
        }
        
        // 根据已使用频率为每个值分配权重
        List<Double> weights = new ArrayList<>(values.size());
        double totalWeight = 0;
        
        for (String value : values) {
            String combinationKey = key + ":" + value;
            int frequency = generatedCombinations.getOrDefault(combinationKey, 0);
            
            // 初期随机性高，后期更偏向低频选项
            double weight = Math.max(0.1, 1.0 - (frequency * (0.2 + progress * 0.5)));
            weights.add(weight);
            totalWeight += weight;
        }
        
        // 根据权重随机选择
        double randomValue = Math.random() * totalWeight;
        double cumulativeWeight = 0;
        
        for (int i = 0; i < weights.size(); i++) {
            cumulativeWeight += weights.get(i);
            if (randomValue <= cumulativeWeight) {
                return values.get(i);
            }
        }
        
        // 默认返回最后一个
        return values.get(values.size() - 1);
    }
    
    /**
     * 应用上下文约束，确保属性组合的合理性
     * 注意：这里的约束规则也可以从数据库获取，以支持动态配置
     */
    private void applyContextualConstraints(Map<String, String> selected) {
        // 约束1: 新车主往往关注保障范围和价格
        if ("新车主".equals(selected.get("userType"))) {
            if (Math.random() < 0.7) {
                if (selected.containsKey("concernPoint")) {
                    List<String> newCarFocus = Arrays.asList("保障范围", "价格");
                    selected.put("concernPoint", newCarFocus.get(RandomUtils.nextInt(0, newCarFocus.size())));
                }
            }
        }
        
        // 约束2: 车辆出险场景通常与理赔流程相关
        if ("车辆出险".equals(selected.get("scenarioDetail"))) {
            if (Math.random() < 0.8) {
                if (selected.containsKey("concernPoint")) {
                    selected.put("concernPoint", "理赔流程");
                }
                if (selected.containsKey("emotionalTone")) {
                    List<String> anxiousTones = Arrays.asList("担忧", "不满", "焦虑");
                    selected.put("emotionalTone", anxiousTones.get(RandomUtils.nextInt(0, anxiousTones.size())));
                }
            }
        }
        
        // 约束3: 价格关注度高的用户通常关心价格
        if ("高".equals(selected.get("userPriceAttention")) || "极高".equals(selected.get("userPriceAttention"))) {
            if (Math.random() < 0.75) {
                if (selected.containsKey("concernPoint")) {
                    selected.put("concernPoint", "价格");
                }
            }
        }
        
        // 约束4: 专业知识水平高的用户通常会提出复杂问题
        if ("专业".equals(selected.get("userKnowledgeLevel"))) {
            if (Math.random() < 0.8) {
                if (selected.containsKey("questionComplexity")) {
                    selected.put("questionComplexity", "复杂");
                }
                if (selected.containsKey("detailLevel")) {
                    selected.put("detailLevel", "详细");
                }
            }
        }
        
        // 约束5: 商务用车通常与企业用户相关
        if ("商务用车".equals(selected.get("carUsage"))) {
            if (Math.random() < 0.6) {
                if (selected.containsKey("userType")) {
                    selected.put("userType", "企业用户");
                }
            }
        }
    }
    
    /**
     * 添加新的属性配置到默认配置中
     * 注意：此方法仅在内存中添加，不会持久化到数据库
     * @param attributeKey 属性键
     * @param values 属性可选值列表
     */
    public void addAttributeConfig(String attributeKey, List<String> values) {
        if (StringUtils.isBlank(attributeKey) || values == null || values.isEmpty()) {
            return;
        }
        
        // 在下一次生成时，这些配置会被合并
        DefaultChatConfigs.DEFAULT_ATTRIBUTES.put(attributeKey, values);
    }
    
    /**
     * 批量添加多个属性配置到默认配置中
     * @param attributesMap 属性配置映射
     */
    public void addAttributeConfigs(Map<String, List<String>> attributesMap) {
        if (attributesMap == null || attributesMap.isEmpty()) {
            return;
        }
        
        for (Map.Entry<String, List<String>> entry : attributesMap.entrySet()) {
            addAttributeConfig(entry.getKey(), entry.getValue());
        }
    }
    
    /**
     * 对外提供的主方法
     * @return 生成的多样化聊天配置JSON列表
     */
    public List<String> randomlyGenerated() {
        return generateDiverseChatConfigs();
    }
}
