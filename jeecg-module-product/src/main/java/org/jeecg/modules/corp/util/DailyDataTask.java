package org.jeecg.modules.corp.util;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Random;
import java.util.concurrent.TimeUnit;

@Component
public class DailyDataTask {

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    private final Map<String, String> keyMapping = new LinkedHashMap<>();

    public DailyDataTask() {
        keyMapping.put("合作公司", "partners");
        keyMapping.put("产品数", "products");
        keyMapping.put("运营指标", "metrics");
        keyMapping.put("独立Query数", "uniqueQuery");
        keyMapping.put("TOP3 PV-CTR", "top3PvCtr");
    }

    @Scheduled(cron = "0 17 22 * * ?") // 每天晚上 10:17 执行
    public void generateDailyData() {
        Random random = new Random();

        Map<String, Object> data = new HashMap<>();
        data.put("partners", generateRandomValue(30, 60));
        data.put("products", generateRandomValue(100, 500));
        data.put("metrics", generateRandomValue(1000, 7000));
        data.put("uniqueQuery", generateRandomValue(500, 2000));
        data.put("top3PvCtr", String.format("%.2f%%", generateRandomValue(1, 50)));

        // 存入 Redis
        String redisKey = "dailyData:" + LocalDate.now();
        redisTemplate.opsForHash().putAll(redisKey, data);

        // 设置过期时间
        redisTemplate.expire(redisKey, 7, TimeUnit.DAYS);
    }

    private int generateRandomValue(int min, int max) {
        return new Random().nextInt(max - min + 1) + min;
    }
}
