package org.jeecg.modules.corp.util;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.corp.dto.ChatDto;
import org.jeecg.modules.corp.entity.PdChatUser;
import org.jeecg.modules.corp.entity.PdLedger;
import org.jeecg.modules.corp.service.IPdChatUserService;
import org.jeecg.modules.corp.service.IPdLedgerService;
import org.jeecg.modules.info.entity.PdGuestUsers;
import org.jeecg.modules.info.service.IPdGuestUsersService;
import org.jeecg.modules.info.util.ServiceUtil;
import org.jeecg.modules.wechat.entity.PdChat;
import org.jeecg.modules.wechat.service.IPdChatService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.*;

@Component
@Slf4j
public class GenerateRecords {
    @Resource
    public  IPdChatService pdChatService;
    @Resource
    public  IPdChatUserService pdChatUserService;
    @Resource
    private IPdLedgerService pdLedgerService;



    private static final String BOOTGRID ="7436354095612428327";



    public void processChatRecords(ChatDto chatDto) {
        String ip = chatDto.getIp();
        PdGuestUsers user = chatDto.getUser();
        Integer linkType = chatDto.getLinkType();
        Integer tenantId = chatDto.getTenantId();
        String userItem = chatDto.getUserItem();
        Date date = chatDto.getDate();

        if (date != null) {
            Random random = new Random();
            long delayMillis;
            if (random.nextDouble() < 0.9) {
                delayMillis = (long) (random.nextDouble() * 24 * 60 * 60 * 1000);
            } else {
                delayMillis = (long) (24 * 60 * 60 * 1000 + random.nextDouble() * 24 * 60 * 60 * 1000);
            }
            Date delayedDate = new Date(date.getTime() + delayMillis);
            chatDto.setDate(delayedDate);
        }

        String message;
        if (!ObjectUtils.isEmpty(userItem)) {
            message = userItem;
        } else {
            switch (linkType) {
                case 0:
                    message = ServiceUtil.getRandomTopic("车险");
                    break;
                case 1:
                    message = "财险";
                    break;
                case 2:
                    message = "增值服务";
                    break;
                default:
                    throw new IllegalArgumentException("未知的 linkType: " + linkType);
            }
        }

        JSONArray chatArray = null;
        String aiMessage;
        int retryCount = 0;
        boolean success = false;

        while (retryCount < 5 && !success) {
            try {
                aiMessage = pdChatService.buttonRecord(message, BOOTGRID);
                if (ObjectUtils.isEmpty(aiMessage)) {
                    throw new RuntimeException("AI 返回消息为空");
                }

                chatArray = JSONArray.parseArray(aiMessage);
                success = true;
            } catch (Exception e) {
                retryCount++;
                log.warn("解析失败，正在重试第 {} 次: {}", retryCount, e.getMessage());
                try {
                    Thread.sleep(8000);
                } catch (InterruptedException ex) {
                    Thread.currentThread().interrupt();
                    throw new RuntimeException("线程休眠被中断", ex);
                }
                if (retryCount == 3) {
                    log.error("AI 消息解析连续 3 次失败，可能不是 JSON 格式");
                    throw new RuntimeException("解析 AI 消息失败", e);
                }
            }
        }

        if (ObjectUtils.isEmpty(chatArray)) {
            throw new IllegalStateException("AI 消息未包含有效聊天内容");
        }

        Date baseTime = Objects.nonNull(date) ? date : new Date();

        PdChatUser chatUser = new PdChatUser();
        chatUser.setIpAddress(ip)
                .setWideType(linkType)
                .setDiverData(baseTime)
                .setUserId(user.getId())
                .setTenantId(tenantId)
                .setUserName(user.getName());
        if (StrUtil.isNotEmpty(chatDto.getKid())) {
            chatUser.setKeyId(chatDto.getKid());
        }
        pdChatUserService.save(chatUser);

        long currentTimeMillis = baseTime.getTime();

        for (int i = 0; i < chatArray.size(); i++) {
            JSONObject chatObject = chatArray.getJSONObject(i);
            String userMessage = chatObject.getString("userMessage");
            String userMessagetow = chatObject.getString("userMessagetow");
            String customerServiceReply = chatObject.getString("customerServiceReply");

            if (ObjectUtils.isEmpty(userMessage) && ObjectUtils.isEmpty(customerServiceReply)) {
                log.warn("跳过无效的聊天记录：{}", chatObject);
                continue;
            }

            long sendTimeOffset = 3000 + (long) (Math.random() * 12000);
            Date sendTime = new Date(currentTimeMillis + sendTimeOffset);

            if (!ObjectUtils.isEmpty(userMessage)) {
                PdChat userChat = new PdChat();
                userChat.setSendType(0)
                        .setMessage(userMessage)
                        .setUserId(user.getId())
                        .setIpAddress(ip)
                        .setTenantId(tenantId)
                        .setSendTime(sendTime);
                pdChatService.save(userChat);
                currentTimeMillis = sendTime.getTime();
            }

            if (!ObjectUtils.isEmpty(userMessagetow) && !"null".equalsIgnoreCase(userMessagetow)) {
                PdChat userChat = new PdChat();
                userChat.setSendType(0)
                        .setMessage(userMessagetow)
                        .setUserId(user.getId())
                        .setIpAddress(ip)
                        .setTenantId(tenantId)
                        .setSendTime(sendTime);
                pdChatService.save(userChat);
                currentTimeMillis = sendTime.getTime();
            }

            if (!ObjectUtils.isEmpty(customerServiceReply)) {
                sendTimeOffset = 3000 + (long) (Math.random() * 12000);
                sendTime = new Date(currentTimeMillis + sendTimeOffset);

                PdChat customerChat = new PdChat();
                customerChat.setSendType(1)
                        .setMessage(customerServiceReply)
                        .setUserId(user.getId())
                        .setIpAddress(ip)
                        .setTenantId(tenantId)
                        .setSendTime(sendTime);
                pdChatService.save(customerChat);
                currentTimeMillis = sendTime.getTime();
            }


            if (linkType == 0) {
                PdLedger pdLedger = new PdLedger();
                pdLedger.setId(chatDto.getKid());
                pdLedger.setChatStatus(2);
                pdLedgerService.updateById(pdLedger);
            }
        }
    }

}
