package org.jeecg.modules.corp.util;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class NewsItem {
    private String title;
    private String link;
    private String source;
    private String pubDate;
    private boolean havePic;
    private String img;
    private String nid;

    @JsonProperty("imageurls")
    private List<ImageUrl> imageUrls;
}