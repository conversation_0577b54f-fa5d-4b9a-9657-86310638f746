package org.jeecg.modules.corp.util;
import io.github.bonigarcia.wdm.WebDriverManager;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.chrome.ChromeDriver;

public class NewsScraper {

        public static void main(String[] args) {
            String newsUrl = "https://news.china.com/socialgd/10000169/20250327/48136699.html";

            // 1. 先尝试用 Jsoup 获取新闻正文
            String content = getNewsContentWithJsoup(newsUrl);
            if (content == null || content.isEmpty()) {
                System.out.println("Jsoup 获取失败，尝试 Selenium...");

                // 2. 如果 Jsoup 失败，改用 Selenium
                content = getNewsContentWithSelenium(newsUrl);
            }

            if (content != null && !content.isEmpty()) {
                System.out.println("新闻内容: " + content);
            } else {
                System.out.println("获取新闻正文失败！");
            }
        }

        /**
         * 使用 Jsoup 获取新闻正文
         */
        public static String getNewsContentWithJsoup(String url) {
            try {
                Document doc = Jsoup.connect(url)
                        .userAgent("Mozilla/5.0 (Windows NT 10.0; Win64; x64)")
                        .timeout(10000)
                        .get();

                // 找可能的正文标签
                Elements content = doc.select("div.article-content, div.content, div.main-content, p");
                return content.text();
            } catch (Exception e) {
                System.out.println("Jsoup 解析失败: " + e.getMessage());
                return null;
            }
        }

        /**
         * 使用 Selenium 获取动态渲染的新闻正文
         */
        public static String getNewsContentWithSelenium(String url) {
            WebDriverManager.chromedriver().setup(); // 自动下载 ChromeDriver
            WebDriver driver = new ChromeDriver();

            try {
                driver.get(url);
                Thread.sleep(3000); // 等待页面加载

                // 获取完整 HTML
                String pageSource = driver.getPageSource();
                driver.quit();

                // 用 Jsoup 解析
                Document doc = Jsoup.parse(pageSource);
                Elements content = doc.select("div.article-content, div.content, div.main-content, p");
                return content.text();
            } catch (Exception e) {
                System.out.println("Selenium 获取失败: " + e.getMessage());
                return null;
            } finally {
                driver.quit();
            }
        }
    }