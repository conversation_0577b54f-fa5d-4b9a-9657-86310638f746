package org.jeecg.modules.corp.util;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.util.RedisUtil;
import org.jeecg.modules.wechat.entity.AppNews;
import org.jeecg.modules.wechat.service.IAppNewsService;
import org.jsoup.Connection;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Component
@Slf4j
public class NewsScraperBig {

    private static final String[] keywords = {"保险"};
    private static final String API_URL = "https://route.showapi.com/109-35";
    private static final String REDIS_PAGE_KEY = "news_scraper:current_page:";

    //@Value("${wanxiang.apikey:57543c82935f4147B1b07d3528933880}")
    private String apiKey="57543c82935f4147B1b07d3528933880";
    
    @Autowired
    private RedisUtil redisUtil;
    
    @Resource
    private IAppNewsService appNewsService;

    public static void main(String[] args) {
        NewsScraperBig scraper = new NewsScraperBig();
        List<Map<String, Object>> results = scraper.fetchAndSaveNews(5);

        for (Map<String, Object> result : results) {
            String title = (String) result.get("title");
            String link = (String) result.get("link");

            System.out.println("标题：" + title);
            System.out.println("链接：" + link);

            // 调用解析正文的方法
            String htmlContent = scraper.fetchAndParseArticle(link);
            System.out.println("富文本内容：");
            System.out.println(htmlContent);
            System.out.println("=====================================");
        }
    }

    /**
     * 获取并保存新闻
     * @param maxCount 需要抓取的新闻数量
     * @return 抓取到的新闻列表
     */
    public List<Map<String, Object>> fetchAndSaveNews(int maxCount) {
        List<Map<String, Object>> results = new ArrayList<>();
        try {
            for (String keyword : keywords) {
                // 从Redis获取当前页码，如果不存在则从第1页开始
                String redisKey = REDIS_PAGE_KEY + keyword;
                int page = 1;
                
                if (redisUtil.hasKey(redisKey)) {
                    Object pageObj = redisUtil.get(redisKey);
                    if (pageObj != null) {
                        page = Integer.parseInt(pageObj.toString());
                    }
                }
                
                // 获取系统中已存在的新闻标题集合
                Set<String> existingTitles = getExistingNewsTitles();
                
                int emptyPageCount = 0;
                int maxEmptyPages = 3; // 连续空页数达到此值时停止抓取该关键词
                
                while (results.size() < maxCount && emptyPageCount < maxEmptyPages) {
                    log.info("抓取关键词 [{}] 第 {} 页的新闻", keyword, page);
                    String response = fetchNewsFromApi(keyword, page);
                    if (response == null || response.isEmpty()) {
                        log.warn("未获取到 {} 第 {} 页相关的资讯", keyword, page);
                        emptyPageCount++;
                        page++;
                        continue;
                    }

                    NewsResponse newsResponse = parseJson(response);
                    if (newsResponse == null || 
                        newsResponse.getShowapiResBody() == null || 
                        newsResponse.getShowapiResBody().getPagebean() == null || 
                        newsResponse.getShowapiResBody().getPagebean().getContentlist() == null || 
                        newsResponse.getShowapiResBody().getPagebean().getContentlist().isEmpty()) {
                        log.warn("API 第 {} 页返回的数据为空: {}", page, keyword);
                        emptyPageCount++;
                        page++;
                        continue;
                    }
                    
                    boolean foundNewNews = false;
                    for (NewsItem news : newsResponse.getShowapiResBody().getPagebean().getContentlist()) {
                        if (results.size() >= maxCount) break;
                        
                        // 判断新闻来源和是否已存在
                        if (news.getSource() != null && 
                            news.getSource().contains("中国新闻网") && 
                            news.getLink() != null && 
                            news.getTitle() != null && 
                            !existingTitles.contains(news.getTitle())) {
                            
                            Map<String, Object> result = new HashMap<>();
                            result.put("link", news.getLink());
                            result.put("img", news.getImg());
                            result.put("title", news.getTitle());
                            result.put("pubDate", news.getPubDate());
                            result.put("heading", keyword);
                            results.add(result);
                            foundNewNews = true;
                        }
                    }
                    
                    if (!foundNewNews) {
                        emptyPageCount++;
                    } else {
                        emptyPageCount = 0; // 重置空页计数
                    }
                    
                    page++;
                }
                
                // 保存当前页码到Redis，设置过期时间为当天结束
                saveCurrentPageToRedis(redisKey, page);
            }
        } catch (Exception e) {
            log.error("获取新闻失败", e);
        }
        
        return results;
    }
    
    /**
     * 获取系统中已存在的新闻标题集合
     */
    private Set<String> getExistingNewsTitles() {
        List<AppNews> newsList = appNewsService.list(new QueryWrapper<AppNews>());
        return newsList.stream()
                .map(AppNews::getName)
                .collect(Collectors.toSet());
    }
    
    /**
     * 保存当前页码到Redis，并设置过期时间为当天结束
     */
    private void saveCurrentPageToRedis(String key, int page) {
        redisUtil.set(key, page);
        
        // 设置过期时间为当天结束
        LocalDateTime endOfDay = LocalDate.now().atTime(LocalTime.MAX);
        Date expiryDate = Date.from(endOfDay.atZone(ZoneId.systemDefault()).toInstant());
        redisUtil.expire(key, (expiryDate.getTime() - System.currentTimeMillis()) / 1000);
        
        log.info("已保存当前页码到Redis: key={}, page={}, 过期时间={}", key, page, expiryDate);
    }

    public String fetchAndParseArticle(String url) {
        try {
            Connection connection = Jsoup.connect(url)
                    .userAgent("Mozilla/5.0 (Windows NT 10.0; Win64; x64)")
                    .timeout(10000);  // 设置超时，避免卡住
            Document document = connection.get();

            // 优先使用 id="content"，如果没有则使用 class="left_zw"
            Element contentDiv = document.getElementById("content");
            if (contentDiv == null) {
                Elements elements = document.getElementsByClass("left_zw");
                if (!elements.isEmpty()) {
                    contentDiv = elements.first();
                }
            }

            if (contentDiv == null) {
                log.error("无法找到新闻正文内容");
                return null;
            }

            // 提取所有 <p> 标签的 HTML 内容（保留格式）
            Elements paragraphs = contentDiv.getElementsByTag("p");
            StringBuilder htmlContent = new StringBuilder();
            for (Element paragraph : paragraphs) {
                if (!paragraph.text().trim().isEmpty()) {
                    htmlContent.append(paragraph.outerHtml()).append("\n");
                }
            }

            return htmlContent.toString();
        } catch (Exception e) {
            log.error("解析新闻正文失败", e);
            return null;
        }
    }


    private String fetchNewsFromApi(String keyword, int page) throws Exception {
        String encodedKeyword = URLEncoder.encode(keyword, StandardCharsets.UTF_8.name());
        String params = String.format("appKey=%s&title=%s&page=%d&maxResult=20",
                apiKey, encodedKeyword, page);

        URL url = new URL(API_URL);
        HttpURLConnection con = (HttpURLConnection) url.openConnection();
        con.setRequestMethod("POST");
        con.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");
        con.setDoOutput(true);

        try (OutputStream os = con.getOutputStream()) {
            os.write(params.getBytes(StandardCharsets.UTF_8));
        }

        int responseCode = con.getResponseCode();
        if (responseCode == HttpURLConnection.HTTP_OK) {
            try (BufferedReader in = new BufferedReader(new InputStreamReader(con.getInputStream()))) {
                StringBuilder response = new StringBuilder();
                String line;
                while ((line = in.readLine()) != null) {
                    response.append(line);
                }
                return response.toString();
            }
        } else {
            log.error("API 请求失败，HTTP 状态码: {}", responseCode);
            return null;
        }
    }

    private NewsResponse parseJson(String jsonResponse) {
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
            return objectMapper.readValue(jsonResponse, NewsResponse.class);
        } catch (Exception e) {
            log.error("解析 JSON 失败", e);
            return null;
        }
    }
}
