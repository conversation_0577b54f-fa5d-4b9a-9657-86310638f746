package org.jeecg.modules.corp.util;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;

public class RandDayNumUtil {

    public static Map<String, LocalDateTime> generateTimeMap(LocalDate date, int startNum, int endNum) {
        return generateTimeMap(date, (double) startNum, (double) endNum);
    }

    public static Map<String, LocalDateTime> generateTimeMap(LocalDate date, Double startNum, Double endNum) {
        Random random = new Random();

        double rand = startNum + random.nextDouble() * (endNum - startNum);
        int randInt = (int) Math.round(rand);

        int randMoPercentMin = (int) (randInt * 0.03);
        int randMoPercentMax = (int) (randInt * 0.05);
        int randMo = random.nextInt(randMoPercentMax - randMoPercentMin + 1) + randMoPercentMin;
        int randDay = randInt - randMo;

        List<LocalDateTime> moTimes = distributeTimes(date, 0, 7, randMo, true);
        List<LocalDateTime> dayTimes = distributeTimes(date, 7, 24, randDay, false);

        List<LocalDateTime> allTimes = new ArrayList<>();
        allTimes.addAll(moTimes);
        allTimes.addAll(dayTimes);
        Collections.sort(allTimes);

        Map<String, LocalDateTime> resultMap = new LinkedHashMap<>();
        AtomicInteger counter = new AtomicInteger(1);
        allTimes.forEach(time -> resultMap.put("key_" + counter.getAndIncrement(), time));

        return resultMap;
    }

    private static List<LocalDateTime> distributeTimes(LocalDate date, int hourStart, int hourEnd, int count, boolean morning) {
        Random random = new Random();
        Map<Integer, Integer> hourBucket = new HashMap<>();

        for (int i = hourStart; i < hourEnd; i++) {
            hourBucket.put(i, 0);
        }

        for (int i = 0; i < count; i++) {
            int hour = random.nextInt(hourEnd - hourStart) + hourStart;
            hourBucket.put(hour, hourBucket.get(hour) + 1);
        }

        List<LocalDateTime> result = new ArrayList<>();
        for (Map.Entry<Integer, Integer> entry : hourBucket.entrySet()) {
            int hour = entry.getKey();
            int nums = entry.getValue();
            for (int i = 0; i < nums; i++) {
                int minute = random.nextInt(60);
                int second = random.nextInt(60);
                result.add(LocalDateTime.of(date, LocalTime.of(hour, minute, second)));
            }
        }
        return result;
    }

    public static void main(String[] args) {
        LocalDate targetDate = LocalDate.of(2025, 4, 13);
        Map<String, LocalDateTime> resultMap = generateTimeMap(targetDate, 1000, 2000);

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        resultMap.forEach((k, v) -> System.out.println(k + " -> " + v.format(formatter)));
    }

    /**
     * 随机从 resultMap 中按 randRateMin ~ randRateMax 比例选取部分数据
     * @param resultMap 原始 Map<String, LocalDateTime>
     * @param randRateMin 比率下限（百分比）
     * @param randRateMax 比率上限（百分比）
     * @return 随机选出的 Map<String, LocalDateTime>
     */
    public static Map<String, LocalDateTime> pickByRandomRate(Map<String, LocalDateTime> resultMap, Double randRateMin, Double randRateMax) {
        if (resultMap == null || resultMap.isEmpty()) {
            return Collections.emptyMap();
        }

        // 随机生成一个百分比
        Random random = new Random();
        double randRate = randRateMin + random.nextDouble() * (randRateMax - randRateMin);

        int totalSize = resultMap.size();
        int pickSize = (int) Math.round(totalSize * randRate / 100.0);

        List<Map.Entry<String, LocalDateTime>> entries = new ArrayList<>(resultMap.entrySet());
        Collections.shuffle(entries); // 打乱顺序

        Map<String, LocalDateTime> ledgerTimeMap = new LinkedHashMap<>();
        for (int i = 0; i < pickSize && i < entries.size(); i++) {
            Map.Entry<String, LocalDateTime> entry = entries.get(i);
            ledgerTimeMap.put(entry.getKey(), entry.getValue());
        }

        return ledgerTimeMap;
    }

    /**
     * 重载方法，支持int类型参数，向下兼容
     */
    public static Map<String, LocalDateTime> pickByRandomRate(Map<String, LocalDateTime> resultMap, int randRateMin, int randRateMax) {
        return pickByRandomRate(resultMap, (double) randRateMin, (double) randRateMax);
    }

}
