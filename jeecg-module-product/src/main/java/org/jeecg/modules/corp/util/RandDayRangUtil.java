package org.jeecg.modules.corp.util;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;

public class RandDayRangUtil {

    public static Map<String, LocalDateTime> generateRandomTimes(LocalDate monthDataStart, LocalDate monthDataEnd, int startNum, int endNum) {
        Map<String, LocalDateTime> result = new TreeMap<>();
        Random random = new Random();

        int totalDays = (int) ChronoUnit.DAYS.between(monthDataStart, monthDataEnd) + 1;
        int rand = (startNum == endNum) ? startNum : startNum + random.nextInt(endNum - startNum + 1);

        // 记录目标总数，确保最终生成的总数与此相等
        final int targetTotalCount = rand;

        // 凌晨点击量（3%~5%）
        int randMo = (int) (rand * (0.03 + (0.02 * random.nextDouble())));
        randMo = Math.max(randMo, 1); // 保底 1
        int randDay = rand - randMo;

        // 为每天生成带有适度波动的随机权重
        List<Double> weights = new ArrayList<>();
        double totalWeight = 0;

        // 生成基础权重（使用高斯分布，但增加标准差以增加波动性）
        for (int i = 0; i < totalDays; i++) {
            // 使用高斯分布，均值为1.0，标准差增加到0.25，增加波动性
            // 大多数值会在0.5-1.5之间，少数会低于0.25或高于1.75
            double weight = Math.max(0.3, random.nextGaussian() * 0.25 + 1.0);
            weights.add(weight);
            totalWeight += weight;
        }

        // 添加一些随机波动（有10%的概率产生一个高峰日或低谷日）
        for (int i = 0; i < totalDays; i++) {
            double chance = random.nextDouble();
            if (chance < 0.05) {
                // 5%概率出现高峰日（比如促销活动、热点事件等）
                weights.set(i, weights.get(i) * (1.5 + random.nextDouble() * 0.5)); // 1.5-2.0倍
            } else if (chance < 0.1) {
                // 5%概率出现低谷日（比如系统维护、节假日等）
                weights.set(i, weights.get(i) * (0.5 + random.nextDouble() * 0.3)); // 0.5-0.8倍
            }
        }

        // 应用周末效应：周末（周六、周日）的权重略低
        for (int i = 0; i < totalDays; i++) {
            LocalDate date = monthDataStart.plusDays(i);
            DayOfWeek dayOfWeek = date.getDayOfWeek();

            // 周末权重略低（周六周日流量通常比工作日少）
            if (dayOfWeek == DayOfWeek.SATURDAY || dayOfWeek == DayOfWeek.SUNDAY) {
                weights.set(i, weights.get(i) * 0.85);
            }
        }

        // 添加一些连续性波动（模拟真实世界中的趋势变化）
        // 生成几个随机的波动周期
        int numWaves = 2 + random.nextInt(3); // 2-4个波动周期
        for (int wave = 0; wave < numWaves; wave++) {
            int waveStart = random.nextInt(totalDays);
            int waveLength = 3 + random.nextInt(5); // 3-7天的波动周期
            double waveAmplitude = 0.2 + random.nextDouble() * 0.3; // 波动幅度0.2-0.5
            boolean isPositiveWave = random.nextBoolean(); // 正波还是负波

            for (int day = 0; day < waveLength; day++) {
                int idx = (waveStart + day) % totalDays;
                // 波动强度随着距离波动中心的增加而减弱
                double factor = waveAmplitude * (1.0 - (double)day / waveLength);
                if (isPositiveWave) {
                    weights.set(idx, weights.get(idx) * (1.0 + factor));
                } else {
                    weights.set(idx, weights.get(idx) * (1.0 - factor * 0.5));
                }
            }
        }

        // 重新计算总权重
        totalWeight = weights.stream().mapToDouble(Double::doubleValue).sum();

        // 按权重分配凌晨
        int[] moCounts = new int[totalDays];
        int moAllocated = 0;
        for (int i = 0; i < totalDays; i++) {
            moCounts[i] = (int) ((randMo * weights.get(i)) / totalWeight);
            moAllocated += moCounts[i];
        }

        // 补足凌晨缺口，优先分配给权重较高的日期
        List<Integer> indices = new ArrayList<>();
        for (int i = 0; i < totalDays; i++) {
            indices.add(i);
        }
        // 按权重从高到低排序日期索引
        indices.sort((a, b) -> Double.compare(weights.get(b), weights.get(a)));

        for (int idx = 0; moAllocated < randMo; idx = (idx + 1) % totalDays) {
            int i = indices.get(idx);
            moCounts[i]++;
            moAllocated++;
        }

        // 同理分配白天
        int[] dayCounts = new int[totalDays];
        int dayAllocated = 0;
        for (int i = 0; i < totalDays; i++) {
            dayCounts[i] = (int) ((randDay * weights.get(i)) / totalWeight);
            dayAllocated += dayCounts[i];
        }

        // 补足白天缺口，同样优先分配给权重较高的日期
        for (int idx = 0; dayAllocated < randDay; idx = (idx + 1) % totalDays) {
            int i = indices.get(idx);
            dayCounts[i]++;
            dayAllocated++;
        }

        // 检查每天的点击数是否在合理范围内，但允许更大的波动
        double avgClicksPerDay = rand / (double) totalDays;
        double maxAllowedClicks = avgClicksPerDay * 2.2; // 最大允许点击数为平均值的2.2倍
        double minAllowedClicks = avgClicksPerDay * 0.3; // 最小允许点击数为平均值的0.3倍

        // 调整异常值
        for (int i = 0; i < totalDays; i++) {
            int totalDayClicks = moCounts[i] + dayCounts[i];

            // 如果某天的点击数过高，将多余的点击数重新分配
            if (totalDayClicks > maxAllowedClicks) {
                int excess = (int) (totalDayClicks - maxAllowedClicks);

                // 按比例从凌晨和白天减少点击数
                int moExcess = (int) (excess * (moCounts[i] / (double) totalDayClicks));
                int dayExcess = excess - moExcess;

                moCounts[i] -= moExcess;
                dayCounts[i] -= dayExcess;

                // 将多余的点击数重新分配给点击数较少的日期
                for (int j = totalDays - 1; j >= 0; j--) {
                    int idx = indices.get(j); // 从权重最低的日期开始分配
                    if (moCounts[idx] + dayCounts[idx] < minAllowedClicks) {
                        // 按比例分配到凌晨和白天
                        int addMo = Math.min(moExcess, (int) (minAllowedClicks - (moCounts[idx] + dayCounts[idx])) / 2);
                        int addDay = Math.min(dayExcess, (int) (minAllowedClicks - (moCounts[idx] + dayCounts[idx])) / 2);

                        moCounts[idx] += addMo;
                        dayCounts[idx] += addDay;

                        moExcess -= addMo;
                        dayExcess -= addDay;

                        if (moExcess <= 0 && dayExcess <= 0) break;
                    }
                }
            }
        }

        // 生成时间数据
        for (int i = 0; i < totalDays; i++) {
            LocalDate date = monthDataStart.plusDays(i);
            result.putAll(generateTimeMap(moCounts[i], date, 0, 7, random, true));
            result.putAll(generateTimeMap(dayCounts[i], date, 7, 24, random, false));
        }

        // 检查最终生成的总数是否与目标总数相等
        int actualCount = result.size();

        // 如果总数不匹配，补足或减少
        if (actualCount != targetTotalCount) {
            if (actualCount < targetTotalCount) {
                // 需要补足
                int toAdd = targetTotalCount - actualCount;
                LocalDate middleDate = monthDataStart.plusDays(totalDays / 2);

                // 在中间日期的工作时间段添加缺少的点击
                Map<String, LocalDateTime> additionalClicks = generateTimeMap(toAdd, middleDate, 9, 17, random, false);
                result.putAll(additionalClicks);
            } else {
                // 需要减少
                int toRemove = actualCount - targetTotalCount;
                List<String> keys = new ArrayList<>(result.keySet());

                // 随机移除多余的点击
                for (int i = 0; i < toRemove; i++) {
                    int randomIndex = random.nextInt(keys.size());
                    result.remove(keys.get(randomIndex));
                    keys.remove(randomIndex);
                }
            }
        }

        return result;
    }

    /**
     * 生成指定时间段内的随机时间点
     * @param totalCount 总点击数
     * @param date 日期
     * @param startHour 开始小时
     * @param endHour 结束小时
     * @param random 随机数生成器
     * @param isMorning 是否为凌晨时段
     * @return 随机时间点Map
     */
    private static Map<String, LocalDateTime> generateTimeMap(int totalCount, LocalDate date, int startHour, int endHour, Random random, boolean isMorning) {
        Map<String, LocalDateTime> timeMap = new TreeMap<>();
        int totalHours = endHour - startHour;

        // 如果总数为0，直接返回空Map
        if (totalCount <= 0) {
            return timeMap;
        }

        // 创建更真实的小时分布权重
        double[] hourWeights = new double[totalHours];
        double totalWeight = 0;

        if (isMorning) {
            // 凌晨时段 (0-7点) - 通常呈现递减趋势，0点活跃度较高，然后逐渐降低
            for (int i = 0; i < totalHours; i++) {
                // 0点权重较高，然后逐渐降低
                hourWeights[i] = Math.max(0.1, 1.0 - (i * 0.15));
                // 添加一些随机波动
                hourWeights[i] *= (0.8 + random.nextDouble() * 0.4); // 0.8-1.2倍随机波动
                totalWeight += hourWeights[i];
            }
        } else {
            // 白天时段 (7-24点) - 工作时间段 (9-12, 14-18) 活跃度高
            for (int i = 0; i < totalHours; i++) {
                int hour = i + startHour;

                // 基础权重 - 根据一天中的时间段设置不同权重
                if (hour >= 9 && hour < 12) {
                    // 上午工作时间 (9-12点)
                    hourWeights[i] = 1.5 + random.nextDouble() * 0.5; // 1.5-2.0
                } else if (hour >= 14 && hour < 18) {
                    // 下午工作时间 (14-18点)
                    hourWeights[i] = 1.8 + random.nextDouble() * 0.7; // 1.8-2.5
                } else if (hour >= 19 && hour < 22) {
                    // 晚间休闲时间 (19-22点)
                    hourWeights[i] = 1.3 + random.nextDouble() * 0.5; // 1.3-1.8
                } else if (hour >= 12 && hour < 14) {
                    // 午休时间 (12-14点)
                    hourWeights[i] = 0.7 + random.nextDouble() * 0.3; // 0.7-1.0
                } else if (hour >= 22) {
                    // 深夜时段 (22-24点)
                    hourWeights[i] = 0.5 + random.nextDouble() * 0.3; // 0.5-0.8
                } else {
                    // 其他时间 (7-9点)
                    hourWeights[i] = 0.8 + random.nextDouble() * 0.4; // 0.8-1.2
                }

                // 添加一些随机波动
                hourWeights[i] *= (0.9 + random.nextDouble() * 0.2); // 0.9-1.1倍随机波动
                totalWeight += hourWeights[i];
            }
        }

        // 根据权重分配点击数
        int[] countPerHour = new int[totalHours];
        int allocated = 0;

        for (int i = 0; i < totalHours; i++) {
            countPerHour[i] = (int) ((totalCount * hourWeights[i]) / totalWeight);
            allocated += countPerHour[i];
        }

        // 补足缺口
        for (int i = 0; allocated < totalCount; i = (i + 1) % totalHours) {
            countPerHour[i]++;
            allocated++;
        }

        // 生成每小时内的随机时间点
        for (int hour = startHour; hour < endHour; hour++) {
            int hourIndex = hour - startHour;
            int count = countPerHour[hourIndex];

            // 在小时内分配分钟，使分钟分布更真实
            // 通常每小时的开始和结束时分钟分布较少，中间时段较多
            for (int i = 0; i < count; i++) {
                // 使用三角分布或贝塔分布来模拟分钟分布
                // 简化版：使用幂函数使分钟集中在中间时段
                double minuteRand = random.nextDouble();
                int minute;

                // 将分钟集中在15-45之间，两端较少
                if (minuteRand < 0.7) {
                    // 70%的时间点在15-45分钟之间
                    minute = 15 + random.nextInt(31);
                } else if (minuteRand < 0.85) {
                    // 15%的时间点在0-15分钟之间
                    minute = random.nextInt(15);
                } else {
                    // 15%的时间点在45-60分钟之间
                    minute = 45 + random.nextInt(15);
                }

                // 秒数随机分布
                int second = random.nextInt(60);

                LocalDateTime randomTime = date.atTime(hour, minute, second);
                timeMap.put(UUID.randomUUID().toString(), randomTime);
            }
        }

        return timeMap;
    }

    public static void main(String[] args) {
        // 测试多次运行，验证总数是否始终等于设置值
        for (int test = 0; test < 5; test++) {
            System.out.println("\n===== 测试 #" + (test + 1) + " =====");

            LocalDate start = LocalDate.of(2025, 3, 1);
            LocalDate end = LocalDate.of(2025, 3, 31);
            int startNum = 2314321;  // 使用您提到的测试值
            int endNum = 2314321;

            long startTime = System.currentTimeMillis();
            Map<String, LocalDateTime> timeMap = generateRandomTimes(start, end, startNum, endNum);
            long endTime = System.currentTimeMillis();

            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

            System.out.println("目标总数: " + startNum);
            System.out.println("实际总数: " + timeMap.size());
            System.out.println("是否相等: " + (timeMap.size() == startNum ? "是" : "否"));
            System.out.println("执行时间: " + (endTime - startTime) + "ms");

            // 统计每天的点击数
            Map<LocalDate, Integer> dailyCounts = new TreeMap<>();
            timeMap.forEach((k, v) -> {
            LocalDate date = v.toLocalDate();
            dailyCounts.put(date, dailyCounts.getOrDefault(date, 0) + 1);
        });

            // 计算统计信息
            int totalDays = dailyCounts.size();
            int totalClicks = timeMap.size();
            double avgClicksPerDay = totalClicks / (double) totalDays;
            int minClicks = Integer.MAX_VALUE;
            int maxClicks = 0;
            LocalDate minDate = null;
            LocalDate maxDate = null;

            for (Map.Entry<LocalDate, Integer> entry : dailyCounts.entrySet()) {
                int count = entry.getValue();
                if (count < minClicks) {
                    minClicks = count;
                    minDate = entry.getKey();
                }
                if (count > maxClicks) {
                    maxClicks = count;
                    maxDate = entry.getKey();
                }
            }

            // 输出统计信息
            System.out.println("\n===== 点击数统计 =====");
            System.out.println("总天数: " + totalDays);
            System.out.println("总点击数: " + totalClicks);
            System.out.println("平均每天点击数: " + String.format("%.2f", avgClicksPerDay));
            System.out.println("最少点击数: " + minClicks + " (" + minDate + ")");
            System.out.println("最多点击数: " + maxClicks + " (" + maxDate + ")");
            System.out.println("最大/平均比例: " + String.format("%.2f", maxClicks / avgClicksPerDay));
            System.out.println("最小/平均比例: " + String.format("%.2f", minClicks / avgClicksPerDay));

            // 输出每天的点击数
            System.out.println("\n===== 每天点击数 =====");
            dailyCounts.forEach((date, count) -> {
                double ratio = count / avgClicksPerDay;
                String dayOfWeek = date.getDayOfWeek().toString();
                System.out.println(date + " (" + dayOfWeek + "): " + count +
                                   " (" + String.format("%.2f", ratio) + " 倍于平均值)");
            });

            // 统计每小时的点击数
            Map<Integer, Integer> hourCounts = new TreeMap<>();
            timeMap.forEach((k, v) -> {
                int hour = v.getHour();
                hourCounts.put(hour, hourCounts.getOrDefault(hour, 0) + 1);
            });

            // 输出每小时的点击数
            System.out.println("\n===== 24小时点击分布 =====");
            double avgClicksPerHour = totalClicks / 24.0;
            for (int hour = 0; hour < 24; hour++) {
                int hourCount = hourCounts.getOrDefault(hour, 0);
                double hourRatio = hourCount / (avgClicksPerHour * totalDays);

                // 生成简单的柱状图
                StringBuilder bar = new StringBuilder();
                int barLength = (int) (hourRatio * 50);
                for (int i = 0; i < barLength; i++) {
                    bar.append("█");
                }

                System.out.printf("%02d:00-%02d:59: %7d (%5.2f%%) %s%n",
                                 hour, hour, hourCount, hourRatio * 100, bar.toString());
            }

            // 统计每小时内分钟的分布（以5分钟为间隔）
            Map<Integer, Integer> minuteCounts = new TreeMap<>();
            timeMap.forEach((k, v) -> {
                int minute = v.getMinute() / 5; // 将分钟分为12个区间，每个区间5分钟
                minuteCounts.put(minute, minuteCounts.getOrDefault(minute, 0) + 1);
            });

            // 输出分钟分布
            System.out.println("\n===== 小时内分钟分布 (5分钟间隔) =====");
            double avgClicksPerMinuteInterval = totalClicks / 12.0;
            for (int interval = 0; interval < 12; interval++) {
                int startMinute = interval * 5;
                int endMinute = startMinute + 4;
                int count = minuteCounts.getOrDefault(interval, 0);
                double ratio = count / avgClicksPerMinuteInterval;

                // 生成简单的柱状图
                StringBuilder bar = new StringBuilder();
                int barLength = (int) (ratio * 30);
                for (int i = 0; i < barLength; i++) {
                    bar.append("█");
                }

                System.out.printf("%02d-%02d分钟: %7d (%5.2f%%) %s%n",
                                 startMinute, endMinute, count, ratio * 100, bar.toString());
            }

            // 只输出前10条具体时间点数据
            System.out.println("\n===== 前10条时间点数据 =====");
            int count = 0;
            for (Map.Entry<String, LocalDateTime> entry : timeMap.entrySet()) {
                if (count++ < 10) {
                    System.out.println(entry.getKey() + " -> " + entry.getValue().format(formatter));
                } else {
                    break;
                }
            }
        }
    }
}