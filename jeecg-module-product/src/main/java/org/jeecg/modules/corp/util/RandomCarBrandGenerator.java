package org.jeecg.modules.corp.util;

import java.util.Random;

public class RandomCarBrandGenerator {
    private static final Random random = new Random();

    // 约 80 个品牌，包含大众品牌（80万以下）、BBA 和新能源品牌，中文名称
    private static final String[] CAR_BRANDS = {
            // 大众品牌（传统燃油车，80万以下）
            "丰田", "本田", "日产", "大众", "现代", "起亚", "福特", "雪佛兰",
            "吉利", "长安", "长城", "奇瑞", "比亚迪", "马自达", "铃木", "斯柯达",
            "标致", "雪铁龙", "三菱", "东风", "上汽", "广汽", "一汽", "北汽",
            "江淮", "海马", "众泰", "力帆", "东南", "华晨", "金杯", "启辰",
            "传祺", "荣威", "名爵", "五菱", "宝骏", "猎豹", "观致", "陆风",

            // BBA（豪华品牌）
            "奔驰", "宝马", "奥迪",

            // 新能源品牌（含部分传统品牌的新能源分支）
            "特斯拉", "蔚来", "小鹏", "理想", "哪吒", "威马", "零跑", "极氪",
            "岚图", "埃安", "欧拉", "几何", "极狐", "智己", "赛力斯", "合众",
            "高合", "广汽新能源", "长安新能源", "吉利新能源", "比亚迪新能源",
            "上汽大通", "东风小康", "开瑞", "云度", "天际", "爱驰", " ARCFOX",
            "小虎", "思皓", "轻橙", "奇瑞新能源", "荣威新能源", "名爵新能源",

            // 其他补充品牌
            "沃尔沃", "捷豹", "路虎", "雷克萨斯", "凯迪拉克", "林肯", "英菲尼迪",
            "红旗", "哈弗", "WEY", "坦克"
    };

    /**
     * 随机返回一个汽车品牌名称（中文）
     * @return 中文品牌名称
     */
    public static String generateRandomCarBrand() {
        return CAR_BRANDS[random.nextInt(CAR_BRANDS.length)];
    }

    // 测试方法
    public static void main(String[] args) {
        for (int i = 0; i < 10; i++) {
            String brand = generateRandomCarBrand();
            System.out.println("Generated Brand " + (i + 1) + ": " + brand);
        }
        System.out.println("Total brands: " + CAR_BRANDS.length);
    }
}
