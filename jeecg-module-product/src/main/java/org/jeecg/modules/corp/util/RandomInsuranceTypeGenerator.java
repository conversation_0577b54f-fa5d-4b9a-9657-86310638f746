package org.jeecg.modules.corp.util;
import java.util.Random;

public class RandomInsuranceTypeGenerator {
    private static final Random random = new Random();

    // 常见车险险种名称（中文）
    private static final String[] INSURANCE_TYPES = {
            // 强制险
            "车损险",
            // 商业险 - 主险
            "第三者责任险",
            "车上人员责任保险 (司机)",
            "车上人员责任保险 (乘客)",
            "附加医保外医疗费用责任险(三者)",
            "附加医保外医疗费用责任险(司机)",
            "附加医保外医疗费用责任险(乘客)",
            "交强险"
    };

    /**
     * 随机返回一个车险险种名称（中文）
     * @return 险种名称
     */
    public static String generateRandomInsuranceType() {
        return INSURANCE_TYPES[random.nextInt(INSURANCE_TYPES.length)];
    }

    // 测试方法
    public static void main(String[] args) {
        for (int i = 0; i < 10; i++) {
            String insuranceType = generateRandomInsuranceType();
            System.out.println("Generated Insurance Type " + (i + 1) + ": " + insuranceType);
        }
        System.out.println("Total insurance types: " + INSURANCE_TYPES.length);
    }
}