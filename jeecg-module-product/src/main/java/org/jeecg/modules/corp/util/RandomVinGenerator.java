package org.jeecg.modules.corp.util;

import java.util.Random;
public class RandomVinGenerator {
    private static final Random random = new Random();
    private static final String[] WMI_PREFIXES = {"LSA", "LSV", "LSG", "LFM", "LFV"}; // 常见的中国 WMI 前缀示例
    private static final String VIN_CHARS = "0123456789ABCDEFGHJKLMNPRSTUVWXYZ"; // VIN 合法字符（不含 I、O、Q）
    private static final String YEAR_CHARS = "123456789ABCDEFGHJKLMNPRSTUVWXY"; // 年份代码（1981-2025）

    /**
     * 生成一个随机的中国车架号
     * @return 17位随机 VIN
     */
    public static String generateRandomVin() {
        StringBuilder vin = new StringBuilder();

        // 第1-8位：包含 WMI（3位） + VDS（5位）
        vin.append(getRandomWmi());           // 3位
        vin.append(generateRandomVds());      // 5位

        // 第9位：校验位
        vin.append(getRandomChar(VIN_CHARS));

        // 第10位：年份代码
        vin.append(getRandomYear());

        // 第11位：装配厂代码
        vin.append(getRandomChar("ABCDEFGHJKLMNPQRSTUVWXYZ"));

        // 第12-17位：6位生产序列号
        vin.append(generateRandomSerialNumber());

        // 返回完整的 VIN，确保长度为 17 位
        return vin.toString(); // 总长度 = 17
    }

    /**
     * 获取随机的 WMI（前3位）
     */
    private static String getRandomWmi() {
        return WMI_PREFIXES[random.nextInt(WMI_PREFIXES.length)];
    }

    /**
     * 生成随机的 VDS（第4-8位）
     */
    private static String generateRandomVds() {
        StringBuilder vds = new StringBuilder();
        for (int i = 0; i < 5; i++) {
            vds.append(getRandomChar(VIN_CHARS));
        }
        return vds.toString();
    }

    /**
     * 获取随机的年份代码（第10位）
     */
    private static String getRandomYear() {
        return String.valueOf(YEAR_CHARS.charAt(random.nextInt(YEAR_CHARS.length())));
    }

    /**
     * 生成随机的生产序列号（第12-17位）
     */
    private static String generateRandomSerialNumber() {
        return String.format("%06d", random.nextInt(1000000)); // 6位数字，补零
    }

    /**
     * 从指定字符集中随机选择一个字符
     */
    private static char getRandomChar(String chars) {
        return chars.charAt(random.nextInt(chars.length()));
    }

    // 测试方法
    public static void main(String[] args) {
        for (int i = 0; i < 10; i++) {
            String vin = generateRandomVin();
            System.out.println("Generated VIN " + (i + 1) + ": " + vin);
        }
    }
}
