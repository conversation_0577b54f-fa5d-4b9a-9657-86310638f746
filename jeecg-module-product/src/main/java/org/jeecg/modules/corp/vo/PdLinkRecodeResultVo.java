package org.jeecg.modules.corp.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 链接快照结果VO
 */
@Data
@Accessors(chain = true)
public class PdLinkRecodeResultVo implements Serializable {

    /**
     * 图表数据
     */
    @ApiModelProperty(value = "图表数据")
    private LineChartData lineChart;

    /**
     * 指标数据
     */
    @ApiModelProperty(value = "指标数据")
    private List<MetricData> data;

    /**
     * 图表数据结构
     */
    @Data
    @Accessors(chain = true)
    public static class LineChartData implements Serializable {
        /**
         * X轴数据
         */
        @ApiModelProperty(value = "X轴数据")
        private List<String> xAxis;

        /**
         * 系列数据
         */
        @ApiModelProperty(value = "系列数据")
        private List<SeriesData> series;
    }

    /**
     * 系列数据结构
     */
    @Data
    @Accessors(chain = true)
    public static class SeriesData implements Serializable {
        /**
         * 类型
         */
        @ApiModelProperty(value = "类型")
        private String type;

        /**
         * 数据
         */
        @ApiModelProperty(value = "数据")
        private List<Integer> data;
    }

    /**
     * 指标数据结构
     */
    @Data
    @Accessors(chain = true)
    public static class MetricData implements Serializable {
        /**
         * 指标名称
         */
        @ApiModelProperty(value = "指标名称")
        private String name;

        /**
         * 指标键名
         */
        @ApiModelProperty(value = "指标键名")
        private String key;

        /**
         * 指标值
         */
        @ApiModelProperty(value = "指标值")
        private Integer value;

        /**
         * 标签
         */
        @ApiModelProperty(value = "标签")
        private String label;

        /**
         * 环比率
         */
        @ApiModelProperty(value = "环比率")
        private Double rate;
    }
}
