package org.jeecg.modules.corp.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;

@Data
@Accessors(chain = true)
public class PdLinkRecodeVo implements Serializable {

    @ApiModelProperty(value = "主键")
    private java.lang.String id;

    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "日期")
    private java.util.Date linkDate;

    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "具体时间")
    private java.util.Date specificTime;

    @ApiModelProperty(value = "页面浏览量")
    private java.lang.Integer pvNum;

    @ApiModelProperty(value = "独立访客量")
    private java.lang.Integer uvNum;

    @ApiModelProperty(value = "翻页率")
    private  java.math.BigDecimal turningNum;

    @ApiModelProperty(value = "转化量")
    private transient java.lang.Integer volumeNum;

    @ApiModelProperty(value = "转化率")
    private transient java.math.BigDecimal rateNum;


}
