package org.jeecg.modules.corp.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * @Description: 今日点击数统计VO
 * @Author: jeecg-boot
 * @Date: 2024-05-16
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "今日点击数统计VO", description = "今日点击数统计VO")
public class TodayClickCountVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 今日点击数
     */
    @ApiModelProperty(value = "今日点击数")
    private Integer todayClickCount;

    /**
     * 今日预约总数
     */
    @ApiModelProperty(value = "今日预约总数")
    private Integer todayReservationCount;

    /**
     * 租户ID
     */
    @ApiModelProperty(value = "租户ID")
    private Integer tenantId;

    /**
     * 统计时间戳
     */
    @ApiModelProperty(value = "统计时间戳")
    private Long timestamp;

    public TodayClickCountVo() {
        this.timestamp = System.currentTimeMillis();
    }

    public TodayClickCountVo(Integer todayClickCount, Integer todayReservationCount, Integer tenantId) {
        this.todayClickCount = todayClickCount;
        this.todayReservationCount = todayReservationCount;
        this.tenantId = tenantId;
        this.timestamp = System.currentTimeMillis();
    }
}
