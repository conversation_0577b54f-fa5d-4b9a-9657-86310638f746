package org.jeecg.modules.corp.vo.report;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import org.jeecg.modules.corp.entity.PdLinkRecode;

import java.io.Serializable;
import java.util.List;

@Data
@Accessors(chain = true)
public class ClickReportHourlyVO implements Serializable {

    @ApiModelProperty(value = "总点击数")
    private PdLinkRecode clickNum;

    @ApiModelProperty(value = "时段曲线图")
    private List<ReportCurveVO> curveList;


    @ApiModelProperty(value = "城市")
    private List<ReportCityVO> cityRateList;

}
