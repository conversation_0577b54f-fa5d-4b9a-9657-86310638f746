package org.jeecg.modules.corp.vo.report;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

@Data
@Accessors(chain = true)
public class ClickReportHourlyVO implements Serializable {

    @ApiModelProperty(value = "时段曲线图")



    @ApiModelProperty(value = "城市")
    private List<ReportCityVO> cityRateList;

}
