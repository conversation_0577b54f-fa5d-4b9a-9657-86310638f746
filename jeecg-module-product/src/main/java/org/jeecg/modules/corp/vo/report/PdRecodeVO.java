package org.jeecg.modules.corp.vo.report;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
@Data
@Accessors(chain = true)
public class PdRecodeVO implements Serializable {



    @ApiModelProperty(value = "跳出率(%)")
    private BigDecimal bounceRate;

    @ApiModelProperty(value = "转化率(%)")
    private BigDecimal conversionRate;

    @ApiModelProperty(value = "表单提交数")
    private Integer formSubmissions;

    @ApiModelProperty(value = "点击率(%)")
    private BigDecimal ctr;

    @ApiModelProperty(value = "平均停留时长(秒)")
    private Integer avgStayTime;

    @ApiModelProperty(value = "返回率(%)")
    private BigDecimal returnRate;

    @ApiModelProperty(value = "内容完成率(%)")
    private BigDecimal completionRate;

    @ApiModelProperty(value = "首屏CTR值(%)")
    private BigDecimal firstScreenCtr;

    @ApiModelProperty(value = "内容Jump率(%)")
    private BigDecimal contentJumpRate;

    @ApiModelProperty(value = "内容Return率(%)")
    private BigDecimal contentReturnRate;

    @ApiModelProperty(value = "Click深度分布(层)")
    private BigDecimal clickDepth;

    @ApiModelProperty(value = "Click间隔时间(秒)")
    private BigDecimal clickIntervalTime;

    @ApiModelProperty(value = "Engagement得分(分)")
    private BigDecimal engagementScore;

    @ApiModelProperty(value = "翻页率比例")
    private BigDecimal pageRate;

    @ApiModelProperty(value = "独立Query数")
    private Integer uniqueQuery;

    @ApiModelProperty(value = "TOP3 PV-CTR(%)")
    private BigDecimal top3PvCtr;
}
