package org.jeecg.modules.corp.vo.report;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@Accessors(chain = true)
public class ReportCityVO implements Serializable {


    @ApiModelProperty(value = "城市")
    private String city;

    @ApiModelProperty(value = "城市占比")
    private BigDecimal cityRate;
}
