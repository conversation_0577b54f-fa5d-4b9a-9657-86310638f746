package org.jeecg.modules.corp.vo.report;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@Accessors(chain = true)
public class ReportCurveVO implements Serializable {

    @ApiModelProperty(value = "时段")
    private String time;

    @ApiModelProperty(value = "点击数即访问量")
    private String  clickNum;

    @ApiModelProperty(value = "预约人数")
    private String  reservationNum;

    @ApiModelProperty(value = "转化率")
    private BigDecimal reservationRate;

}
