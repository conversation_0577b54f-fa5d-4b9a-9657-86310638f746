package org.jeecg.modules.corp.vo.report;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Data
@Accessors(chain = true)
public class ReportCurveVO implements Serializable {

    @ApiModelProperty(value = "时段")
    private String time;

    @ApiModelProperty(value = "点击数")
    private String  clickNum;

    @ApiModelProperty(value = "点击率")
    private String clickRate;

    @ApiModelProperty(value = "预约数")
    private String  reservationNum;

    @ApiModelProperty(value = "预约率")
    private String reservationRate;


}
