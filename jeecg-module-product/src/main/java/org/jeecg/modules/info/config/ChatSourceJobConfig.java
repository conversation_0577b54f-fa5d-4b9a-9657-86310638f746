package org.jeecg.modules.info.config;

import org.jeecg.modules.info.job.sysjob.ChatSourceTaskConsumer;
import org.jeecg.modules.info.job.sysjob.ChatSourceTaskMonitor;
import org.jeecg.modules.info.job.sysjob.ChatSourceUpdateJob;
import org.quartz.*;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 聊天源任务调度配置
 */
@Configuration
public class ChatSourceJobConfig {

    /**
     * 聊天源更新任务
     * 每小时执行一次
     */
    @Bean
    public JobDetail chatSourceUpdateJobDetail() {
        return JobBuilder.newJob(ChatSourceUpdateJob.class)
                .withIdentity("chatSourceUpdateJob")
                .storeDurably()
                .build();
    }

    @Bean
    public Trigger chatSourceUpdateJobTrigger() {
        return TriggerBuilder.newTrigger()
                .forJob(chatSourceUpdateJobDetail())
                .withIdentity("chatSourceUpdateTrigger")
                .withSchedule(CronScheduleBuilder.cronSchedule("0 0 * * * ?")) // 每小时执行一次
                .build();
    }

    /**
     * 聊天源任务消费者
     * 手动触发，不自动调度
     */
    @Bean
    public JobDetail chatSourceTaskConsumerJobDetail() {
        return JobBuilder.newJob(ChatSourceTaskConsumer.class)
                .withIdentity("chatSourceTaskConsumerJob")
                .storeDurably()
                .requestRecovery(true) // 如果执行失败，请求恢复
                .build();
    }

    // 移除自动触发器，改为手动触发

    /**
     * 聊天源任务监控
     * 每10分钟执行一次
     */
    @Bean
    public JobDetail chatSourceTaskMonitorJobDetail() {
        return JobBuilder.newJob(ChatSourceTaskMonitor.class)
                .withIdentity("chatSourceTaskMonitorJob")
                .storeDurably()
                .build();
    }

    @Bean
    public Trigger chatSourceTaskMonitorJobTrigger() {
        return TriggerBuilder.newTrigger()
                .forJob(chatSourceTaskMonitorJobDetail())
                .withIdentity("chatSourceTaskMonitorTrigger")
                .withSchedule(CronScheduleBuilder.cronSchedule("0 */10 * * * ?")) // 每10分钟执行一次
                .build();
    }
}
