package org.jeecg.modules.info.config;

import org.jeecg.modules.info.job.DashboardMetricsJob;
import org.quartz.*;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 仪表板指标数据定时任务配置
 */
@Configuration
public class DashboardMetricsJobConfig {

    /**
     * 仪表板指标数据更新任务
     * 每5分钟执行一次
     */
    @Bean
    public JobDetail dashboardMetricsJobDetail() {
        return JobBuilder.newJob(DashboardMetricsJob.class)
                .withIdentity("dashboardMetricsJob")
                .withDescription("仪表板指标数据更新任务")
                .storeDurably()
                .build();
    }

    @Bean
    public Trigger dashboardMetricsJobTrigger() {
        return TriggerBuilder.newTrigger()
                .forJob(dashboardMetricsJobDetail())
                .withIdentity("dashboardMetricsJobTrigger")
                .withDescription("仪表板指标数据更新任务触发器")
                .withSchedule(CronScheduleBuilder.cronSchedule("0 */5 * * * ?")) // 每5分钟执行一次
                .build();
    }
}
