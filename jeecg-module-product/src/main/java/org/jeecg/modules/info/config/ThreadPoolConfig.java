package org.jeecg.modules.info.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * 线程池配置
 */
@Configuration
public class ThreadPoolConfig {

    /**
     * 聊天源任务处理线程池
     * 核心线程数：50
     * 最大线程数：200
     * 队列容量：500
     * 线程空闲时间：60秒
     */
    @Bean(name = "chatSourceTaskExecutor")
    public ExecutorService chatSourceTaskExecutor() {
        return new ThreadPoolExecutor(
                50,                         // 核心线程数
                200,                        // 最大线程数
                60L,                        // 线程空闲时间
                TimeUnit.SECONDS,           // 时间单位
                new LinkedBlockingQueue<>(500), // 队列容量
                new ThreadPoolExecutor.CallerRunsPolicy() // 拒绝策略：由调用者线程执行
        );
    }
}
