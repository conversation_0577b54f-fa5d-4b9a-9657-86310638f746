package org.jeecg.modules.info.constant;

/**
 * 聊天源生成任务相关的 Redis 键
 */
public enum ChatSourceRedisKeys {

    /**
     * 待处理任务队列
     * 格式: chat_source_tasks:{linkType}
     */
    TASK_QUEUE("chat_source_tasks:%d"),

    /**
     * 处理中任务集合
     * 格式: chat_source_processing:{linkType}
     */
    PROCESSING_SET("chat_source_processing:%d"),

    /**
     * 成功任务集合
     * 格式: chat_source_success:{linkType}
     */
    SUCCESS_SET("chat_source_success:%d"),

    /**
     * 失败任务集合
     * 格式: chat_source_failed:{linkType}
     */
    FAILED_SET("chat_source_failed:%d"),

    /**
     * 任务详情哈希表
     * 格式: chat_source_task:{taskId}
     */
    TASK_DETAIL("chat_source_task:%s"),

    /**
     * 任务并发控制信号量
     * 格式: chat_source_semaphore
     */
    CONCURRENCY_SEMAPHORE("chat_source_semaphore"),

    /**
     * 任务统计信息
     * 格式: chat_source_stats:{linkType}
     */
    STATS("chat_source_stats:%d");

    private final String keyPattern;

    ChatSourceRedisKeys(String keyPattern) {
        this.keyPattern = keyPattern;
    }

    /**
     * 获取格式化后的键
     * @param args 格式化参数
     * @return 格式化后的键
     */
    public String getKey(Object... args) {
        return String.format(keyPattern, args);
    }
}
