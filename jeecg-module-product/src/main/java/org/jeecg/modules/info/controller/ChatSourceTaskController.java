package org.jeecg.modules.info.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.modules.info.entity.PdChatSource;
import org.jeecg.modules.info.service.IChatSourceTaskService;
import org.jeecg.modules.info.service.IPdChatSourceService;
import org.jeecg.modules.info.vo.ChatSourceQueueStatsListVO;
import org.jeecg.modules.info.vo.ChatSourceQueueStatsVO;
import org.quartz.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * 聊天源任务控制器
 * 用于手动触发聊天源相关任务
 */
@Slf4j
@RestController
@RequestMapping("/info/chatSourceTask")
@Api(tags = "聊天源任务管理")
public class ChatSourceTaskController {

    @Autowired
    private Scheduler scheduler;

    @Autowired
    private IChatSourceTaskService chatSourceTaskService;

    @Autowired
    private IPdChatSourceService pdChatSourceService;

    /**
     * 手动触发聊天源任务消费者
     * @return 触发结果
     */
    @GetMapping("/triggerConsumer")
    @ApiOperation(value = "手动触发聊天源任务消费者", notes = "手动触发聊天源任务消费者，处理Redis队列中的任务")
    public Result<String> triggerChatSourceTaskConsumer() {
        try {
            // 获取任务详情
            JobKey jobKey = JobKey.jobKey("chatSourceTaskConsumerJob");
            JobDetail jobDetail = scheduler.getJobDetail(jobKey);

            if (jobDetail == null) {
                return Result.error("任务不存在");
            }

            // 创建一次性触发器
            Trigger trigger = TriggerBuilder.newTrigger()
                    .forJob(jobKey)
                    .withIdentity("manualTrigger_" + System.currentTimeMillis())
                    .startNow()
                    .build();

            // 调度任务
            scheduler.scheduleJob(trigger);

            log.info("已手动触发聊天源任务消费者");
            return Result.OK("已成功触发聊天源任务消费者");
        } catch (Exception e) {
            log.error("触发聊天源任务消费者失败", e);
            return Result.error("触发失败: " + e.getMessage());
        }
    }

    /**
     * 手动触发聊天源更新任务
     * @return 触发结果
     */
    @GetMapping("/triggerUpdate")
    @ApiOperation(value = "手动触发聊天源更新任务", notes = "手动触发聊天源更新任务，将任务添加到Redis队列")
    public Result<String> triggerChatSourceUpdateJob() {
        try {
            // 获取任务详情
            JobKey jobKey = JobKey.jobKey("chatSourceUpdateJob");
            JobDetail jobDetail = scheduler.getJobDetail(jobKey);

            if (jobDetail == null) {
                return Result.error("任务不存在");
            }

            // 创建一次性触发器
            Trigger trigger = TriggerBuilder.newTrigger()
                    .forJob(jobKey)
                    .withIdentity("manualTrigger_" + System.currentTimeMillis())
                    .startNow()
                    .build();

            // 调度任务
            scheduler.scheduleJob(trigger);

            log.info("已手动触发聊天源更新任务");
            return Result.OK("已成功触发聊天源更新任务");
        } catch (Exception e) {
            log.error("触发聊天源更新任务失败", e);
            return Result.error("触发失败: " + e.getMessage());
        }
    }

    /**
     * 手动触发聊天源任务监控
     * @return 触发结果
     */
    @GetMapping("/triggerMonitor")
    @ApiOperation(value = "手动触发聊天源任务监控", notes = "手动触发聊天源任务监控，检查任务执行情况")
    public Result<String> triggerChatSourceTaskMonitor() {
        try {
            // 获取任务详情
            JobKey jobKey = JobKey.jobKey("chatSourceTaskMonitorJob");
            JobDetail jobDetail = scheduler.getJobDetail(jobKey);

            if (jobDetail == null) {
                return Result.error("任务不存在");
            }

            // 创建一次性触发器
            Trigger trigger = TriggerBuilder.newTrigger()
                    .forJob(jobKey)
                    .withIdentity("manualTrigger_" + System.currentTimeMillis())
                    .startNow()
                    .build();

            // 调度任务
            scheduler.scheduleJob(trigger);

            log.info("已手动触发聊天源任务监控");
            return Result.OK("已成功触发聊天源任务监控");
        } catch (Exception e) {
            log.error("触发聊天源任务监控失败", e);
            return Result.error("触发失败: " + e.getMessage());
        }
    }

    /**
     * 获取队列统计信息
     * @return 队列统计信息
     */
    @GetMapping("/stats")
    @ApiOperation(value = "获取队列统计信息", notes = "获取聊天源任务队列的统计信息，包括待处理、处理中、成功、失败的任务数量")
    public Result<ChatSourceQueueStatsListVO> getQueueStats() {
        try {
            // 创建返回对象
            ChatSourceQueueStatsListVO result = new ChatSourceQueueStatsListVO();

            // 获取并发信息
            Map<String, Integer> concurrencyInfo = chatSourceTaskService.getConcurrencyInfo();
            result.setAvailableConcurrency(concurrencyInfo.get("availableConcurrency"));
            result.setUsedConcurrency(concurrencyInfo.get("usedConcurrency"));
            result.setMaxConcurrency(concurrencyInfo.get("maxConcurrency"));

            // 总计数据
            ChatSourceQueueStatsVO totalStats = new ChatSourceQueueStatsVO();
            totalStats.setLinkType(-1); // -1 表示总计
            totalStats.setAvailableConcurrency(concurrencyInfo.get("availableConcurrency"));
            totalStats.setUsedConcurrency(concurrencyInfo.get("usedConcurrency"));
            totalStats.setMaxConcurrency(concurrencyInfo.get("maxConcurrency"));

            // 获取各类型的统计信息
            for (int linkType = 0; linkType <= 2; linkType++) {
                ChatSourceQueueStatsVO typeStats = getTypeStats(linkType);
                result.getTypeStats().add(typeStats);

                // 累加到总计
                totalStats.setPendingCount(totalStats.getPendingCount() + typeStats.getPendingCount());
                totalStats.setProcessingCount(totalStats.getProcessingCount() + typeStats.getProcessingCount());
                totalStats.setSuccessCount(totalStats.getSuccessCount() + typeStats.getSuccessCount());
                totalStats.setFailedCount(totalStats.getFailedCount() + typeStats.getFailedCount());
                totalStats.setTotalCount(totalStats.getTotalCount() + typeStats.getTotalCount());
            }

            // 计算总成功率
            if (totalStats.getTotalCount() > 0) {
                totalStats.setSuccessRate(
                        Math.round(totalStats.getSuccessCount() * 10000.0 / totalStats.getTotalCount()) / 100.0
                );
            }

            result.setTotalStats(totalStats);

            return Result.OK(result);
        } catch (Exception e) {
            log.error("获取队列统计信息失败", e);
            return Result.error("获取队列统计信息失败: " + e.getMessage());
        }
    }

    /**
     * 获取指定类型的队列统计信息
     * @param linkType 链接类型
     * @return 队列统计信息
     */
    @GetMapping("/stats/{linkType}")
    @ApiOperation(value = "获取指定类型的队列统计信息", notes = "获取指定类型的聊天源任务队列的统计信息")
    public Result<ChatSourceQueueStatsVO> getQueueStatsByType(@PathVariable Integer linkType) {
        try {
            if (linkType < 0 || linkType > 2) {
                return Result.error("无效的链接类型: " + linkType);
            }

            ChatSourceQueueStatsVO stats = getTypeStats(linkType);
            return Result.OK(stats);
        } catch (Exception e) {
            log.error("获取队列统计信息失败", e);
            return Result.error("获取队列统计信息失败: " + e.getMessage());
        }
    }

    /**
     * 获取指定类型的统计信息
     * @param linkType 链接类型
     * @return 统计信息
     */
    private ChatSourceQueueStatsVO getTypeStats(Integer linkType) {
        ChatSourceQueueStatsVO stats = new ChatSourceQueueStatsVO();
        stats.setLinkType(linkType);

        // 获取并发信息
        Map<String, Integer> concurrencyInfo = chatSourceTaskService.getConcurrencyInfo();
        stats.setAvailableConcurrency(concurrencyInfo.get("availableConcurrency"));
        stats.setUsedConcurrency(concurrencyInfo.get("usedConcurrency"));
        stats.setMaxConcurrency(concurrencyInfo.get("maxConcurrency"));

        // 获取任务统计信息
        Map<String, Long> taskStats = chatSourceTaskService.getTaskStats(linkType);
        stats.setPendingCount(taskStats.getOrDefault("pending", 0L));
        stats.setProcessingCount(taskStats.getOrDefault("processing", 0L));
        stats.setSuccessCount(taskStats.getOrDefault("success", 0L));
        stats.setFailedCount(taskStats.getOrDefault("failed", 0L));

        // 获取队列长度
        long queueSize = chatSourceTaskService.getQueueSize(linkType);
        if (stats.getPendingCount() == 0 && queueSize > 0) {
            stats.setPendingCount(queueSize);
        }

        // 获取已生成的聊天源数量
        long completedCount = pdChatSourceService.lambdaQuery()
                .eq(PdChatSource::getChatType, linkType)
                .count();
        stats.getExtraStats().put("completedCount", completedCount);

        // 计算总任务数量
        stats.setTotalCount(
                stats.getPendingCount() + stats.getProcessingCount() + stats.getSuccessCount() + stats.getFailedCount()
        );

        // 计算成功率
        if (stats.getTotalCount() > 0) {
            stats.setSuccessRate(
                    Math.round(stats.getSuccessCount() * 10000.0 / stats.getTotalCount()) / 100.0
            );
        }

        return stats;
    }
}
