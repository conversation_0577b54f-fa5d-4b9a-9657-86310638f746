package org.jeecg.modules.info.controller;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.corp.dto.LedgerListDto;
import org.jeecg.modules.corp.entity.PdLedger;
import org.jeecg.modules.info.entity.PdChatSource;
import org.jeecg.modules.info.entity.PdChatSourceDet;
import org.jeecg.modules.info.entity.PdGuestUsers;
import org.jeecg.modules.info.service.IPdChatSourceDetService;
import org.jeecg.modules.info.service.IPdChatSourceService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.modules.info.vo.PdChatSourceVo;
import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: 聊天源
 * @Author: jeecg-boot
 * @Date:   2025-04-06
 * @Version: V1.0
 */
@Api(tags="聊天源")
@RestController
@RequestMapping("/info/pdChatSource")
@Slf4j
public class PdChatSourceController extends JeecgController<PdChatSource, IPdChatSourceService> {
	@Autowired
	private IPdChatSourceService pdChatSourceService;
	@Resource
	private IPdChatSourceDetService pdChatSourceDetService;
	
	/**
	 * 分页列表查询
	 * @return
	 */
	//@AutoLog(value = "聊天源-分页列表查询")
	@ApiOperation(value="聊天源-分页列表查询", notes="聊天源-分页列表查询")
	@PostMapping(value = "/list/{pageNum}/{pageSize}")
	public Result<IPage<PdChatSource>> queryPageList(@PathVariable(name = "pageNum")Long pageNum,
													 @PathVariable(name = "pageSize")Long pageSize,
													 @RequestBody LedgerListDto dto) {
		Page<PdChatSource> page = new Page<>(pageNum, pageSize);
		IPage<PdChatSource> pageList = pdChatSourceService.pageList(page,dto);

		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param pdChatSource
	 * @return
	 */
	@AutoLog(value = "聊天源-添加")
	@ApiOperation(value="聊天源-添加", notes="聊天源-添加")
	@RequiresPermissions("info:pd_chat_source:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody PdChatSource pdChatSource) {
		pdChatSourceService.save(pdChatSource);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param pdChatSource
	 * @return
	 */
	@AutoLog(value = "聊天源-编辑")
	@ApiOperation(value="聊天源-编辑", notes="聊天源-编辑")
	@RequiresPermissions("info:pd_chat_source:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody PdChatSource pdChatSource) {
		pdChatSourceService.updateById(pdChatSource);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "聊天源-通过id删除")
	@ApiOperation(value="聊天源-通过id删除", notes="聊天源-通过id删除")
	@RequiresPermissions("info:pd_chat_source:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		pdChatSourceService.removeById(id);
		//删除子表
		pdChatSourceDetService.remove(new QueryWrapper<PdChatSourceDet>().eq("pid",id));
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "聊天源-批量删除")
	@ApiOperation(value="聊天源-批量删除", notes="聊天源-批量删除")
	@RequiresPermissions("info:pd_chat_source:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.pdChatSourceService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "聊天源-通过id查询")
	@ApiOperation(value="聊天源-通过id查询", notes="聊天源-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<PdChatSource> queryById(@RequestParam(name="id",required=true) String id) {
		PdChatSource pdChatSource = pdChatSourceService.getById(id);
		if(pdChatSource==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(pdChatSource);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param pdChatSource
    */
    @RequiresPermissions("info:pd_chat_source:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, PdChatSource pdChatSource) {
        return super.exportXls(request, pdChatSource, PdChatSource.class, "聊天源");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("info:pd_chat_source:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, PdChatSource.class);
    }

}
