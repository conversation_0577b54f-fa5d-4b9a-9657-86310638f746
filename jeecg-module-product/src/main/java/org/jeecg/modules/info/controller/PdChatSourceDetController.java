package org.jeecg.modules.info.controller;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.corp.dto.LedgerListDto;
import org.jeecg.modules.corp.entity.PdLedger;
import org.jeecg.modules.info.entity.PdChatSourceDet;
import org.jeecg.modules.info.service.IPdChatSourceDetService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: 聊天源子表
 * @Author: jeecg-boot
 * @Date:   2025-04-06
 * @Version: V1.0
 */
@Api(tags="聊天源子表")
@RestController
@RequestMapping("/info/pdChatSourceDet")
@Slf4j
public class PdChatSourceDetController extends JeecgController<PdChatSourceDet, IPdChatSourceDetService> {
	@Autowired
	private IPdChatSourceDetService pdChatSourceDetService;
	
	/**
	 * 分页列表查询
	 *
	 * @return
	 */
	//@AutoLog(value = "聊天源子表-分页列表查询")
	@ApiOperation(value="聊天源子表-分页列表查询", notes="聊天源子表-分页列表查询")
	@PostMapping(value = "/list/{pageNum}/{pageSize}")
	public Result<IPage<PdChatSourceDet>> queryPageList(@PathVariable(name = "pageNum")Long pageNum,
														@PathVariable(name = "pageSize")Long pageSize,
														@RequestBody LedgerListDto dto) {
		Page<PdChatSourceDet> page = new Page<>(pageNum, pageSize);
		IPage<PdChatSourceDet> pageList = pdChatSourceDetService.pageList(page,dto);
		return Result.OK(pageList);
	}

	 @GetMapping(value = "/getByPid")
	 public Result<List<PdChatSourceDet>> getByPid(@RequestParam(name="pid", required = true) String pid) {
		 List<PdChatSourceDet> list = pdChatSourceDetService.lambdaQuery().eq(PdChatSourceDet::getPid, pid)
				 .eq(PdChatSourceDet::getPid, pid)  // 根据 pid 查询
				 .orderByAsc(PdChatSourceDet::getNum)  // 按照 num 排序，升序排列
				 .orderByAsc(PdChatSourceDet::getSendType).list();
		 //TODO 排序下,按照 num 即第一次对话,第一个都是用户提问的,之后是用户,以此类推,最后排序好
		 return Result.OK(list);
	 }
	
	/**
	 *   添加
	 *
	 * @param pdChatSourceDet
	 * @return
	 */
	@AutoLog(value = "聊天源子表-添加")
	@ApiOperation(value="聊天源子表-添加", notes="聊天源子表-添加")
	@RequiresPermissions("info:pd_chat_source_det:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody PdChatSourceDet pdChatSourceDet) {
		pdChatSourceDetService.save(pdChatSourceDet);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param pdChatSourceDet
	 * @return
	 */
	@AutoLog(value = "聊天源子表-编辑")
	@ApiOperation(value="聊天源子表-编辑", notes="聊天源子表-编辑")
	@RequiresPermissions("info:pd_chat_source_det:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody PdChatSourceDet pdChatSourceDet) {
		pdChatSourceDetService.updateById(pdChatSourceDet);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "聊天源子表-通过id删除")
	@ApiOperation(value="聊天源子表-通过id删除", notes="聊天源子表-通过id删除")
	@RequiresPermissions("info:pd_chat_source_det:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		pdChatSourceDetService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "聊天源子表-批量删除")
	@ApiOperation(value="聊天源子表-批量删除", notes="聊天源子表-批量删除")
	@RequiresPermissions("info:pd_chat_source_det:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.pdChatSourceDetService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "聊天源子表-通过id查询")
	@ApiOperation(value="聊天源子表-通过id查询", notes="聊天源子表-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<PdChatSourceDet> queryById(@RequestParam(name="id",required=true) String id) {
		PdChatSourceDet pdChatSourceDet = pdChatSourceDetService.getById(id);
		if(pdChatSourceDet==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(pdChatSourceDet);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param pdChatSourceDet
    */
    @RequiresPermissions("info:pd_chat_source_det:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, PdChatSourceDet pdChatSourceDet) {
        return super.exportXls(request, pdChatSourceDet, PdChatSourceDet.class, "聊天源子表");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("info:pd_chat_source_det:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, PdChatSourceDet.class);
    }

}
