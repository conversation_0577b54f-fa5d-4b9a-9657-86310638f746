package org.jeecg.modules.info.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 聊天源生成任务数据传输对象
 */
@Data
@Accessors(chain = true)
public class ChatSourceTaskDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 任务ID
     */
    private String taskId;

    /**
     * 链接类型
     * 0-车险; 1-财险; 2-增值服务
     */
    private Integer linkType;

    /**
     * 创建时间戳
     */
    private Long createTime;

    /**
     * 任务状态
     * pending-待处理; processing-处理中; success-成功; failed-失败
     */
    private String status;

    /**
     * 重试次数
     */
    private Integer retryCount;

    /**
     * 最后一次错误信息
     */
    private String lastError;

    /**
     * 完成时间戳
     */
    private Long completeTime;
}
