package org.jeecg.modules.info.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class CozeConfigDto {

    /** 链接类型 */
    @ApiModelProperty(value = "链接类型(0-车险;1-财险;2-增值服务)")
    private Integer linkType;

    /** 消息内容 */
    @ApiModelProperty(value = "消息内容")
    private String messageContent;

    /** 回合数 */
    @ApiModelProperty(value = "回合数")
    private Integer roundCount;

    /** 是否启用上下文 0 否, 1 是 */
    @ApiModelProperty(value = "coze 类型")
    private Integer type;

    /** 是否启用上下文 0 否, 1 是 */
    @ApiModelProperty(value = "是否启用上下文 0否,1是")
    private Integer enableContext;

    @ApiModelProperty(value = "是否根据链接替换 message  0否,1是")
    private Integer linkChengType;

}

