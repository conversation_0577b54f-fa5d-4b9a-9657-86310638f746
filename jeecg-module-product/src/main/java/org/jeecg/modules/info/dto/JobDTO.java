package org.jeecg.modules.info.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
public class JobDTO {

    /**租户 ID*/
    @ApiModelProperty(value = "租户 ID")
    private Integer tenantId;

    @ApiModelProperty(value = "生成条数")
    private Integer num;

    @ApiModelProperty(value = "地区")
    private List<String> regionList;

    /**数据列表*/
    @ApiModelProperty(value = "数据列表")
    private List<JobDataDTO> data;
}
