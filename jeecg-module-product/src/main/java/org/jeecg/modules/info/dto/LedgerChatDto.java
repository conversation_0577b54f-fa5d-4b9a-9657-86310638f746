package org.jeecg.modules.info.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

@Data
@Accessors(chain = true)
public class LedgerChatDto {

    /**主键*/
    @ApiModelProperty(value = "预约信息主键")
    private java.lang.String id;

    @ApiModelProperty(value = "创建日期/即聊天开始日期")
    private Date createTime;

}
