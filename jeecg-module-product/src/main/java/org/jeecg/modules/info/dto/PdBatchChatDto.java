package org.jeecg.modules.info.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 生成聊天 dto , 必传字段为以下
 */
@Data
@Accessors(chain = true)
public class PdBatchChatDto {
    @ApiModelProperty(value = "台账 dto",required = true)
    private List<LedgerChatDto> pidList;

    @ApiModelProperty(value = "聊天时长",required = false)
    private Integer chatTime;

    @ApiModelProperty(value = "IP所属省份列表",required = false)
    private List<Integer> ipProvinceList;

    @ApiModelProperty(value = "生成时间",required = false)
    private Date createTime;

    @ApiModelProperty(value = "租户 id",required = true)
    private Integer tenantId;

    @ApiModelProperty(value = "客服 ip",required = false)
    private String ipAddress;

    private Map<String, LedgerChatDto> ledgerChatDtoMap;
}