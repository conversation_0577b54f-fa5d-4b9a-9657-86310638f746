package org.jeecg.modules.info.dto;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;

import java.util.List;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
public class PdSceneAddDTO {

    @Excel(name = "名称", width = 15)
    @ApiModelProperty(value = "场景名称")
    private String sceneName;
    /**业务类型*/
    @Excel(name = "业务类型", width = 15)
    @ApiModelProperty(value = "业务类型")
    private String serverType;
    /**使用状态*/
    @ApiModelProperty(value = "使用状态 0未使用,1已使用")
    private String isUse;

    @ApiModelProperty(value = "类型 1 场景库 2 姓名库")
    private Integer sourceType;

    private List<String> sceneList;
    private List<String> nameList;
}
