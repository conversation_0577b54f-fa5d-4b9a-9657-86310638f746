package org.jeecg.modules.info.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import java.util.List;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;

/**
 * @Description: 车险报价信息
 * @Author: jeecg-boot
 * @Date:   2024-11-02
 * @Version: V1.0
 */
@Data
@TableName("pd_car_info")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="pd_car_info对象", description="车险报价信息")
public class PdCarInfo implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private String id;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    @Excel(name = "时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private String updateBy;
	/**信息来源*/
    @ApiModelProperty(value = "信息来源")
    private String source;
    @ApiModelProperty(value = "ip地址")
    @Excel(name = "ip地址", width = 15)
    private String ipAddress;

    @ApiModelProperty(value = "手机号")
    @Excel(name = "手机号", width = 15)
    private String phoneNumber;

    @ApiModelProperty(value = "城市")
    @Excel(name = "城市", width = 15)
    private String city;

    @ApiModelProperty(value = "城市编码")
    @Excel(name = "城市编码", width = 15)
    private String cityCode;

    @ApiModelProperty(value = "项目")
    private transient String project;

    @ApiModelProperty(value = "游客id")
    private java.lang.String guestId;

    @ApiModelProperty(value = "游客名称")
    @Excel(name = "游客名称", width = 15)
    private java.lang.String guestName;

    @ApiModelProperty(value = "商业险单选，1-交强险; 2-第三者责任险; 3-车损险; 4-车上人员责任险; 5-盗抢险; 6-玻璃单独破碎险; 7-自燃险; 8-不计免赔险; 9-发动机涉水险")
    private String commerceInsuranceOptions;
	/**车牌号*/
	@Excel(name = "车牌号", width = 15)
    @ApiModelProperty(value = "车牌号")
    private String licensePlateNumber;
	/**所有人*/
	@Excel(name = "所有人", width = 15)
    @ApiModelProperty(value = "所有人")
    private String owner;
	/**发动机号码*/
	@Excel(name = "发动机号码", width = 15)
    @ApiModelProperty(value = "发动机号码")
    private String engineNumber;
	/**品牌型号*/
	@Excel(name = "品牌型号", width = 15)
    @ApiModelProperty(value = "品牌型号")
    private String model;
	/**使用性质*/
	@Excel(name = "使用性质", width = 15)
    @ApiModelProperty(value = "使用性质")
    private String useNature;
	/**车辆类型*/
	@Excel(name = "车辆类型", width = 15)
    @ApiModelProperty(value = "车辆类型")
    private String vehicleType;
	/**身份证号码*/
	@Excel(name = "身份证号码", width = 15)
    @ApiModelProperty(value = "身份证号码")
    private String idNumber;
	/**出生日期*/
	@Excel(name = "出生日期", width = 15)
    @ApiModelProperty(value = "出生日期")
    private String birthDate;
	/**车辆识别代码*/
	@Excel(name = "车辆识别代码", width = 15)
    @ApiModelProperty(value = "车辆识别代码")
    private String vinCode;
    @ApiModelProperty(value = " 链接 id")
    private String linkId;
	/**性别*/
	@Excel(name = "性别", width = 15)
    @ApiModelProperty(value = "性别")
    private String sex;
	/**民族*/
    @ApiModelProperty(value = "民族")
    private String ethnicity;
    @ApiModelProperty(value = "台账 id")
    private String ledgerId;
	/**删除*/
    @ApiModelProperty(value = "删除")
    private String isDelete;

    @ApiModelProperty(value = "是否已生成")
    private  Integer isVied;
	/**多租户*/
	@Excel(name = "多租户", width = 15)
    @ApiModelProperty(value = "多租户")
    private Integer tenantId;

    @ApiModelProperty(value = "多租户名称")
    @Excel(name = "多租户", width = 15)
    private transient String tenantName;
    /**是否存在聊天用户（0=否，1=是）*/
    private transient Integer hasChatUser;
}
