package org.jeecg.modules.info.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 游客表
 * @Author: jeecg-boot
 * @Date:   2024-11-12
 * @Version: V1.0
 */
@Data
@TableName("pd_guest_users")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="pd_guest_users对象", description="游客表")
public class PdGuestUsers implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private String id;
	/**游客唯一标识符，UUID*/
	@Excel(name = "游客唯一标识符，UUID", width = 15)
    @ApiModelProperty(value = "游客唯一标识符，UUID")
    private String uuid;
	/**游客名称*/
	@Excel(name = "游客名称", width = 15)
    @ApiModelProperty(value = "游客名称")
    private String name;
	/**游客偏好设置*/
	@Excel(name = "游客偏好设置", width = 15)
    @ApiModelProperty(value = "游客偏好设置")
    private String preferences;
    @ApiModelProperty(value = "租户")
    private java.lang.Integer tenantId;
}
