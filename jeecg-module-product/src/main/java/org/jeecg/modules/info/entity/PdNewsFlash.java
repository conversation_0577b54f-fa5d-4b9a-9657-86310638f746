package org.jeecg.modules.info.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 新闻快讯
 * @Author: jeecg-boot
 * @Date:   2025-05-05
 * @Version: V1.0
 */
@Data
@TableName("pd_news_flash")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="pd_news_flash对象", description="新闻快讯")
public class PdNewsFlash implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private String id;
	/**新闻时间*/
	@Excel(name = "新闻时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "新闻时间")
    private Date orderTime;
	/**标题*/
	@Excel(name = "标题", width = 15)
    @ApiModelProperty(value = "标题")
    private String content;
	/**资讯 id*/
	@Excel(name = "资讯 id", width = 15)
    @ApiModelProperty(value = "资讯 id")
    private String newsId;


    @ApiModelProperty(value = "点赞数",example = "20")
    private Integer holdNum;
	/**租户 id*/
	@Excel(name = "租户 id", width = 15)
    @ApiModelProperty(value = "租户 id")
    private Integer tenantId;
}
