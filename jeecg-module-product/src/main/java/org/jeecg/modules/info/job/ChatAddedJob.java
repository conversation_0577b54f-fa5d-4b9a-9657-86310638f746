package org.jeecg.modules.info.job;

import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.corp.dto.ChatDto;
import org.jeecg.modules.corp.entity.PdAdded;
import org.jeecg.modules.corp.service.IPdAddedService;
import org.jeecg.modules.corp.service.IPdLedgerService;
import org.jeecg.modules.corp.util.GenerateRecords;
import org.jeecg.modules.info.entity.PdCarInfo;
import org.jeecg.modules.info.entity.PdGuestUsers;
import org.jeecg.modules.info.service.IPdCarInfoService;
import org.jeecg.modules.info.service.IPdGuestUsersService;
import org.quartz.Job;
import org.quartz.JobDataMap;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

@Slf4j
@Component
public class ChatAdded<PERSON>ob implements Job {
    @Resource
    private IPdLedgerService pdLedgerService;
    @Resource
    private IPdAddedService pdAddedService;
    @Resource
    private IPdCarInfoService carInfoService;
    @Resource
    private GenerateRecords generateRecords;
    @Autowired
    private IPdGuestUsersService guestUsersService;
    @Override
    public void execute(JobExecutionContext jobExecutionContext) throws JobExecutionException {
        // 从 JobExecutionContext 获取 JobDataMap
        JobDataMap jobDataMap = jobExecutionContext.getMergedJobDataMap();

        // 获取新闻数量的参数（假设参数名为 "newsCount"）
        int newsCount = jobDataMap.getInt("newsCount");
        if (1 == 1) {
            return;
        }
        // 随机查询一条 PdCarInfo 数据
        long count = pdAddedService.lambdaQuery().eq(PdAdded::getIsVied, 0).count(); // 获取表中的总记录数
        PdAdded pdCarInfo = new PdAdded();
        if (count > 0) {
            int offset = new Random().nextInt((int) count); // 随机生成一个偏移量
            pdCarInfo = pdAddedService.lambdaQuery().eq(PdAdded::getIsVied, 0).last("LIMIT 1 OFFSET " + offset).one();
            PdCarInfo pdCarInfo1 =new PdCarInfo();
            pdCarInfo1.setId(pdCarInfo.getId());
            pdCarInfo1.setIsVied(1);
            carInfoService.updateById(pdCarInfo1);
        }

        // 获取关联的用户信息
        PdGuestUsers guestUser = guestUsersService.getById(pdCarInfo.getGuestId());

        // 获取商业险选项字符串，例如 "1,2,3"

        // 将商业险选项字符串转化为对应的险种描述
        try {
            ChatDto chatDto = new ChatDto();
            chatDto.setIp(pdCarInfo.getIpAddress())
                    .setUser(guestUser)
                    .setLinkType(2)
                    .setTenantId(pdCarInfo.getTenantId())
                    .setUserItem("增值服务")
                    .setDate(null);
            // 调用记录生成方法并传递相关参数
            generateRecords.processChatRecords(chatDto);
            pdCarInfo.setIsVied(1);
            pdAddedService.updateById(pdCarInfo);
            log.info("执行完成，睡眠 3s");

            // 睡眠 3 秒
            Thread.sleep(3000);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }

    /**
     * 将商业险选项字符串转化为对应的险种描述
     * @param options 商业险选项字符串（例如 "1,2,3"）
     * @return 对应的险种描述（例如 "交强险, 第三者责任险, 车损险"）
     */
    private String convertInsuranceOptionsToText(String options) {
        // 定义险种映射
        Map<String, String> insuranceMap = new HashMap<>();
        insuranceMap.put("1", "交强险");
        insuranceMap.put("2", "第三者责任险");
        insuranceMap.put("3", "车损险");
        insuranceMap.put("4", "车上人员责任险");
        insuranceMap.put("5", "盗抢险");
        insuranceMap.put("6", "玻璃单独破碎险");
        insuranceMap.put("7", "自燃险");
        insuranceMap.put("8", "不计免赔险");
        insuranceMap.put("9", "发动机涉水险");

        // 如果为空或空字符串，随机选择一个险种返回
        if (options == null || options.isEmpty()) {
            List<String> randomValues = new ArrayList<>(insuranceMap.values());
            int randomIndex = new Random().nextInt(randomValues.size());
            return randomValues.get(randomIndex);
        }

        // 转化选项为对应的描述
        String[] selectedOptions = options.split(",");
        List<String> descriptions = new ArrayList<>();
        for (String option : selectedOptions) {
            String description = insuranceMap.get(option.trim());
            if (description != null) {
                descriptions.add(description);
            }
        }

        return String.join(", ", descriptions);
    }


}