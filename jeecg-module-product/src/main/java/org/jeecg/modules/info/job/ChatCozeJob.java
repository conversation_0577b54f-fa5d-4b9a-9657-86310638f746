package org.jeecg.modules.info.job;

import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.corp.util.ChatAlgorithmUtil;
import org.jeecg.modules.info.dto.CozeConfigDto;
import org.jeecg.modules.info.util.SendMessageAiUtil;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * 按照工作流,生成聊天源
 */
@Slf4j
@Component
public class ChatCozeJob implements Job {
    @Resource
    private ChatAlgorithmUtil chatAlgorithmUtil;
    @Resource
    private SendMessageAiUtil sendMessageAiUtil;

    private static final int MAX_CONCURRENT_CALLS = 150;
    private static final long SLEEP_DURATION = 3 * 60 * 1000;  // 3 minutes in milliseconds

    @Override
    public void execute(JobExecutionContext jobExecutionContext) throws JobExecutionException {
        // 1.获取参数配置,按照配置的条数,生成源 json
        List<String> strings = chatAlgorithmUtil.generateDiverseChatConfigs();
        if (1 == 1) {
            return;
        }
        List<CompletableFuture<Void>> futures = new ArrayList<>();
        int chuck = 0;
        int type = 0;
        CozeConfigDto configDto = new CozeConfigDto();
        for (int i = 0; i < strings.size(); i++) {
            final String s = strings.get(i);
            if (chuck == 3) {
                break;
            }
            // 2.异步调用 sendMessage.sendMessage
            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                sendMessageAiUtil.sendMessageSaveChat(configDto);
            });

            futures.add(future);
            chuck++;

            // 3.每 150 次调用后休眠 3 分钟
            if ((i + 1) % MAX_CONCURRENT_CALLS == 0) {
                log.info("Reached {} concurrent calls, sleeping for 3 minutes", MAX_CONCURRENT_CALLS);
                // 等待所有当前批次的异步任务完成
                CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
                try {
                    Thread.sleep(SLEEP_DURATION);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    log.error("Sleep was interrupted", e);
                }
                // 清空 futures 列表以准备下一批任务
                futures.clear();
            }
        }

        // 等待最后一批异步任务完成
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
    }
}

