package org.jeecg.modules.info.job;


import lombok.extern.slf4j.Slf4j;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Random;
import java.util.concurrent.TimeUnit;
@Slf4j
@Component
public class DailyDataJob implements Job {

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    private final Map<String, String> keyMapping = new LinkedHashMap<>();

    public DailyDataJob() {
        // 初始化映射
        keyMapping.put("合作公司", "partners");
        keyMapping.put("产品数", "products");
        keyMapping.put("运营指标", "metrics");
        keyMapping.put("翻页率", "noResultRatio"); // 20-50%
        keyMapping.put("无结果比率", "noResult"); // 5-20%
    }

    @Override
    public void execute(JobExecutionContext context) throws JobExecutionException {
        //log.info("开始执行 DailyDataJob 任务...");
        try {
            // 随机生成数据
            Map<String, Object> data = new HashMap<>();
            data.put("partners", generateRandomValue(30, 60));
            data.put("products", generateRandomValue(100, 500));
            data.put("metrics", generateRandomValue(1000, 7000));
            data.put("noResultRatio", String.format("%.2f%%", generateRandomValue(20, 50) + generateRandomFraction()));
           data.put("noResult", String.format("%.2f%%", generateRandomValue(5, 20) + generateRandomFraction()));


            // 存储到 Redis
            String redisKey = "dailyData:" + LocalDate.now();
            redisTemplate.opsForHash().putAll(redisKey, data);

            // 设置过期时间（7天）
            redisTemplate.expire(redisKey, 7, TimeUnit.DAYS);

            //log.info("任务完成，数据已存入 Redis，key: {}", redisKey);
        } catch (Exception e) {
            log.error("执行 DailyDataJob 时发生错误：", e);
            throw new JobExecutionException(e);
        }
    }

    /**
     * 生成随机整数
     */
    private int generateRandomValue(int min, int max) {
        return new Random().nextInt(max - min + 1) + min;
    }

    /**
     * 生成随机小数（用于百分比数据）
     */
    private double generateRandomFraction() {
        return new Random().nextDouble();
    }

    private double generatePositivePercentage() {
        return generateRandomValue(1, 10) + generateRandomFraction();
    }
}