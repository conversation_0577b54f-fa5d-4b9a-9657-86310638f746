package org.jeecg.modules.info.job;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.util.RedisUtil;
import org.jeecg.modules.demo.produce.entity.WhBackgrounConfig;
import org.jeecg.modules.demo.produce.service.IWhBackgrounConfigService;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ThreadLocalRandom;

/**
 * 仪表板指标数据定时任务
 * 用于更新用户停留时长、满意度和运营效果数据
 */
@Slf4j
@Component
public class DashboardMetricsJob implements Job {

    @Autowired
    private IWhBackgrounConfigService whBackgrounConfigService;

    @Autowired
    private RedisUtil redisUtil;

    // Redis键前缀
    private static final String REDIS_KEY_PREFIX = "dashboard:metrics:";
    private static final String LAST_UPDATE_DATE_KEY = "dashboard:last_update_date";

    // 数据范围配置
    private static final int STAY_DURATION_MIN = 5;  // 停留时长最小值（秒）
    private static final int STAY_DURATION_MAX = 20; // 停留时长最大值（秒）

    private static final int SATISFACTION_MIN = 20;  // 满意度最小值（%）
    private static final int SATISFACTION_MAX = 45;  // 满意度最大值（%）

    private static final int OPERATION_EFFECT_MIN = 100; // 运营效果最小值（%）
    private static final int OPERATION_EFFECT_MAX = 300; // 运营效果最大值（%）

    private static final int TREND_MIN = 5;  // 趋势百分比最小值
    private static final int TREND_MAX = 20; // 趋势百分比最大值

    @Override
    public void execute(JobExecutionContext jobExecutionContext) throws JobExecutionException {
        try {
            log.info("开始执行仪表板指标数据更新任务");

            // 获取配置项 home_code
            WhBackgrounConfig homeConfig = whBackgrounConfigService.getOne(
                new LambdaQueryWrapper<WhBackgrounConfig>()
                    .eq(WhBackgrounConfig::getCode, "home_code")
            );

            if (homeConfig == null) {
                log.warn("未找到 home_code 配置项，跳过执行");
                return;
            }

            String today = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            String lastUpdateDate = (String) redisUtil.get(LAST_UPDATE_DATE_KEY);

            boolean isNewDay = !today.equals(lastUpdateDate);

            // 更新主要指标值
            updateMainMetrics(homeConfig);

            // 如果是新的一天，更新图表数据
            if (isNewDay) {
                updateChartData(homeConfig);
                redisUtil.set(LAST_UPDATE_DATE_KEY, today);
                log.info("检测到新的一天，已更新图表数据");
            }

            // 保存配置
            whBackgrounConfigService.updateById(homeConfig);

            log.info("仪表板指标数据更新任务执行完成");

        } catch (Exception e) {
            log.error("执行仪表板指标数据更新任务时发生错误", e);
            throw new JobExecutionException(e);
        }
    }

    /**
     * 更新主要指标值
     */
    private void updateMainMetrics(WhBackgrounConfig config) {
        // 更新满意度（存储在 companySum 字段，单位：%）
        int satisfaction = generateRandomValue(SATISFACTION_MIN, SATISFACTION_MAX);
        config.setCompanySum(String.valueOf(satisfaction));

        // 更新用户停留时长（存储在 activeProduct 字段，单位：秒）
        int stayDuration = generateRandomValue(STAY_DURATION_MIN, STAY_DURATION_MAX);
        config.setActiveProduct(String.valueOf(stayDuration));

        // 更新运营效果（存储在 percent 字段，单位：%）
        int operationEffect = generateRandomValue(OPERATION_EFFECT_MIN, OPERATION_EFFECT_MAX);
        config.setPercent(String.valueOf(operationEffect));

        log.info("更新主要指标 - 满意度: {}%, 停留时长: {}秒, 运营效果: {}%",
                satisfaction, stayDuration, operationEffect);
    }

    /**
     * 更新图表数据（曲线图和柱状图）
     */
    private void updateChartData(WhBackgrounConfig config) {
        // 生成曲线图数据（用户停留时长的历史数据）
        List<Integer> stayDurationChart = generateChartData(STAY_DURATION_MIN, STAY_DURATION_MAX, 5);

        // 生成柱状图数据（满意度的历史数据）
        List<Integer> satisfactionChart = generateChartData(SATISFACTION_MIN, SATISFACTION_MAX, 5);

        // 将满意度柱状图数据存储在 conversion 字段中（前端使用 integerList）
        String chartData = String.join(",", satisfactionChart.stream().map(String::valueOf).toArray(String[]::new));
        config.setConversion(chartData);

        // 存储停留时长曲线图数据到 Redis（前端可能需要单独获取）
        String stayDurationKey = REDIS_KEY_PREFIX + "stay_duration_chart";
        redisUtil.set(stayDurationKey, stayDurationChart, 24 * 60 * 60); // 缓存24小时

        // 生成运营效果的周同比和日同比数据
        int weeklyTrend = generateRandomValue(TREND_MIN, TREND_MAX);
        int dailyTrend = generateRandomValue(TREND_MIN, TREND_MAX);

        // 存储趋势数据到 Redis
        String trendsKey = REDIS_KEY_PREFIX + "operation_trends";
        Map<String, Integer> trends = new HashMap<>();
        trends.put("weekly", weeklyTrend);
        trends.put("daily", dailyTrend);
        redisUtil.set(trendsKey, trends, 24 * 60 * 60); // 缓存24小时

        log.info("更新图表数据 - 停留时长图表: {}, 满意度图表: {}, 周同比: {}%, 日同比: {}%",
                stayDurationChart, satisfactionChart, weeklyTrend, dailyTrend);
    }

    /**
     * 生成图表数据
     */
    private List<Integer> generateChartData(int min, int max, int count) {
        List<Integer> data = new ArrayList<>();
        for (int i = 0; i < count; i++) {
            data.add(generateRandomValue(min, max));
        }
        return data;
    }

    /**
     * 生成指定范围内的随机值
     */
    private int generateRandomValue(int min, int max) {
        return ThreadLocalRandom.current().nextInt(min, max + 1);
    }
}
