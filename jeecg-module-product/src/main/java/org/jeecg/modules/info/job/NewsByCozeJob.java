package org.jeecg.modules.info.job;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.modules.corp.util.NewsScraperBig;
import org.jeecg.modules.info.entity.PdNewsFlash;
import org.jeecg.modules.info.service.IPdNewsFlashService;
import org.jeecg.modules.info.util.AiChatHelper;
import org.jeecg.modules.wechat.dto.config.DeployConfigDTO;
import org.jeecg.modules.wechat.entity.AppNews;
import org.jeecg.modules.wechat.service.IAppNewsService;
import org.jeecg.modules.wechat.service.ISysDeployConfigService;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 获取快讯定时任务
 * "time": "22:00",
 * "title": "小米平板 7 系列享 6 年安全更新，可升安卓 19"
 */
@Slf4j
@Component
public class NewsByCozeJob implements Job {

    @Resource
    private NewsScraperBig newsScraperBig;

    @Resource
    private ISysDeployConfigService sysDeployConfigService;

    @Resource
    private IPdNewsFlashService pdNewsFlashService;

    @Resource
    private AiChatHelper chatHelper;

    @Value("${coze.newsbotid}")
    private String newsbotid;

    @Override
    public void execute(JobExecutionContext jobExecutionContext) throws JobExecutionException {
        // 获取系统配置，用于生成点赞数的随机区间
        DeployConfigDTO deployConfig = sysDeployConfigService.getDeployConfig();
        try {
            // 1. 获取 AI 快讯内容
            String aiResponse = chatHelper.getAiConversation("新闻", newsbotid);
            if (StrUtil.isBlank(aiResponse)) {
                log.warn("未获取到快讯内容");
                return;
            }

            // 2. 解析 JSON 数组
            JSONArray newsArray = JSONArray.parseArray(aiResponse);
            if (newsArray == null || newsArray.isEmpty()) {
                log.info("快讯内容为空");
                return;
            }

            // 3. 获取当天已有的快讯标题集合，避免重复
            Date todayStart = getTodayStart();
            Date todayEnd = getTodayEnd();

            List<String> existingTitles = pdNewsFlashService.lambdaQuery()
                    .between(PdNewsFlash::getOrderTime, todayStart, todayEnd)
                    .select(PdNewsFlash::getContent)
                    .list()
                    .stream()
                    .map(PdNewsFlash::getContent)
                    .collect(Collectors.toList());

            List<PdNewsFlash> saveList = new ArrayList<>();

            // 4. 插入新快讯
            for (int i = 0; i < newsArray.size(); i++) {
                JSONObject item = newsArray.getJSONObject(i);
                String title = item.getString("title");
                String time = item.getString("time");

                if (StrUtil.isBlank(title) || StrUtil.isBlank(time)) {
                    continue;
                }

                if (existingTitles.contains(title) || "调用 IT 咨询热榜函数获取资讯".equals(title)
                ) {
                    log.info("已存在快讯标题，跳过插入：" + title);
                    continue;
                }

                PdNewsFlash flash = new PdNewsFlash();
                flash.setContent(title);
                flash.setOrderTime(parseTodayTime(time));
                flash.setTenantId(0); // 如有租户信息，可替换为实际值

                // 随机决定是否设置点赞数，30%的概率不设置点赞数
                Random random = new Random();
                if (random.nextDouble() > 0.3) { // 70%的概率设置点赞数
                    int randomLikeCount;

                    // 如果deployConfig不为空，则生成的点赞数需要为配置类中的随机区间
                    if (deployConfig != null && deployConfig.getNewsLikeCountMin() != null && deployConfig.getNewsLikeCountMax() != null) {
                        // 确保最小值不大于最大值
                        int min = Math.min(deployConfig.getNewsLikeCountMin(), deployConfig.getNewsLikeCountMax());
                        int max = Math.max(deployConfig.getNewsLikeCountMin(), deployConfig.getNewsLikeCountMax());

                        // 生成随机点赞数
                        randomLikeCount = min;
                        if (max > min) {
                            randomLikeCount = min + random.nextInt(max - min + 1);
                        }
                    } else {
                        // 如果没有配置，则使用默认区间3-10
                        randomLikeCount = 3 + random.nextInt(8); // 3到10之间的随机数
                    }

                    flash.setHoldNum(randomLikeCount);
                }

                saveList.add(flash);


            }
            if (CollectionUtil.isNotEmpty(saveList)) {
                pdNewsFlashService.saveBatch(saveList);
            }

        } catch (Exception e) {
            log.error("快讯获取执行失败：", e);
        }
    }

    private Date parseTodayTime(String hhmm) {
        try {
            String today = new SimpleDateFormat("yyyy-MM-dd").format(new Date());
            String dateTimeStr = today + " " + hhmm;
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm");
            return sdf.parse(dateTimeStr);
        } catch (Exception e) {
            log.error("时间解析失败：" + hhmm, e);
            return new Date();
        }
    }

    private Date getTodayStart() {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }

    private Date getTodayEnd() {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        calendar.set(Calendar.MILLISECOND, 999);
        return calendar.getTime();
    }
}