package org.jeecg.modules.info.job;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.corp.util.NewsScraperBig;
import org.jeecg.modules.demo.firmInformation.service.IWhClickService;
import org.jeecg.modules.wechat.entity.AppNews;
import org.jeecg.modules.wechat.service.IAppNewsService;
import org.quartz.Job;
import org.quartz.JobDataMap;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
@Slf4j
@Component
public class NewsJob implements Job {

    @Resource
    private NewsScraperBig newsScraperBig;

    @Resource
    private IAppNewsService appNewsService;

    @Autowired
    private IWhClickService whClickService;
    
    @Value("${news.job.fetch.count:10}")
    private int fetchCount;

    @Override
    public void execute(JobExecutionContext jobExecutionContext) throws JobExecutionException {
        //通过 job 中参数  获取需要抓取的新闻数量打印出来
        // 从 JobExecutionContext 获取 JobDataMap
        JobDataMap jobDataMap = jobExecutionContext.getMergedJobDataMap();

        // 获取新闻数量的参数（假设参数名为 "newsCount"）
        int newsCount = jobDataMap.getInt("parameter");
        
        // 1. 获取新闻列表 - 传入需要获取的新闻数量
        List<Map<String, Object>> mapList = newsScraperBig.fetchAndSaveNews(newsCount);
        List<AppNews> appNewsList = new ArrayList<>();

        if (CollectionUtil.isEmpty(mapList)) {
            log.info("未获取到新的新闻，任务结束");
            return;
        }
        
        log.info("成功获取{}条新闻，开始处理", mapList.size());

        // 2. 处理每条新闻
        for (Map<String, Object> map : mapList) {
            String title = (String) map.get("title");
            String pubDate = (String) map.get("pubDate");
            String img = (String) map.get("img");
            String link = (String) map.get("link");

            // 由于在NewsScraperBig中已经过滤了重复标题，这里再次检查是为了防止并发情况
            int existCount = (int) appNewsService.count(new QueryWrapper<AppNews>().eq("name", title));
            if (existCount == 0) {
                // 解析正文内容
                String content = newsScraperBig.fetchAndParseArticle(link);
                if (content == null || content.length() < 50) {
                    log.warn("新闻 '{}' 内容长度不足或解析失败，将被跳过", title);
                    continue;
                }

                // 转换时间
                try {
                    SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    Date date = dateFormat.parse(pubDate);

                    AppNews appNews = new AppNews()
                            .setName(title)
                            .setContent(content)
                            .setLink(link)
                            .setImage(img)
                            .setNewsTime(date)
                            .setCreateTime(date)
                            .setType(1); // 自定义类型
                    //随机 clicksNum 2000-2500
                    int clicksNum = (int) (Math.random() * 500 + 2000);
                    String whClick = whClickService.savaWhClick(clicksNum);
                    appNews.setClicksId(whClick);

                    appNewsList.add(appNews);
                } catch (Exception e) {
                    log.error("时间解析失败：{}", e.getMessage());
                }
            } else {
                log.info("新闻已存在，跳过：{}", title);
            }
        }

        // 3. 批量保存
        if (CollectionUtil.isNotEmpty(appNewsList)) {
            appNewsService.saveBatch(appNewsList);
            log.info("成功保存 {} 条新闻", appNewsList.size());
        } else {
            log.warn("没有有效新闻可保存");
        }
    }
}
