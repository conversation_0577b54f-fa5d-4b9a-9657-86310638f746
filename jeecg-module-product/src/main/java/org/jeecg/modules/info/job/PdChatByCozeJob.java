package org.jeecg.modules.info.job;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.config.ProvinceIpGenerator;
import org.jeecg.modules.corp.dto.ChatDto;
import org.jeecg.modules.corp.entity.PdChatUser;
import org.jeecg.modules.corp.entity.PdLinkInfo;
import org.jeecg.modules.corp.service.IPdChatUserService;
import org.jeecg.modules.corp.service.IPdLedgerService;
import org.jeecg.modules.corp.service.IPdLinkInfoService;
import org.jeecg.modules.corp.util.GenerateRecords;
import org.jeecg.modules.info.dto.JobDTO;
import org.jeecg.modules.info.dto.JobDataDTO;
import org.jeecg.modules.info.entity.PdGuestUsers;
import org.jeecg.modules.info.service.IPdGuestUsersService;
import org.jeecg.modules.wechat.entity.PdChat;
import org.jeecg.modules.wechat.service.IPdChatService;
import org.quartz.*;
        import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

@Slf4j
public class PdChatByCozeJob implements Job {
    @Resource
    private IPdLinkInfoService pdLinkInfoService;
    @Resource
    private IPdChatService pdChatService;
    @Resource
    private IPdChatUserService pdChatUserService;
    @Resource
    private IPdGuestUsersService pdGuestUsersService;

    @Resource
    private GenerateRecords generateRecords;


    @Resource
    private IPdLedgerService pdLedgerService;

    private static final String BOOTGRID ="7436354095612428327";

    @Resource
    private ObjectMapper objectMapper;

    @Resource
    private RedisTemplate<String, String> redisTemplate;

    private static final String CHAT_QUEUE = "chatQueue"; // Redis 队列名称
    private static final int CONCURRENT_TASKS = 150; // 并发任务数

    @Override
    public void execute(JobExecutionContext jobExecutionContext) throws JobExecutionException {
        ExecutorService executorService = Executors.newFixedThreadPool(CONCURRENT_TASKS);
        AtomicInteger count = new AtomicInteger(0);
        List<Future<?>> futures = new ArrayList<>();

        // 尝试从队列获取最多6个任务
        for (int i = 0; i < CONCURRENT_TASKS; i++) {
            Object chatJsonObj = redisTemplate.opsForList().leftPop(CHAT_QUEUE);
            if (chatJsonObj == null) {
                break;
            }

            String chatJson = chatJsonObj.toString();
            Future<?> future = executorService.submit(() -> {
                try {
                    ChatDto chatDto = objectMapper.readValue(chatJson, ChatDto.class);
                    log.info("开始执行任务: {}", chatDto.getKid());
                    generateRecords.processChatRecords(chatDto);
                    int currentCount = count.incrementAndGet();
                    log.info("当前已处理任务数: {}", currentCount);
                } catch (JsonProcessingException e) {
                    log.error("JSON 解析失败: {}", chatJson, e);
                    log.info("丢弃该任务: {}", chatJson);
                } catch (Exception e) {
                    log.error("任务执行失败，任务将重新放回队列: {}", chatJson, e);
                    redisTemplate.opsForList().rightPush(CHAT_QUEUE, chatJson);
                }
            });
            futures.add(future);
        }

        // 等待所有任务完成
        for (Future<?> future : futures) {
            try {
                future.get();
            } catch (Exception e) {
                log.error("等待任务完成时发生错误", e);
            }
        }

        executorService.shutdown();
        try {
            if (!executorService.awaitTermination(30, TimeUnit.SECONDS)) {
                executorService.shutdownNow();
            }
        } catch (InterruptedException e) {
            executorService.shutdownNow();
            Thread.currentThread().interrupt();
        }

        log.info("总共处理了 {} 条任务", count.get());
    }





    private List<JobDTO> extractJobData(JobExecutionContext jobExecutionContext) {
        JobDetail jobDetail = jobExecutionContext.getJobDetail();
        JobDataMap mergedJobDataMap = jobDetail.getJobDataMap();
        String parameter = mergedJobDataMap.get("parameter").toString();

        // 将参数解析为 JSONObject
        JSONObject jsonObject = JSON.parseObject(parameter);

        // 解析外围的 "job" JSONArray
        JSONArray jsonArray = jsonObject.getJSONArray("job");

        // 创建一个 List 用于存储多个 JobDTO
        List<JobDTO> jobDTOList = new ArrayList<>();

        // 遍历 jsonArray，处理每个元素
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject jobObject = jsonArray.getJSONObject(i);

            // 创建 JobDTO 对象并填充数据
            JobDTO jobDTO = new JobDTO();
            jobDTO.setTenantId(jobObject.getInteger("tenant_id"));
            jobDTO.setNum(jobObject.getInteger("num"));


            // 将 jobDTO 添加到 jobDTOList 中
            jobDTOList.add(jobDTO);
        }

        // 返回 List<JobDTO> 对象
        return jobDTOList;
    }
}
