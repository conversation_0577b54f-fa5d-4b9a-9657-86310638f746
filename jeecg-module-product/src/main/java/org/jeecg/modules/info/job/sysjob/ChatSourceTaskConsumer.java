package org.jeecg.modules.info.job.sysjob;

import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.info.constant.ChatSourceRedisKeys;
import org.jeecg.modules.info.dto.ChatSourceTaskDTO;
import org.jeecg.modules.info.dto.CozeConfigDto;
import org.jeecg.modules.info.service.IChatSourceTaskService;
import org.jeecg.modules.info.util.SendMessageAiUtil;
import org.jeecg.modules.info.vo.ChatEnum;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 聊天源任务消费者
 * 定时从Redis队列中获取任务并处理
 */
@Slf4j
@Component
public class ChatSourceTaskConsumer implements Job {

    @Resource
    private IChatSourceTaskService chatSourceTaskService;

    @Resource
    private SendMessageAiUtil sendMessageAiUtil;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource(name = "chatSourceTaskExecutor")
    private ExecutorService taskExecutor;

    /**
     * 最大并发数
     */
    private static final int MAX_CONCURRENCY = 200;

    /**
     * 每次处理的任务数量
     */
    private static final int BATCH_SIZE = 70; // 增加批处理大小，充分利用并发

    @Override
    public void execute(JobExecutionContext context) throws JobExecutionException {
        log.info("ChatSourceTaskConsumer 开始执行... [手动触发]");

        try {
            // 获取当前可用的并发数
            int availableConcurrency = getAvailableConcurrency();
            log.info("当前可用并发数: {}", availableConcurrency);

            if (availableConcurrency <= 0) {
                log.warn("当前没有可用的并发槽位，跳过本次执行");
                return;
            }

            // 计算本次可以处理的任务数量
            int batchSize = Math.min(availableConcurrency, BATCH_SIZE);
            log.info("本次处理批次大小: {}", batchSize);

            // 处理各类型的任务
            for (int linkType = 0; linkType <= 2; linkType++) {
                try {
                    // 检查队列长度
                    String queueKey = ChatSourceRedisKeys.TASK_QUEUE.getKey(linkType);
                    Long queueSize = stringRedisTemplate.opsForList().size(queueKey);
                    log.info("类型 {} 的任务队列长度: {}", linkType, queueSize);

                    if (queueSize != null && queueSize > 0) {
                        log.info("开始处理类型 {} 的任务队列", linkType);
                        processTasksForType(linkType, batchSize);
                    } else {
                        log.info("类型 {} 的任务队列为空，跳过处理", linkType);
                    }
                } catch (Exception e) {
                    log.error("处理类型 {} 的任务时发生异常: {}", linkType, e.getMessage(), e);
                }
            }
        } catch (Exception e) {
            log.error("ChatSourceTaskConsumer 执行过程中发生异常: {}", e.getMessage(), e);
            throw new JobExecutionException("任务执行失败", e);
        } finally {
            log.info("ChatSourceTaskConsumer 执行完成");
        }
    }

    /**
     * 处理指定类型的任务
     * @param linkType 链接类型
     * @param batchSize 批处理大小
     */
    private void processTasksForType(int linkType, int batchSize) {
        log.info("开始处理类型 {} 的任务，最大批次大小: {}", linkType, batchSize);

        // 获取任务统计信息
        Map<String, Long> stats = chatSourceTaskService.getTaskStats(linkType);
        long pendingCount = stats.getOrDefault("pending", 0L);
        log.info("类型 {} 的待处理任务数量: {}", linkType, pendingCount);

        if (pendingCount <= 0) {
            log.info("类型 {} 没有待处理的任务，跳过", linkType);
            return; // 没有待处理的任务
        }

        // 计算实际获取的任务数量
        int actualBatchSize = (int) Math.min(pendingCount, batchSize);
        log.info("类型 {} 实际处理任务数量: {}", linkType, actualBatchSize);

        // 获取任务
        List<ChatSourceTaskDTO> tasks = chatSourceTaskService.dequeueTasks(linkType, actualBatchSize);
        log.info("类型 {} 实际获取到 {} 个任务", linkType, tasks.size());

        if (tasks.isEmpty()) {
            log.warn("类型 {} 没有获取到任务，可能是队列为空或任务已被其他消费者处理", linkType);
            return;
        }

        // 获取并发信号量
        acquireConcurrencyPermits(tasks.size());
        log.info("已获取 {} 个并发许可", tasks.size());

        try {
            // 创建 CompletableFuture 列表
            List<CompletableFuture<Boolean>> futures = new ArrayList<>();
            AtomicInteger successCount = new AtomicInteger(0);

            // 并发处理任务
            for (ChatSourceTaskDTO task : tasks) {
                log.info("提交任务到线程池: {}, 类型: {}, 状态: {}, 重试次数: {}",
                        task.getTaskId(), task.getLinkType(), task.getStatus(), task.getRetryCount());

                // 使用 CompletableFuture 和自定义线程池异步处理任务
                CompletableFuture<Boolean> future = CompletableFuture.supplyAsync(() -> {
                    try {
                        log.info("开始执行任务: {}", task.getTaskId());
                        boolean result = processTaskInternal(task);
                        if (result) {
                            successCount.incrementAndGet();
                        }
                        return result;
                    } catch (Exception e) {
                        log.error("处理任务 {} 异常: {}", task.getTaskId(), e.getMessage(), e);
                        return false;
                    }
                }, taskExecutor);

                futures.add(future);
            }

            // 等待所有任务完成
            try {
                // 等待所有任务完成，最多等待 10 分钟
                CompletableFuture<Void> allFutures = CompletableFuture.allOf(
                        futures.toArray(new CompletableFuture[0])
                );
                allFutures.get(10, TimeUnit.MINUTES);

                log.info("类型 {} 的所有任务已完成，成功: {}, 总数: {}",
                        linkType, successCount.get(), tasks.size());
            } catch (Exception e) {
                log.warn("等待任务完成时发生异常: {}", e.getMessage(), e);

                // 统计已完成的任务数量
                long completedCount = futures.stream()
                        .filter(CompletableFuture::isDone)
                        .count();

                log.info("类型 {} 的任务部分完成，已完成: {}, 总数: {}",
                        linkType, completedCount, tasks.size());
            }
        } finally {
            // 释放并发信号量
            releaseConcurrencyPermits(tasks.size());
            log.info("已释放 {} 个并发许可", tasks.size());
        }

        log.info("类型 {} 的任务处理完成", linkType);
    }

    /**
     * 处理单个任务（内部方法，由 CompletableFuture 调用）
     * @param task 任务
     * @return 处理结果
     */
    private boolean processTaskInternal(ChatSourceTaskDTO task) {
        try {
            // 创建配置
            CozeConfigDto configDto = new CozeConfigDto()
                    .setType(ChatEnum.COZE_FLOW.getCode())
                    .setLinkType(task.getLinkType())
                    .setLinkChengType(1);

            // 执行任务
            boolean success = sendMessageAiUtil.sendMessageSaveChatSync(configDto);

            // 更新任务状态
            if (success) {
                chatSourceTaskService.markTaskSuccess(task.getTaskId(), task.getLinkType());
                return true;
            } else {
                String error = "生成聊天源失败，可能是AI响应为空或解析失败";
                chatSourceTaskService.markTaskFailed(task.getTaskId(), task.getLinkType(), error);

                // 如果重试次数未达上限，尝试重试
                if (task.getRetryCount() < 3) {
                    chatSourceTaskService.retryTask(task.getTaskId(), task.getLinkType());
                }
                return false;
            }
        } catch (Exception e) {
            log.error("处理任务 {} 异常: {}", task.getTaskId(), e.getMessage(), e);
            chatSourceTaskService.markTaskFailed(task.getTaskId(), task.getLinkType(), e.getMessage());

            // 如果重试次数未达上限，尝试重试
            if (task.getRetryCount() < 3) {
                chatSourceTaskService.retryTask(task.getTaskId(), task.getLinkType());
            }
            return false;
        }
    }

    /**
     * 获取当前可用的并发数
     * @return 可用并发数
     */
    private int getAvailableConcurrency() {
        String semaphoreKey = ChatSourceRedisKeys.CONCURRENCY_SEMAPHORE.getKey();
        String value = stringRedisTemplate.opsForValue().get(semaphoreKey);

        int usedConcurrency = 0;
        if (value != null) {
            try {
                usedConcurrency = Integer.parseInt(value);
            } catch (NumberFormatException e) {
                log.warn("解析并发信号量值失败: {}", value);
            }
        }

        return MAX_CONCURRENCY - usedConcurrency;
    }

    /**
     * 获取并发信号量
     * @param count 获取数量
     */
    private void acquireConcurrencyPermits(int count) {
        String semaphoreKey = ChatSourceRedisKeys.CONCURRENCY_SEMAPHORE.getKey();
        stringRedisTemplate.opsForValue().increment(semaphoreKey, count);

        // 设置过期时间，防止死锁
        stringRedisTemplate.expire(semaphoreKey, 10, TimeUnit.MINUTES);
    }

    /**
     * 释放并发信号量
     * @param count 释放数量
     */
    private void releaseConcurrencyPermits(int count) {
        String semaphoreKey = ChatSourceRedisKeys.CONCURRENCY_SEMAPHORE.getKey();
        Long value = stringRedisTemplate.opsForValue().decrement(semaphoreKey, count);

        // 确保不会小于0
        if (value != null && value < 0) {
            stringRedisTemplate.opsForValue().set(semaphoreKey, "0");
        }
    }
}
