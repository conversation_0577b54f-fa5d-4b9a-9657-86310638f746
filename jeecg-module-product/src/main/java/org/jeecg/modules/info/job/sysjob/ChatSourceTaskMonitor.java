package org.jeecg.modules.info.job.sysjob;

import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.info.service.IChatSourceTaskService;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;

/**
 * 聊天源任务监控
 * 定时监控任务队列状态和执行情况
 */
@Slf4j
@Component
public class ChatSourceTaskMonitor implements Job {

    @Resource
    private IChatSourceTaskService chatSourceTaskService;

    @Override
    public void execute(JobExecutionContext context) throws JobExecutionException {
        // 监控各类型任务的状态
//        for (int linkType = 0; linkType <= 2; linkType++) {
//            monitorTaskType(linkType);
//        }

        // 清理过期任务（7天前的已完成任务）
        chatSourceTaskService.cleanupExpiredTasks(7);
    }

    /**
     * 监控指定类型的任务
     * @param linkType 链接类型
     */
    private void monitorTaskType(int linkType) {
//        Map<String, Long> stats = chatSourceTaskService.getTaskStats(linkType);
//
//        long pendingCount = stats.getOrDefault("pending", 0L);
//        long processingCount = stats.getOrDefault("processing", 0L);
//        long successCount = stats.getOrDefault("success", 0L);
//        long failedCount = stats.getOrDefault("failed", 0L);



    }
}
