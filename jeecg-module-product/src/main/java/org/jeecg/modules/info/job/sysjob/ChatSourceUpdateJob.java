package org.jeecg.modules.info.job.sysjob;

import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.info.entity.PdChatSource;
import org.jeecg.modules.info.entity.PdScene;
import org.jeecg.modules.info.service.IChatSourceTaskService;
import org.jeecg.modules.info.service.IPdChatSourceService;
import org.jeecg.modules.info.service.IPdSceneService;
import org.jeecg.modules.wechat.dto.config.DeployConfigDTO;
import org.jeecg.modules.wechat.service.ISysDeployConfigService;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * 更新聊天源,按照操作前台中的配置,来动态生成达标的配置文件
 * 使用Redis任务队列来管理任务
 */
@Slf4j
@Component
public class ChatSourceUpdateJob implements Job {

    @Resource
    private IPdChatSourceService pdChatSourceService;

    @Resource
    private ISysDeployConfigService deployConfigService;

    @Resource
    private IPdSceneService pdSceneService;

    @Resource
    private IChatSourceTaskService chatSourceTaskService;

    @Override
    public void execute(JobExecutionContext jobExecutionContext) throws JobExecutionException {
        // 1. 查询配置
        DeployConfigDTO deployConfig = deployConfigService.getDeployConfig();
        Integer carInsuranceCount = deployConfig.getCarInsuranceCount();
        Integer propertyInsuranceCount = deployConfig.getPropertyInsuranceCount();
        Integer valueAddedServiceCount = deployConfig.getValueAddedServiceCount();

        // 2. 映射 linkType 和目标数量
        Map<Integer, Integer> configMap = new HashMap<>();
        configMap.put(0, carInsuranceCount);         // 车险
        configMap.put(1, propertyInsuranceCount);    // 财险
        configMap.put(2, valueAddedServiceCount);    // 增值服务

        // 3. 遍历每一类配置，执行任务
        for (Map.Entry<Integer, Integer> entry : configMap.entrySet()) {
            Integer linkType = entry.getKey();
            Integer targetCount = entry.getValue();

            if (targetCount == null || targetCount <= 0) {
                continue;
            }

            // 当前已生成数量（包括已完成的任务和待处理的任务）
            long currentCount = getCurrentTotalCount(linkType);

            if (currentCount >= targetCount) {
                log.debug("当前类型 {} 已生成或正在生成 {} 个聊天源，无需生成", linkType, currentCount);
                continue;
            }

            int needToGenerate = targetCount - (int) currentCount;

            // 检查对应类型的场景库中未使用的场景数量
            String serverType = String.valueOf(linkType);
            long availableSceneCount = pdSceneService.lambdaQuery()
                    .eq(PdScene::getSourceType, 1)  // 场景库类型
                    .eq(PdScene::getServerType, serverType)
                    .eq(PdScene::getIsUse, "0")    // 未使用状态
                    .count();

            if (availableSceneCount <= 0) {
                log.warn("类型 {} 的场景库中没有可用的未使用场景，无法生成聊天源", linkType);
                continue;
            }

            // 根据可用场景数量和需要生成的数量，取较小值
            int actualGenerate = Math.min(needToGenerate, (int) availableSceneCount);
            log.info("类型 {} 需要生成 {} 个聊天源，场景库中有 {} 个可用场景，实际将生成 {} 个",
                    linkType, needToGenerate, availableSceneCount, actualGenerate);

            // 将任务添加到Redis队列
            if (actualGenerate > 0) {
                enqueueTasks(linkType, actualGenerate);
            }
        }
    }

    /**
     * 获取当前类型的总数量（已生成 + 待处理 + 处理中）
     * @param linkType 链接类型
     * @return 总数量
     */
    private long getCurrentTotalCount(Integer linkType) {
        // 已生成的聊天源数量
        long completedCount = pdChatSourceService.lambdaQuery()
                .eq(PdChatSource::getChatType, linkType)
                .count();

        // 任务队列中的数量
        Map<String, Long> taskStats = chatSourceTaskService.getTaskStats(linkType);
        long pendingCount = taskStats.getOrDefault("pending", 0L);
        long processingCount = taskStats.getOrDefault("processing", 0L);

        // 总数量 = 已生成 + 待处理 + 处理中
        return completedCount + pendingCount + processingCount;
    }

    /**
     * 将任务添加到Redis队列
     * @param linkType 链接类型
     * @param count 任务数量
     */
    private void enqueueTasks(Integer linkType, int count) {
        try {
            // 将任务添加到队列
            chatSourceTaskService.enqueueTasks(linkType, count);
            log.info("已将 {} 个类型为 {} 的聊天源生成任务添加到队列", count, linkType);
        } catch (Exception e) {
            log.error("将任务添加到队列失败: {}", e.getMessage(), e);
        }
    }
}
