package org.jeecg.modules.info.job.sysjob;

import cn.hutool.core.collection.CollectionUtil;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.corp.entity.PdAdded;
import org.jeecg.modules.corp.service.*;
import org.jeecg.modules.info.dto.PdBatchChatDto;
import org.jeecg.modules.info.entity.PdCarInfo;
import org.jeecg.modules.info.entity.PdCasualtyInfo;
import org.jeecg.modules.info.service.IPdCarInfoService;
import org.jeecg.modules.info.service.IPdCasualtyInfoService;
import org.jeecg.modules.info.service.IPdChatSourceService;
import org.quartz.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
public class PdChatJob implements Job {
    @Resource
    private IPdChatSourceService chatSourceService;

    @Resource
    private IPdCarInfoService carInfoService;
    @Resource
    private IPdCasualtyInfoService casualtyInfoService;
    @Resource
    private IPdAddedService addedService;




    @Override
    public void execute(JobExecutionContext jobExecutionContext) throws JobExecutionException {





    }


}
