package org.jeecg.modules.info.job.sysjob.config;

import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.corp.service.IClickAutoPreService;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;

import javax.annotation.Resource;
import java.text.ParseException;

/**
 * 生成台账
 */
@Slf4j
public class AutoCreateClickAllJob implements Job {
    @Resource
    private IClickAutoPreService clickAutoPreService;
    @Override
    public void execute(JobExecutionContext jobExecutionContext) throws JobExecutionException {
        try {
            //调用根据每日配置生成台账方法
            clickAutoPreService.autoCreateClickAll();
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
    }
}
