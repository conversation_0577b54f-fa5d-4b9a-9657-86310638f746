package org.jeecg.modules.info.job.sysjob.config;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.demo.produce.entity.ClickReport;
import org.jeecg.modules.demo.produce.entity.WhBackgrounConfig;
import org.jeecg.modules.demo.produce.service.IClickReportService;
import org.jeecg.modules.demo.produce.service.IWhBackgrounConfigService;
import org.jeecg.modules.system.entity.SysTenant;
import org.jeecg.modules.system.service.ISysTenantService;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * 定时刷新配置,合作公司与总点击数
 */
@Slf4j
@Component
public class BackgrounConfigJob implements Job {
    @Autowired
    private IWhBackgrounConfigService whBackgrounConfigService;
    @Autowired
    private ISysTenantService sysTenantService;
    @Autowired
    private IClickReportService clickReportService;

    @Override
    public void execute(JobExecutionContext jobExecutionContext) throws JobExecutionException {
        // 获取配置项 home_code
        WhBackgrounConfig homeCode = whBackgrounConfigService.getOne(new LambdaQueryWrapper<WhBackgrounConfig>()
                .eq(WhBackgrounConfig::getCode, "home_code"));

        // 获取未删除的租户数量
        Long count = sysTenantService.lambdaQuery().eq(SysTenant::getDelFlag, 0).count();
        homeCode.setCompanySum(String.valueOf(count));

        // 查询所有公司今日点击数合计
        Integer totalClickSum = clickReportService.getClickSum();

        // 设置点击合计数
        homeCode.setClickSum(Integer.valueOf(totalClickSum.toString()));

//        // 查询所有公司累计点击数合计
//        Long clickTotalSum = clickReportService.getClickTotalSum();
//
//        // 设置累计点击合计数
//        homeCode.setClickTotal(clickTotalSum);
        homeCode.setClickTotal(Long.valueOf(homeCode.getClickSum()));


        // 更新配置项
        whBackgrounConfigService.updateById(homeCode);
    }

}
