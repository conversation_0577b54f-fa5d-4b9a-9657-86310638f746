package org.jeecg.modules.info.job.sysjob.config;

import com.fasterxml.jackson.core.JsonProcessingException;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.corp.service.IClickAutoPreService;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;

import javax.annotation.Resource;

/**
 * 生成每日自动生成配置
 */
@Slf4j
public class GetReadyJob implements Job {
    @Resource
    private IClickAutoPreService clickAutoPreService;

    @Override
    public void execute(JobExecutionContext jobExecutionContext) throws JobExecutionException {
        log.info("预生成配置时间");
        clickAutoPreService.autoCreateClickPreAll();
    }
}
