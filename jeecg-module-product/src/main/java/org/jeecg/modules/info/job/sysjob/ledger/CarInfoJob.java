package org.jeecg.modules.info.job.sysjob.ledger;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import org.jeecg.modules.corp.entity.PdAdded;
import org.jeecg.modules.info.dto.LedgerChatDto;
import org.jeecg.modules.info.dto.PdBatchChatDto;
import org.jeecg.modules.info.entity.PdCarInfo;
import org.jeecg.modules.info.entity.PdCasualtyInfo;
import org.jeecg.modules.info.service.IPdCarInfoService;
import org.jeecg.modules.info.service.IPdChatSourceService;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.RedisTemplate;

import javax.annotation.Resource;
import java.time.Duration;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
public class CarInfoJob implements Job {

    private static final Logger log = LoggerFactory.getLogger(CarInfoJob.class);

    @Resource
    private IPdChatSourceService chatSourceService;
    @Resource
    private IPdCarInfoService carInfoService;
    @Resource
    private RedisTemplate<String, String> redisTemplate;

    private static final String RUNNING_SET_KEY = "job:carInfoJob:running-set";
    private static final int BATCH_SIZE = 500;

    @Override
    public void execute(JobExecutionContext jobExecutionContext) throws JobExecutionException {
        // 本次任务唯一标识
        String taskId = "task:" + System.currentTimeMillis() + ":" + Thread.currentThread().getId();

        // 加入运行中集合
        redisTemplate.opsForSet().add(RUNNING_SET_KEY, taskId);

        try {
            // 第一步：查询状态为 0 的前 500 条
            List<PdCarInfo> carInfos = carInfoService.lambdaQuery()
                    .select(PdCarInfo::getId, PdCarInfo::getTenantId, PdCarInfo::getCreateTime)
                    .eq(PdCarInfo::getIsVied, 0)
                    .last("LIMIT " + BATCH_SIZE)
                    .list();

            if (CollectionUtil.isEmpty(carInfos)) {
                return;
            }



            // 第二步：将状态改为 1（生成中）
            List<PdCarInfo> updateBatch = new ArrayList<>();
            for (PdCarInfo info : carInfos) {
                PdCarInfo updated = new PdCarInfo();
                updated.setId(info.getId());
                updated.setIsVied(1);
                updateBatch.add(updated);
            }
            carInfoService.updateBatchById(updateBatch);

            // 第三步：按租户处理数据
            Map<Integer, List<PdCarInfo>> carsGroupedByTenant = carInfos.stream()
                    .collect(Collectors.groupingBy(PdCarInfo::getTenantId));

            carsGroupedByTenant.forEach((tenantId, tenantList) -> {
                PdBatchChatDto pdBatchChatDto = new PdBatchChatDto();
                //查询租户 id,赋值 ip地址
                String ipAddress = chatSourceService.getIpAddressByTenantId(tenantId);
                if (StrUtil.isEmpty(ipAddress)) {
                    pdBatchChatDto.setIpAddress("**************");
                }else {
                    pdBatchChatDto.setIpAddress(ipAddress);
                }
                pdBatchChatDto.setTenantId(tenantId);
                List<LedgerChatDto> chatDtoList = new ArrayList<>();
                tenantList.forEach(entity -> {
                    LedgerChatDto chatDto = new LedgerChatDto();
                    chatDto.setId(entity.getId());
                    chatDto.setCreateTime(entity.getCreateTime());
                    chatDtoList.add(chatDto);
                });
                pdBatchChatDto.setPidList(chatDtoList);
                chatSourceService.createChatUser(pdBatchChatDto);
            });

            // 第四步：处理完成后状态改为 2（已生成）
            carInfoService.updateByPid(carInfos.stream().map(PdCarInfo::getId).collect(Collectors.toList()));

        } catch (Exception e) {
            log.error("CarInfoJob 执行异常：{}", e.getMessage(), e);
        } finally {
            // 移除当前任务标识
            redisTemplate.opsForSet().remove(RUNNING_SET_KEY, taskId);
        }
    }
}