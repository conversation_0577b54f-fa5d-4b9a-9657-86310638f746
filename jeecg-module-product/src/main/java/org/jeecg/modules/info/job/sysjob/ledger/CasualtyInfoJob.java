package org.jeecg.modules.info.job.sysjob.ledger;

import cn.hutool.core.collection.CollectionUtil;
import org.jeecg.modules.info.dto.LedgerChatDto;
import org.jeecg.modules.info.dto.PdBatchChatDto;
import org.jeecg.modules.info.entity.PdCarInfo;
import org.jeecg.modules.info.entity.PdCasualtyInfo;
import org.jeecg.modules.info.service.IPdCarInfoService;
import org.jeecg.modules.info.service.IPdCasualtyInfoService;
import org.jeecg.modules.info.service.IPdChatSourceService;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.RedisTemplate;

import javax.annotation.Resource;
import java.time.Duration;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;public class CasualtyInfoJob implements Job {

    private static final Logger log = LoggerFactory.getLogger(CasualtyInfoJob.class);

    @Resource
    private IPdChatSourceService chatSourceService;
    @Resource
    private IPdCasualtyInfoService casualtyInfoService;
    @Resource
    private IPdCarInfoService carInfoService;
    @Resource
    private RedisTemplate<String, String> redisTemplate;

    private static final String RUNNING_SET_KEY = "job:casualtyInfoJob:running-set";
    private static final int BATCH_SIZE = 500;

    @Override
    public void execute(JobExecutionContext jobExecutionContext) throws JobExecutionException {
        // 本次任务唯一标识
        String taskId = "task:" + System.currentTimeMillis() + ":" + Thread.currentThread().getId();

        // 加入运行中集合
        redisTemplate.opsForSet().add(RUNNING_SET_KEY, taskId);

        try {
            // 查询待处理数据
            List<PdCasualtyInfo> casualtyInfoList = casualtyInfoService.lambdaQuery()
                    .select(PdCasualtyInfo::getId, PdCasualtyInfo::getTenantId,PdCasualtyInfo::getCreateTime)
                    .eq(PdCasualtyInfo::getIsVied, 0)
                    .last("LIMIT " + BATCH_SIZE)
                    .list();

            if (CollectionUtil.isEmpty(casualtyInfoList)) {
                return;
            }



            // 将状态改为 1（生成中）
            List<PdCasualtyInfo> updateBatch = new ArrayList<>();
            for (PdCasualtyInfo info : casualtyInfoList) {
                PdCasualtyInfo updated = new PdCasualtyInfo();
                updated.setId(info.getId());
                updated.setIsVied(1);
                updateBatch.add(updated);
            }
            casualtyInfoService.updateBatchById(updateBatch);

            // 按租户处理
            Map<Integer, List<PdCasualtyInfo>> casualtyGroupedByTenant = casualtyInfoList.stream()
                    .collect(Collectors.groupingBy(PdCasualtyInfo::getTenantId));

            casualtyGroupedByTenant.forEach((tenantId, tenantList) -> {
                PdBatchChatDto pdBatchChatDto = new PdBatchChatDto();
                pdBatchChatDto.setTenantId(tenantId);
                List<LedgerChatDto> chatDtoList = new ArrayList<>();
                tenantList.forEach(entity -> {
                    LedgerChatDto chatDto = new LedgerChatDto();
                    chatDto.setId(entity.getId());
                    chatDto.setCreateTime(entity.getCreateTime());
                    chatDtoList.add(chatDto);
                });
                pdBatchChatDto.setPidList(chatDtoList);
                chatSourceService.createChatUser(pdBatchChatDto);
            });

            // 更新车辆信息
            carInfoService.updateByCasualty(
                    casualtyInfoList.stream().map(PdCasualtyInfo::getId).collect(Collectors.toList())
            );

        } catch (Exception e) {
            log.error("CasualtyInfoJob 执行异常：{}", e.getMessage(), e);
        } finally {
            // 移除任务标识
            redisTemplate.opsForSet().remove(RUNNING_SET_KEY, taskId);
        }
    }
}