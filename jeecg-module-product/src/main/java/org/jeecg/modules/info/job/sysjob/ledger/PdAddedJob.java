package org.jeecg.modules.info.job.sysjob.ledger;

import cn.hutool.core.collection.CollectionUtil;
import org.jeecg.modules.corp.entity.PdAdded;
import org.jeecg.modules.corp.service.IPdAddedService;
import org.jeecg.modules.info.dto.LedgerChatDto;
import org.jeecg.modules.info.dto.PdBatchChatDto;
import org.jeecg.modules.info.entity.PdCasualtyInfo;
import org.jeecg.modules.info.service.IPdCarInfoService;
import org.jeecg.modules.info.service.IPdChatSourceService;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.RedisTemplate;

import javax.annotation.Resource;
import java.time.Duration;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
public class PdAddedJob implements Job {

    private static final Logger log = LoggerFactory.getLogger(PdAddedJob.class);

    @Resource
    private IPdChatSourceService chatSourceService;
    @Resource
    private IPdAddedService addedService;
    @Resource
    private IPdCarInfoService carInfoService;
    @Resource
    private RedisTemplate<String, String> redisTemplate;

    private static final String RUNNING_SET_KEY = "job:pdAddedJob:running-set";
    private static final int BATCH_SIZE = 500;

    @Override
    public void execute(JobExecutionContext jobExecutionContext) throws JobExecutionException {
        // 创建本次任务唯一 ID（可根据时间戳+线程/UUID组合）
        String taskId = "task:" + System.currentTimeMillis() + ":" + Thread.currentThread().getId();

        // 加入运行中任务集合
        redisTemplate.opsForSet().add(RUNNING_SET_KEY, taskId);

        try {
            List<PdAdded> pendingList = addedService.lambdaQuery()
                    .select(PdAdded::getId, PdAdded::getTenantId,PdAdded::getCreateTime)
                    .eq(PdAdded::getIsVied, 0)
                    .last("LIMIT " + BATCH_SIZE)
                    .list();

            if (CollectionUtil.isEmpty(pendingList)) {
                return;
            }

            // 标记为“生成中”
            List<PdAdded> updateBatch = new ArrayList<>();
            for (PdAdded added : pendingList) {
                PdAdded pdAdded = new PdAdded();
                pdAdded.setId(added.getId());
                pdAdded.setIsVied(1);
                updateBatch.add(pdAdded);
            }
            addedService.updateBatchById(updateBatch);

            // 按租户处理
            Map<Integer, List<PdAdded>> addedGroupedByTenant = pendingList.stream()
                    .collect(Collectors.groupingBy(PdAdded::getTenantId));

            addedGroupedByTenant.forEach((tenantId, tenantList) -> {
                PdBatchChatDto pdBatchChatDto = new PdBatchChatDto();
                pdBatchChatDto.setTenantId(tenantId);
                List<LedgerChatDto> chatDtoList = new ArrayList<>();
                tenantList.forEach(entity -> {
                    LedgerChatDto chatDto = new LedgerChatDto();
                    chatDto.setId(entity.getId());
                    chatDto.setCreateTime(entity.getCreateTime());
                    chatDtoList.add(chatDto);
                });
                pdBatchChatDto.setPidList(chatDtoList);
                chatSourceService.createChatUser(pdBatchChatDto);
            });

            carInfoService.updateByPdAdded(
                    pendingList.stream().map(PdAdded::getId).collect(Collectors.toList())
            );

        } catch (Exception e) {
            log.error("PdAddedJob 执行异常：{}", e.getMessage(), e);
        } finally {
            // 移除本次任务标识
            redisTemplate.opsForSet().remove(RUNNING_SET_KEY, taskId);
        }
    }
}