package org.jeecg.modules.info.job.sysjob.news;

import org.jeecg.modules.info.util.AiChatHelper;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.beans.factory.annotation.Value;

import javax.annotation.Resource;

public class New<PERSON><PERSON><PERSON>ob implements Job {
    @Resource
    private AiChatHelper chatHelper;
    @Value("${coze.newsbotid}")
    private String newsbotid;
    @Override
    public void execute(JobExecutionContext jobExecutionContext) throws JobExecutionException {

    }
}
