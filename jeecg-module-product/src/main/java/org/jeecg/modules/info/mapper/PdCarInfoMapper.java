package org.jeecg.modules.info.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.corp.dto.LedgerListDto;
import org.jeecg.modules.info.entity.PdCarInfo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * @Description: 车险报价信息
 * @Author: jeecg-boot
 * @Date:   2024-11-02
 * @Version: V1.0
 */
public interface PdCarInfoMapper extends BaseMapper<PdCarInfo> {

    void updateByPid(@Param("pidList") List<String> pidList);

    // 更新 pd_car_info 表
    void updatePdCarInfo(@Param("pidList") List<String> pidList);

    // 更新 pd_ledger 表
    void updatePdLedger(@Param("pidList") List<String> pidList);

    // 更新 pd_casualty_info 表
    void updatePdCasualtyInfo(@Param("pidList") List<String> pidList);

    // 更新 pd_integrated 表
    void updatePdIntegrated(@Param("pidList") List<String> pidList);

    // 更新 pd_added 表
    void updatePdAdded(@Param("pidList") List<String> pidList);

    // 更新 pd_added_ledger 表
    void updatePdAddedLedger(@Param("pidList") List<String> pidList);


    void updateByCasualty(@Param("pidList") List<String> pidList);

    void updateByPdAdded(@Param("pidList") List<String> pidList);

    IPage<PdCarInfo> pageList(Page<PdCarInfo> page, @Param("dto") LedgerListDto dto);

    /**
     * 根据ID列表查询车险报价信息（包含联查字段）
     *
     * @param idList ID列表
     * @return 车险报价信息列表
     */
    List<PdCarInfo> getListByIds(@Param("idList") List<String> idList);

    /**
     * 根据ID查询车险报价信息详情（包含联查字段）
     *
     * @param id 主键ID
     * @return 车险报价信息详情
     */
    PdCarInfo getDetailById(@Param("id") String id);
}
