package org.jeecg.modules.info.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.corp.dto.LedgerListDto;
import org.jeecg.modules.info.entity.PdCasualtyInfo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * @Description: 财险预约信息
 * @Author: jeecg-boot
 * @Date:   2024-11-02
 * @Version: V1.0
 */
public interface PdCasualtyInfoMapper extends BaseMapper<PdCasualtyInfo> {

    IPage<PdCasualtyInfo> pageList(Page<PdCasualtyInfo> page, @Param("dto") LedgerListDto dto);
}
