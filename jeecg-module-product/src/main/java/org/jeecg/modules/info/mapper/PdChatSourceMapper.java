package org.jeecg.modules.info.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.corp.dto.LedgerListDto;
import org.jeecg.modules.info.entity.PdChatSource;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * @Description: 聊天源
 * @Author: jeecg-boot
 * @Date:   2025-04-06
 * @Version: V1.0
 */
public interface PdChatSourceMapper extends BaseMapper<PdChatSource> {

    String getIpAddressByTenantId(@Param("tenantId") Integer tenantId);

    IPage<PdChatSource> pageList(Page<PdChatSource> page, @Param("dto") LedgerListDto dto);
}
