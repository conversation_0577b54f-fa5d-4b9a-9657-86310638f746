package org.jeecg.modules.info.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.info.entity.PdScene;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * @Description: 场景库
 * @Author: jeecg-boot
 * @Date:   2025-04-22
 * @Version: V1.0
 */
public interface PdSceneMapper extends BaseMapper<PdScene> {
    PdScene selectOneUnusedSceneWithLock(@Param("serverType") String serverType);
}
