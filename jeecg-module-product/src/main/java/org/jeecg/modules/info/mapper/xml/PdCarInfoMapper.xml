<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.info.mapper.PdCarInfoMapper">

    <update id="updateByPid" parameterType="java.util.List">
        UPDATE pd_car_info
        SET is_vied = 2
        WHERE id IN
        <foreach collection="pidList" item="pid" open="(" separator="," close=")">
            #{pid}
        </foreach>;

        UPDATE pd_ledger
        SET chat_status = 2
        WHERE id IN (
        SELECT ledger_id
        FROM pd_car_info
        WHERE id IN
        <foreach collection="pidList" item="pid" open="(" separator="," close=")">
            #{pid}
        </foreach>
        )
    </update>

    <update id="updatePdCarInfo" parameterType="java.util.List">
        UPDATE pd_car_info
        SET is_vied = 2
        WHERE id IN
        <foreach collection="pidList" item="pid" open="(" separator="," close=")">
            #{pid}
        </foreach>;
    </update>

    <update id="updatePdLedger" parameterType="java.util.List">
        UPDATE pd_ledger
        SET chat_status = 2
        WHERE id IN (
        SELECT ledger_id
        FROM pd_car_info
        WHERE id IN
        <foreach collection="pidList" item="pid" open="(" separator="," close=")">
            #{pid}
        </foreach>
        )
    </update>

    <!-- 更新 pd_casualty_info 表 -->
    <update id="updatePdCasualtyInfo" parameterType="java.util.List">
        UPDATE pd_casualty_info
        SET is_vied = 2
        WHERE id IN
        <foreach collection="pidList" item="pid" open="(" separator="," close=")">
            #{pid}
        </foreach>
    </update>

    <!-- 更新 pd_integrated 表 -->
    <update id="updatePdIntegrated" parameterType="java.util.List">
        UPDATE pd_insurance_ledger
        SET is_vied = 2
        WHERE id IN (
        SELECT ledger_id
        FROM pd_casualty_info
        WHERE id IN
        <foreach collection="pidList" item="pid" open="(" separator="," close=")">
            #{pid}
        </foreach>
        )
    </update>


    <update id="updateByCasualty" parameterType="java.util.List">
        UPDATE pd_casualty_info
        SET is_vied = 2
        WHERE id IN
        <foreach collection="pidList" item="pid" open="(" separator="," close=")">
            #{pid}
        </foreach>;

        UPDATE pd_integrated
        SET chat_status = 2
        WHERE id IN (
        SELECT ledger_id
        FROM pd_casualty_info
        WHERE id IN
        <foreach collection="pidList" item="pid" open="(" separator="," close=")">
            #{pid}
        </foreach>
        )
    </update>

    <update id="updateByPdAdded" parameterType="java.util.List">
        UPDATE pd_added
        SET is_vied = 2
        WHERE id IN
        <foreach collection="pidList" item="pid" open="(" separator="," close=")">
            #{pid}
        </foreach>;

        UPDATE pd_added_ledger
        SET is_vied = 2
        WHERE id IN (
        SELECT ledger_id
        FROM pd_added
        WHERE id IN
        <foreach collection="pidList" item="pid" open="(" separator="," close=")">
            #{pid}
        </foreach>
        )
    </update>

    <!-- 更新 pd_added 表 -->
    <update id="updatePdAdded" parameterType="java.util.List">
        UPDATE pd_added
        SET is_vied = 2
        WHERE id IN
        <foreach collection="pidList" item="pid" open="(" separator="," close=")">
            #{pid}
        </foreach>;
    </update>

    <!-- 更新 pd_added_ledger 表 -->
    <update id="updatePdAddedLedger" parameterType="java.util.List">
        UPDATE pd_added_ledger
        SET is_vied = 2
        WHERE id IN (
        SELECT ledger_id
        FROM pd_added
        WHERE id IN
        <foreach collection="pidList" item="pid" open="(" separator="," close=")">
            #{pid}
        </foreach>
        )
    </update>

    <!-- 优化后的分页查询 -->
    <select id="pageList" resultType="org.jeecg.modules.info.entity.PdCarInfo">
        SELECT
        car.id,
        car.create_time AS createTime,
        car.ip_address AS ipAddress,
        car.guest_name AS guestName,
        car.city AS city,
        car.license_plate_number AS licensePlateNumber,
        car.owner AS owner,
        car.model AS model,
        car.vin_code AS vinCode,
        car.sex AS sex,
        st.name AS tenantName,
        car.phone_number AS phoneNumber
        FROM pd_car_info car
        LEFT JOIN sys_tenant st ON car.tenant_id = st.id
        <where>
            <if test="dto.tenantIds != null and dto.tenantIds.size() > 0">
                AND car.tenant_id IN
                <foreach collection="dto.tenantIds" item="tid" open="(" separator="," close=")">
                    #{tid}
                </foreach>
            </if>
            <if test="dto.startDate != null">
                AND car.create_time >= CONCAT(#{dto.startDate}, ' 00:00:00')
            </if>
            <if test="dto.endDate != null">
                AND car.create_time &lt;=  CONCAT(#{dto.endDate}, ' 23:59:59')
            </if>
            <if test="dto.keyword != null">
                AND (
                car.owner LIKE CONCAT('%', #{dto.keyword}, '%')
                OR car.license_plate_number LIKE CONCAT('%', #{dto.keyword}, '%')
                )
            </if>
        </where>
        <if test="dto.orderBy != null">
            ORDER BY ${dto.orderBy}
        </if>
        <if test="dto.orderBy == null">
            ORDER BY car.create_time DESC
        </if>
    </select>

    <!-- 根据ID列表查询车险报价信息（包含联查字段） -->
    <select id="getListByIds" resultType="org.jeecg.modules.info.entity.PdCarInfo">
        SELECT
        car.id,
        car.create_time AS createTime,
        car.ip_address AS ipAddress,
        car.guest_name AS guestName,
        car.city AS city,
        car.license_plate_number AS licensePlateNumber,
        car.owner AS owner,
        car.model AS model,
        car.vin_code AS vinCode,
        car.sex AS sex,
        st.name AS tenantName,
        car.phone_number AS phoneNumber
        FROM pd_car_info car
        LEFT JOIN sys_tenant st ON car.tenant_id = st.id
        WHERE car.id IN
        <foreach collection="idList" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        ORDER BY car.create_time DESC
    </select>

    <!-- 根据ID查询车险报价信息详情（包含联查字段） -->
    <select id="getDetailById" resultType="org.jeecg.modules.info.entity.PdCarInfo">
        SELECT
        car.id,
        car.create_time AS createTime,
        car.ip_address AS ipAddress,
        car.guest_name AS guestName,
        car.city AS city,
        car.license_plate_number AS licensePlateNumber,
        car.owner AS owner,
        car.model AS model,
        car.vin_code AS vinCode,
        car.sex AS sex,
        st.name AS tenantName,
        car.phone_number AS phoneNumber,
        (
            SELECT GROUP_CONCAT(
                CASE
                WHEN integ.input_type = 'Y' AND rel.amount IS NOT NULL AND rel.amount != ''
                THEN CONCAT(integ.name, ':', rel.amount, '万元')
                ELSE integ.name
                END
                ORDER BY rel.id SEPARATOR '，'
            )
            FROM pd_car_info_rel rel
            LEFT JOIN pd_integrated integ ON rel.mercial_id = integ.id
            WHERE rel.car_info_id = car.id
        ) AS project,
        car.engine_number AS engineNumber,
        car.use_nature AS useNature,
        car.vehicle_type AS vehicleType,
        car.id_number AS idNumber,
        car.birth_date AS birthDate,
        car.ethnicity AS ethnicity,
        car.source AS source,
        car.tenant_id AS tenantId
        FROM pd_car_info car
        LEFT JOIN sys_tenant st ON car.tenant_id = st.id
        WHERE car.id = #{id}
    </select>

</mapper>