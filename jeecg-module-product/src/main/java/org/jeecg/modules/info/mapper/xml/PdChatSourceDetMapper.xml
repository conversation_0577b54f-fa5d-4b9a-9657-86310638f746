<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.info.mapper.PdChatSourceDetMapper">

    <select id="pageList" resultType="org.jeecg.modules.info.entity.PdChatSourceDet">
        SELECT
        det.*
        FROM
        pd_chat_source_det det
        LEFT JOIN pd_chat_source src ON det.pid = src.id
        <where>
            <if test="dto.chatType != null">
                AND src.chat_type = #{dto.chatType}
            </if>
        </where>
        ORDER BY det.create_time DESC
    </select>
</mapper>