<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.info.mapper.PdChatSourceMapper">

    <select id="getIpAddressByTenantId" resultType="java.lang.String">
        SELECT ip_address FROM  sys_tenant
        WHERE id = #{tenantId}
    </select>
    <select id="pageList" resultType="org.jeecg.modules.info.entity.PdChatSource">
        SELECT
        id,
        create_time,
        chat_type,
        is_use
        FROM
        pd_chat_source
        <where>
            <if test="dto.chatType != null">
                AND chat_type = #{dto.chatType}
            </if>
            <if test="dto.isUse != null">
                AND is_use = #{dto.isUse}
            </if>
        </where>
        ORDER BY create_time DESC
    </select>
</mapper>