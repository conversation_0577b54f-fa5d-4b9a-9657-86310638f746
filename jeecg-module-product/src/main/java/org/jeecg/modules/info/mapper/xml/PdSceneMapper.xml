<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.info.mapper.PdSceneMapper">

    <select id="selectOneUnusedSceneWithLock" resultType="org.jeecg.modules.info.entity.PdScene">
        SELECT
            id, scene_name, server_type, is_use, source_type
        FROM
            pd_scene
        WHERE
            server_type = #{serverType}
          AND is_use = '0'
          AND source_type = 1
        ORDER BY id ASC
            LIMIT 1
    FOR UPDATE
    </select>
</mapper>