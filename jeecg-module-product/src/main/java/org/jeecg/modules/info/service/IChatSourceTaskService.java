package org.jeecg.modules.info.service;

import org.jeecg.modules.info.dto.ChatSourceTaskDTO;

import java.util.List;
import java.util.Map;

/**
 * 聊天源任务服务接口
 */
public interface IChatSourceTaskService {

    /**
     * 将任务添加到队列
     * @param linkType 链接类型
     * @param count 任务数量
     * @return 添加的任务ID列表
     */
    List<String> enqueueTasks(Integer linkType, int count);

    /**
     * 从队列中获取任务
     * @param linkType 链接类型
     * @param count 获取数量
     * @return 任务列表
     */
    List<ChatSourceTaskDTO> dequeueTasks(Integer linkType, int count);

    /**
     * 标记任务为成功
     * @param taskId 任务ID
     * @param linkType 链接类型
     */
    void markTaskSuccess(String taskId, Integer linkType);

    /**
     * 标记任务为失败
     * @param taskId 任务ID
     * @param linkType 链接类型
     * @param error 错误信息
     */
    void markTaskFailed(String taskId, Integer linkType, String error);

    /**
     * 重试任务
     * @param taskId 任务ID
     * @param linkType 链接类型
     * @return 是否重试成功
     */
    boolean retryTask(String taskId, Integer linkType);

    /**
     * 获取任务统计信息
     * @param linkType 链接类型
     * @return 统计信息
     */
    Map<String, Long> getTaskStats(Integer linkType);

    /**
     * 清理过期任务
     * @param days 过期天数
     * @return 清理的任务数量
     */
    int cleanupExpiredTasks(int days);

    /**
     * 获取当前并发信息
     * @return 并发信息，包含已使用并发数和最大并发数
     */
    Map<String, Integer> getConcurrencyInfo();

    /**
     * 获取队列长度
     * @param linkType 链接类型
     * @return 队列长度
     */
    long getQueueSize(Integer linkType);
}
