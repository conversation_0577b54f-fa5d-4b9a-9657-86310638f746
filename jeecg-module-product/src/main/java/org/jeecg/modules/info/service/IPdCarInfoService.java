package org.jeecg.modules.info.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.jeecg.modules.corp.dto.LedgerListDto;
import org.jeecg.modules.info.entity.PdCarInfo;
import com.baomidou.mybatisplus.extension.service.IService;
import org.jetbrains.annotations.NotNull;

import java.util.List;

/**
 * @Description: 车险报价信息
 * @Author: jeecg-boot
 * @Date:   2024-11-02
 * @Version: V1.0
 */
public interface IPdCarInfoService extends IService<PdCarInfo> {

    void updateByPid( List<String> collect);

    void updateByCasualty( List<String> collect);

    void updateByPdAdded( List<String> collect);

    IPage<PdCarInfo> pageList(Page<PdCarInfo> page, LedgerListDto dto);

    /**
     * 根据ID列表查询车险报价信息（包含联查字段）
     *
     * @param idList ID列表
     * @return 车险报价信息列表
     */
    List<PdCarInfo> getListByIds(List<String> idList);

    /**
     * 根据ID查询车险报价信息详情（包含联查字段）
     *
     * @param id 主键ID
     * @return 车险报价信息详情
     */
    PdCarInfo getDetailById(String id);
}
