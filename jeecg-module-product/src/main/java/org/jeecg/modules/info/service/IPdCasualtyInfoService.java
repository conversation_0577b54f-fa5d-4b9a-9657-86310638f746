package org.jeecg.modules.info.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.jeecg.modules.corp.dto.LedgerListDto;
import org.jeecg.modules.info.entity.PdCasualtyInfo;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * @Description: 财险预约信息
 * @Author: jeecg-boot
 * @Date:   2024-11-02
 * @Version: V1.0
 */
public interface IPdCasualtyInfoService extends IService<PdCasualtyInfo> {

    IPage<PdCasualtyInfo> pageList(Page<PdCasualtyInfo> page, LedgerListDto dto);
}
