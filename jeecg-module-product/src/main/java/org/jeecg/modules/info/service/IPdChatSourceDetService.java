package org.jeecg.modules.info.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.jeecg.modules.corp.dto.LedgerListDto;
import org.jeecg.modules.corp.entity.PdLedger;
import org.jeecg.modules.info.entity.PdChatSourceDet;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * @Description: 聊天源子表
 * @Author: jeecg-boot
 * @Date:   2025-04-06
 * @Version: V1.0
 */
public interface IPdChatSourceDetService extends IService<PdChatSourceDet> {

    IPage<PdChatSourceDet> pageList(Page<PdChatSourceDet> page, LedgerListDto dto);
}
