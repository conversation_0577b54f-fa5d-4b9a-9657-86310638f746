package org.jeecg.modules.info.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.jeecg.modules.corp.dto.LedgerListDto;
import org.jeecg.modules.info.dto.PdBatchChatDto;
import org.jeecg.modules.info.entity.PdChatSource;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * @Description: 聊天源
 * @Author: jeecg-boot
 * @Date:   2025-04-06
 * @Version: V1.0
 */
public interface IPdChatSourceService extends IService<PdChatSource> {
    //根据传参 id,生成用户与链接聊天记录关系
     void createChatUser(PdBatchChatDto dto);

    String getIpAddressByTenantId(Integer tenantId);

    IPage<PdChatSource> pageList(Page<PdChatSource> page, LedgerListDto dto);
}
