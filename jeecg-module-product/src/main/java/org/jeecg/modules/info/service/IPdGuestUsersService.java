package org.jeecg.modules.info.service;

import org.jeecg.modules.info.dto.PdBatchChatDto;
import org.jeecg.modules.info.entity.PdGuestUsers;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * @Description: 游客表
 * @Author: jeecg-boot
 * @Date:   2024-11-12
 * @Version: V1.0
 */
public interface IPdGuestUsersService extends IService<PdGuestUsers> {

    PdGuestUsers createGuestUser();

    List<PdGuestUsers> createGuestUserByNum(PdBatchChatDto dto);
}
