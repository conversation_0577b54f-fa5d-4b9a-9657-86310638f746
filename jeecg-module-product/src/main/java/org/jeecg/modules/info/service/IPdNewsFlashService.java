package org.jeecg.modules.info.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.jeecg.modules.info.entity.PdNewsFlash;
import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.wechat.vo.news.AppnewsListVO;

import java.util.List;

/**
 * @Description: 新闻快讯
 * @Author: jeecg-boot
 * @Date:   2025-05-05
 * @Version: V1.0
 */
public interface IPdNewsFlashService extends IService<PdNewsFlash> {



    /**
     * 获取快讯内容（分页）并按照日期格式化
     * @param pageNo 页码
     * @param pageSize 每页记录数
     * @return 快讯列表
     */
    AppnewsListVO getNewsFlashByPage(Integer pageNo, Integer pageSize);
}
