package org.jeecg.modules.info.service;

import org.jeecg.modules.info.entity.PdScene;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;

/**
 * @Description: 场景库
 * @Author: jeecg-boot
 * @Date:   2025-04-22
 * @Version: V1.0
 */
public interface IPdSceneService extends IService<PdScene> {
    public  String getRandomName() ;

    void importExcel(MultipartFile file, String sourceType) throws Exception;

    PdScene selectOneUnusedSceneWithLock(String serverType);
}
