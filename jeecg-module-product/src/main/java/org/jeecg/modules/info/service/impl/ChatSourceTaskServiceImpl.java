package org.jeecg.modules.info.service.impl;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.info.constant.ChatSourceRedisKeys;
import org.jeecg.modules.info.dto.ChatSourceTaskDTO;
import org.jeecg.modules.info.service.IChatSourceTaskService;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 聊天源任务服务实现
 */
@Slf4j
@Service
public class ChatSourceTaskServiceImpl implements IChatSourceTaskService {

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Override
    public List<String> enqueueTasks(Integer linkType, int count) {
        String queueKey = ChatSourceRedisKeys.TASK_QUEUE.getKey(linkType);
        List<String> taskIds = new ArrayList<>(count);

        for (int i = 0; i < count; i++) {
            // 生成唯一任务ID
            String taskId = UUID.randomUUID().toString();
            taskIds.add(taskId);

            // 创建任务对象
            ChatSourceTaskDTO task = new ChatSourceTaskDTO()
                    .setTaskId(taskId)
                    .setLinkType(linkType)
                    .setCreateTime(System.currentTimeMillis())
                    .setStatus("pending")
                    .setRetryCount(0);

            // 保存任务详情
            stringRedisTemplate.opsForValue().set(
                    ChatSourceRedisKeys.TASK_DETAIL.getKey(taskId),
                    JSON.toJSONString(task),
                    7, // 保存7天
                    TimeUnit.DAYS
            );

            // 将任务ID添加到队列
            stringRedisTemplate.opsForList().rightPush(queueKey, taskId);
        }

        // 更新统计信息
        String statsKey = ChatSourceRedisKeys.STATS.getKey(linkType);
        stringRedisTemplate.opsForHash().increment(statsKey, "pending", count);

        log.info("已将 {} 个类型为 {} 的聊天源生成任务添加到队列", count, linkType);
        return taskIds;
    }

    @Override
    public List<ChatSourceTaskDTO> dequeueTasks(Integer linkType, int count) {
        String queueKey = ChatSourceRedisKeys.TASK_QUEUE.getKey(linkType);
        String processingKey = ChatSourceRedisKeys.PROCESSING_SET.getKey(linkType);
        List<ChatSourceTaskDTO> tasks = new ArrayList<>();

        log.info("开始从队列 {} 获取任务，计划获取数量: {}", queueKey, count);

        // 检查队列长度
        Long queueSize = stringRedisTemplate.opsForList().size(queueKey);
        log.info("队列 {} 当前长度: {}", queueKey, queueSize);

        if (queueSize == null || queueSize == 0) {
            log.info("队列 {} 为空，无法获取任务", queueKey);
            return tasks;
        }

        // 尝试获取指定数量的任务
        for (int i = 0; i < count; i++) {
            String taskId = stringRedisTemplate.opsForList().leftPop(queueKey);
            if (taskId == null) {
                log.info("队列 {} 已经为空，已获取 {} 个任务", queueKey, i);
                break; // 队列为空
            }

            log.info("从队列 {} 获取到任务 ID: {}", queueKey, taskId);

            // 获取任务详情
            String taskDetailKey = ChatSourceRedisKeys.TASK_DETAIL.getKey(taskId);
            String taskJson = stringRedisTemplate.opsForValue().get(taskDetailKey);
            if (taskJson == null) {
                log.warn("任务 {} 详情不存在，键: {}", taskId, taskDetailKey);
                continue;
            }

            log.info("获取到任务 {} 详情: {}", taskId, taskJson);

            try {
                ChatSourceTaskDTO task = JSON.parseObject(taskJson, ChatSourceTaskDTO.class);
                task.setStatus("processing");

                log.info("更新任务 {} 状态为 processing", taskId);

                // 更新任务状态
                stringRedisTemplate.opsForValue().set(
                        taskDetailKey,
                        JSON.toJSONString(task),
                        7,
                        TimeUnit.DAYS
                );

                // 将任务添加到处理中集合
                stringRedisTemplate.opsForSet().add(processingKey, taskId);
                log.info("将任务 {} 添加到处理中集合 {}", taskId, processingKey);

                // 更新统计信息
                String statsKey = ChatSourceRedisKeys.STATS.getKey(linkType);
                stringRedisTemplate.opsForHash().increment(statsKey, "pending", -1);
                stringRedisTemplate.opsForHash().increment(statsKey, "processing", 1);
                log.info("更新任务统计信息: pending-1, processing+1");

                tasks.add(task);
            } catch (Exception e) {
                log.error("解析或处理任务 {} 失败: {}", taskId, e.getMessage(), e);
                // 如果处理失败，将任务重新放回队列
                stringRedisTemplate.opsForList().rightPush(queueKey, taskId);
                log.info("将任务 {} 重新放回队列 {}", taskId, queueKey);
            }
        }

        log.info("成功从队列 {} 获取 {} 个任务", queueKey, tasks.size());
        return tasks;
    }

    @Override
    public void markTaskSuccess(String taskId, Integer linkType) {
        String processingKey = ChatSourceRedisKeys.PROCESSING_SET.getKey(linkType);
        String successKey = ChatSourceRedisKeys.SUCCESS_SET.getKey(linkType);
        String taskDetailKey = ChatSourceRedisKeys.TASK_DETAIL.getKey(taskId);

        // 获取任务详情
        String taskJson = stringRedisTemplate.opsForValue().get(taskDetailKey);
        if (taskJson == null) {
            log.warn("任务 {} 详情不存在", taskId);
            return;
        }

        ChatSourceTaskDTO task = JSON.parseObject(taskJson, ChatSourceTaskDTO.class);
        task.setStatus("success");
        task.setCompleteTime(System.currentTimeMillis());

        // 更新任务状态
        stringRedisTemplate.opsForValue().set(
                taskDetailKey,
                JSON.toJSONString(task),
                7,
                TimeUnit.DAYS
        );

        // 从处理中集合移除，添加到成功集合
        stringRedisTemplate.opsForSet().remove(processingKey, taskId);
        stringRedisTemplate.opsForSet().add(successKey, taskId);

        // 更新统计信息
        String statsKey = ChatSourceRedisKeys.STATS.getKey(linkType);
        stringRedisTemplate.opsForHash().increment(statsKey, "processing", -1);
        stringRedisTemplate.opsForHash().increment(statsKey, "success", 1);

        log.info("任务 {} 已成功完成", taskId);
    }

    @Override
    public void markTaskFailed(String taskId, Integer linkType, String error) {
        String processingKey = ChatSourceRedisKeys.PROCESSING_SET.getKey(linkType);
        String failedKey = ChatSourceRedisKeys.FAILED_SET.getKey(linkType);
        String taskDetailKey = ChatSourceRedisKeys.TASK_DETAIL.getKey(taskId);

        // 获取任务详情
        String taskJson = stringRedisTemplate.opsForValue().get(taskDetailKey);
        if (taskJson == null) {
            log.warn("任务 {} 详情不存在", taskId);
            return;
        }

        ChatSourceTaskDTO task = JSON.parseObject(taskJson, ChatSourceTaskDTO.class);
        task.setStatus("failed");
        task.setLastError(error);
        task.setCompleteTime(System.currentTimeMillis());

        // 更新任务状态
        stringRedisTemplate.opsForValue().set(
                taskDetailKey,
                JSON.toJSONString(task),
                7,
                TimeUnit.DAYS
        );

        // 从处理中集合移除，添加到失败集合
        stringRedisTemplate.opsForSet().remove(processingKey, taskId);
        stringRedisTemplate.opsForSet().add(failedKey, taskId);

        // 更新统计信息
        String statsKey = ChatSourceRedisKeys.STATS.getKey(linkType);
        stringRedisTemplate.opsForHash().increment(statsKey, "processing", -1);
        stringRedisTemplate.opsForHash().increment(statsKey, "failed", 1);

        log.warn("任务 {} 执行失败: {}", taskId, error);
    }

    @Override
    public boolean retryTask(String taskId, Integer linkType) {
        String queueKey = ChatSourceRedisKeys.TASK_QUEUE.getKey(linkType);
        String processingKey = ChatSourceRedisKeys.PROCESSING_SET.getKey(linkType);
        String failedKey = ChatSourceRedisKeys.FAILED_SET.getKey(linkType);
        String taskDetailKey = ChatSourceRedisKeys.TASK_DETAIL.getKey(taskId);

        // 获取任务详情
        String taskJson = stringRedisTemplate.opsForValue().get(taskDetailKey);
        if (taskJson == null) {
            log.warn("任务 {} 详情不存在", taskId);
            return false;
        }

        ChatSourceTaskDTO task = JSON.parseObject(taskJson, ChatSourceTaskDTO.class);

        // 检查重试次数
        if (task.getRetryCount() >= 3) {
            log.warn("任务 {} 重试次数已达上限", taskId);
            return false;
        }

        // 更新任务状态
        task.setStatus("pending");
        task.setRetryCount(task.getRetryCount() + 1);

        stringRedisTemplate.opsForValue().set(
                taskDetailKey,
                JSON.toJSONString(task),
                7,
                TimeUnit.DAYS
        );

        // 从处理中或失败集合移除，重新添加到队列
        stringRedisTemplate.opsForSet().remove(processingKey, taskId);
        stringRedisTemplate.opsForSet().remove(failedKey, taskId);
        stringRedisTemplate.opsForList().rightPush(queueKey, taskId);

        // 更新统计信息
        String statsKey = ChatSourceRedisKeys.STATS.getKey(linkType);
        stringRedisTemplate.opsForHash().increment(statsKey, "processing", -1);
        stringRedisTemplate.opsForHash().increment(statsKey, "failed", -1);
        stringRedisTemplate.opsForHash().increment(statsKey, "pending", 1);

        log.info("任务 {} 已重新加入队列，重试次数: {}", taskId, task.getRetryCount());
        return true;
    }

    @Override
    public Map<String, Long> getTaskStats(Integer linkType) {
        String statsKey = ChatSourceRedisKeys.STATS.getKey(linkType);

        // 检查队列长度
        String queueKey = ChatSourceRedisKeys.TASK_QUEUE.getKey(linkType);
        Long queueSize = stringRedisTemplate.opsForList().size(queueKey);

        Map<Object, Object> rawStats = stringRedisTemplate.opsForHash().entries(statsKey);

        Map<String, Long> stats = new HashMap<>();
        for (Map.Entry<Object, Object> entry : rawStats.entrySet()) {
            String key = entry.getKey().toString();
            try {
                Long value = Long.parseLong(entry.getValue().toString());
                stats.put(key, value);
            } catch (NumberFormatException e) {
                log.warn("解析统计值失败: key={}, value={}", key, entry.getValue());
                stats.put(key, 0L);
            }
        }

        // 确保所有状态都有值
        stats.putIfAbsent("pending", 0L);
        stats.putIfAbsent("processing", 0L);
        stats.putIfAbsent("success", 0L);
        stats.putIfAbsent("failed", 0L);

        // 如果 pending 为 0 但队列不为空，则更新 pending 值
        if (stats.get("pending") == 0L && queueSize != null && queueSize > 0) {
            stats.put("pending", queueSize);
            // 更新 Redis 中的统计信息
            stringRedisTemplate.opsForHash().put(statsKey, "pending", String.valueOf(queueSize));
        }

        return stats;
    }

    @Override
    public int cleanupExpiredTasks(int days) {
        long expireTime = System.currentTimeMillis() - (days * 24 * 60 * 60 * 1000L);
        int cleanedCount = 0;

        // 清理所有类型的任务
        for (int linkType = 0; linkType <= 2; linkType++) {
            String successKey = ChatSourceRedisKeys.SUCCESS_SET.getKey(linkType);
            String failedKey = ChatSourceRedisKeys.FAILED_SET.getKey(linkType);

            // 获取成功和失败的任务ID
            Set<String> successTasks = stringRedisTemplate.opsForSet().members(successKey);
            Set<String> failedTasks = stringRedisTemplate.opsForSet().members(failedKey);

            Set<String> allTasks = new HashSet<>();
            if (!CollectionUtils.isEmpty(successTasks)) {
                allTasks.addAll(successTasks);
            }
            if (!CollectionUtils.isEmpty(failedTasks)) {
                allTasks.addAll(failedTasks);
            }

            // 检查每个任务的完成时间
            for (String taskId : allTasks) {
                String taskDetailKey = ChatSourceRedisKeys.TASK_DETAIL.getKey(taskId);
                String taskJson = stringRedisTemplate.opsForValue().get(taskDetailKey);

                if (taskJson != null) {
                    ChatSourceTaskDTO task = JSON.parseObject(taskJson, ChatSourceTaskDTO.class);

                    // 如果任务已完成且超过过期时间
                    if (task.getCompleteTime() != null && task.getCompleteTime() < expireTime) {
                        // 删除任务详情
                        stringRedisTemplate.delete(taskDetailKey);

                        // 从集合中移除
                        if ("success".equals(task.getStatus())) {
                            stringRedisTemplate.opsForSet().remove(successKey, taskId);
                        } else if ("failed".equals(task.getStatus())) {
                            stringRedisTemplate.opsForSet().remove(failedKey, taskId);
                        }

                        cleanedCount++;
                    }
                }
            }
        }

        return cleanedCount;
    }

    @Override
    public Map<String, Integer> getConcurrencyInfo() {
        String semaphoreKey = ChatSourceRedisKeys.CONCURRENCY_SEMAPHORE.getKey();
        String value = stringRedisTemplate.opsForValue().get(semaphoreKey);

        int usedConcurrency = 0;
        if (value != null) {
            try {
                usedConcurrency = Integer.parseInt(value);
            } catch (NumberFormatException e) {
                log.warn("解析并发信号量值失败: {}", value);
            }
        }

        // 最大并发数
        int maxConcurrency = 200;

        // 可用并发数
        int availableConcurrency = Math.max(0, maxConcurrency - usedConcurrency);

        Map<String, Integer> result = new HashMap<>();
        result.put("usedConcurrency", usedConcurrency);
        result.put("availableConcurrency", availableConcurrency);
        result.put("maxConcurrency", maxConcurrency);

        return result;
    }

    @Override
    public long getQueueSize(Integer linkType) {
        String queueKey = ChatSourceRedisKeys.TASK_QUEUE.getKey(linkType);
        Long size = stringRedisTemplate.opsForList().size(queueKey);
        return size != null ? size : 0;
    }
}
