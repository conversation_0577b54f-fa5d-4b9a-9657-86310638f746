package org.jeecg.modules.info.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import java.util.ArrayList;
import java.util.Random;
import java.util.UUID;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.desensitization.util.SensitiveInfoUtil;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.modules.corp.dto.LedgerListDto;
import org.jeecg.modules.info.entity.PdCarInfo;
import org.jeecg.modules.info.entity.PdGuestUsers;
import org.jeecg.modules.info.mapper.PdCarInfoMapper;
import org.jeecg.modules.info.service.IPdCarInfoService;
import org.jeecg.modules.wechat.dto.config.DeployConfigDTO;
import org.jeecg.modules.wechat.service.ISysDeployConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.util.*;

/**
 * @Description: 车险报价信息
 * @Author: jeecg-boot
 * @Date:   2024-11-02
 * @Version: V1.0
 */
@Service
public class PdCarInfoServiceImpl extends ServiceImpl<PdCarInfoMapper, PdCarInfo> implements IPdCarInfoService {

    @Autowired
    private ISysDeployConfigService sysDeployConfigService;

    @Override
    public void updateByPid(List<String> pidList) {
        if (CollectionUtil.isEmpty(pidList)) {
            return;
        }
        this.baseMapper.updatePdLedger(pidList);
        this.baseMapper.updatePdCarInfo(pidList);
    }

    @Override
    public void updateByCasualty(List<String> pidList) {
        if (CollectionUtil.isEmpty(pidList)) {
            return;
        }
        this.baseMapper.updatePdCasualtyInfo(pidList);
        this.baseMapper.updatePdIntegrated(pidList);
    }

    @Override
    public void updateByPdAdded(List<String> pidList) {
        if (CollectionUtil.isEmpty(pidList)) {
            return;
        }
        this.baseMapper.updatePdAdded(pidList);
        this.baseMapper.updatePdAddedLedger(pidList);
    }

    @Override
    public IPage<PdCarInfo> pageList(Page<PdCarInfo> page, LedgerListDto dto) {
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();

        // 检查用户是否有权限查看所有租户数据
        boolean hasAllDataPermission = sysUser != null && sysUser.getAllCheck() != null && sysUser.getAllCheck() == 1;

        // 如果用户有权限查看所有租户数据，则不需要限制租户ID
        if (!hasAllDataPermission) {
            // 用户没有权限查看所有租户数据，按照原来的逻辑限制租户ID
            List<String> tenantIds = Arrays.asList(sysUser.getRelTenantIds().split(","));
            //需要查询用户是否有这个租户的权限
            if (!StrUtil.isEmpty(dto.getTenantId())) {
                // 如果传入的租户ID不在当前用户的租户权限中，则抛出异常或返回错误
                if (!tenantIds.contains(dto.getTenantId())) {
                    dto.setTenantId(null);
                }
                // 如果用户有该租户权限，将其加入dto
                dto.setTenantIds(Collections.singletonList(dto.getTenantId()));
            } else {
                // 如果没有传入租户ID，使用当前用户的所有租户权限
                dto.setTenantIds(tenantIds);
                //如果List<String> tenantIds中包含 0
                if (tenantIds.contains("0")) {
                    dto.setTenantId(null);
                    dto.setTenantIds(null);
                }
            }
        } else {
            // 用户有权限查看所有租户数据
            // 如果传入了特定的租户ID，则只查询该租户的数据
            if (!StrUtil.isEmpty(dto.getTenantId())) {
                dto.setTenantIds(Collections.singletonList(dto.getTenantId()));
            } else {
                // 如果没有传入租户ID，则不限制租户ID，查询所有租户的数据
                dto.setTenantIds(null);
            }
        }
        if (StrUtil.isNotEmpty(dto.getDateRange())) {
            //将时间字段拆分dateRange
            String[] split = dto.getDateRange().split(",");
            dto.setStartDate(split[0]);
            dto.setEndDate(split[1]);
        }
        Random random = new Random();
        IPage<PdCarInfo> pageList = this.baseMapper.pageList(page, dto);

        // 获取系统配置
        DeployConfigDTO config = sysDeployConfigService.getDeployConfig();

        // 处理游客名称和脱敏
        pageList.getRecords().forEach(item -> {
            // 处理游客名称
            if (StrUtil.isEmpty(item.getGuestName())) {
                // 随机生成3位数字
                int randomNum = 100 + random.nextInt(900); // 生成 100-999 的随机数
                // 创建游客用户实例并设置UUID和名称
                String uuid = UUID.randomUUID().toString();
                item.setGuestName("游客"+randomNum+"_" + uuid.substring(0, 8)); // 生成一个格式统一的名称，比如"游客_xxxxxxxx"
            }

            // 处理脱敏
            // 如果数据实现方式是"查询实现脱敏"，并且"去除查询脱敏"是关闭的，则进行脱敏处理
            if ("query".equals(config.getQueryType()) && !Boolean.TRUE.equals(config.getRemoveQueryMask())) {
                // 根据配置进行脱敏处理

                // 姓名切换为先生/女士
                if (Boolean.TRUE.equals(config.getNameSwitch()) && StrUtil.isNotEmpty(item.getOwner())) {
                    // 如果姓名不为空，则将姓名后面的部分替换为"先生"或"女士"
                    if ("男".equals(item.getSex())) {
                        item.setOwner(item.getOwner().substring(0, 1) + "先生");
                    } else if ("女".equals(item.getSex())) {
                        item.setOwner(item.getOwner().substring(0, 1) + "女士");
                    } else {
                        // 如果性别未知，则使用通用脱敏
                        item.setOwner(SensitiveInfoUtil.chineseName(item.getOwner()));
                    }
                }

                // 车牌号脱敏
                if (Boolean.TRUE.equals(config.getPlateNoMask()) && StrUtil.isNotEmpty(item.getLicensePlateNumber())) {
                    // 车牌号脱敏：保留前两位和后两位，中间用星号代替
                    String plateNo = item.getLicensePlateNumber();
                    if (plateNo.length() > 4) {
                        String prefix = plateNo.substring(0, 2);
                        String suffix = plateNo.substring(plateNo.length() - 2);
                        String stars = String.join("", Collections.nCopies(plateNo.length() - 4, "*"));
                        item.setLicensePlateNumber(prefix + stars + suffix);
                    }
                }

                // 车架号脱敏
                if (Boolean.TRUE.equals(config.getVinNoMask()) && StrUtil.isNotEmpty(item.getVinCode())) {
                    // 车架号脱敏：保留前4位和后4位，中间用星号代替
                    item.setVinCode(SensitiveInfoUtil.formatBetween(item.getVinCode(), 4, 4));
                }

                // 手机号脱敏
                if (Boolean.TRUE.equals(config.getPhoneSwitch()) && StrUtil.isNotEmpty(item.getPhoneNumber())) {
                    // 手机号脱敏：前三位，后四位，其他隐藏
                    item.setPhoneNumber(SensitiveInfoUtil.mobilePhone(item.getPhoneNumber()));
                }
            }
        });

        return pageList;
    }

    @Override
    public List<PdCarInfo> getListByIds(List<String> idList) {
        if (CollectionUtil.isEmpty(idList)) {
            return new ArrayList<>();
        }

        // 调用Mapper中的方法，获取包含联查字段的数据
        List<PdCarInfo> resultList = this.baseMapper.getListByIds(idList);

        // 获取系统配置
        DeployConfigDTO config = sysDeployConfigService.getDeployConfig();

        // 处理游客名称和脱敏
        resultList.forEach(item -> {
            // 处理游客名称
            if (StrUtil.isEmpty(item.getGuestName())) {
                // 随机生成3位数字
                Random random = new Random();
                int randomNum = 100 + random.nextInt(900); // 生成 100-999 的随机数
                // 创建游客用户实例并设置UUID和名称
                String uuid = UUID.randomUUID().toString();
                item.setGuestName("游客"+randomNum+"_" + uuid.substring(0, 8)); // 生成一个格式统一的名称，比如"游客_xxxxxxxx"
            }

            // 手机号脱敏
            if (Boolean.TRUE.equals(config.getPhoneSwitch()) && StrUtil.isNotEmpty(item.getPhoneNumber())) {
                // 手机号脱敏：前三位，后四位，其他隐藏
                item.setPhoneNumber(SensitiveInfoUtil.mobilePhone(item.getPhoneNumber()));
            }
        });

        return resultList;
    }

    @Override
    public PdCarInfo getDetailById(String id) {
        if (StrUtil.isEmpty(id)) {
            return null;
        }

        // 调用Mapper中的方法，获取包含联查字段的数据
        PdCarInfo result = this.baseMapper.getDetailById(id);

        if (result == null) {
            return null;
        }

        // 获取系统配置
        DeployConfigDTO config = sysDeployConfigService.getDeployConfig();

        // 处理游客名称
        if (StrUtil.isEmpty(result.getGuestName())) {
            // 随机生成3位数字
            Random random = new Random();
            int randomNum = 100 + random.nextInt(900); // 生成 100-999 的随机数
            // 创建游客用户实例并设置UUID和名称
            String uuid = UUID.randomUUID().toString();
            result.setGuestName("游客"+randomNum+"_" + uuid.substring(0, 8)); // 生成一个格式统一的名称，比如"游客_xxxxxxxx"
        }

        // 处理脱敏
        // 如果数据实现方式是"查询实现脱敏"，并且"去除查询脱敏"是关闭的，则进行脱敏处理
        if ("query".equals(config.getQueryType()) && !Boolean.TRUE.equals(config.getRemoveQueryMask())) {
            // 根据配置进行脱敏处理

            // 姓名切换为先生/女士
            if (Boolean.TRUE.equals(config.getNameSwitch()) && StrUtil.isNotEmpty(result.getOwner())) {
                // 如果姓名不为空，则将姓名后面的部分替换为"先生"或"女士"
                if ("男".equals(result.getSex())) {
                    result.setOwner(result.getOwner().substring(0, 1) + "先生");
                } else if ("女".equals(result.getSex())) {
                    result.setOwner(result.getOwner().substring(0, 1) + "女士");
                } else {
                    // 如果性别未知，则使用通用脱敏
                    result.setOwner(SensitiveInfoUtil.chineseName(result.getOwner()));
                }
            }

            // 车牌号脱敏
            if (Boolean.TRUE.equals(config.getPlateNoMask()) && StrUtil.isNotEmpty(result.getLicensePlateNumber())) {
                // 车牌号脱敏：保留前两位和后两位，中间用星号代替
                String plateNo = result.getLicensePlateNumber();
                if (plateNo.length() > 4) {
                    String prefix = plateNo.substring(0, 2);
                    String suffix = plateNo.substring(plateNo.length() - 2);
                    String stars = String.join("", Collections.nCopies(plateNo.length() - 4, "*"));
                    result.setLicensePlateNumber(prefix + stars + suffix);
                }
            }

            // 车架号脱敏
            if (Boolean.TRUE.equals(config.getVinNoMask()) && StrUtil.isNotEmpty(result.getVinCode())) {
                // 车架号脱敏：保留前4位和后4位，中间用星号代替
                result.setVinCode(SensitiveInfoUtil.formatBetween(result.getVinCode(), 4, 4));
            }

            // 手机号脱敏
            if (Boolean.TRUE.equals(config.getPhoneSwitch()) && StrUtil.isNotEmpty(result.getPhoneNumber())) {
                // 手机号脱敏：前三位，后四位，其他隐藏
                result.setPhoneNumber(SensitiveInfoUtil.mobilePhone(result.getPhoneNumber()));
            }
        }

        return result;
    }
}
