package org.jeecg.modules.info.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.jeecg.modules.corp.dto.LedgerListDto;
import org.jeecg.modules.corp.entity.PdLedger;
import org.jeecg.modules.info.entity.PdChatSourceDet;
import org.jeecg.modules.info.mapper.PdChatSourceDetMapper;
import org.jeecg.modules.info.service.IPdChatSourceDetService;
import org.jeecg.modules.info.entity.PdChatSourceDet;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: 聊天源子表
 * @Author: jeecg-boot
 * @Date:   2025-04-06
 * @Version: V1.0
 */
@Service
public class PdChatSourceDetServiceImpl extends ServiceImpl<PdChatSourceDetMapper, PdChatSourceDet> implements IPdChatSourceDetService {

    @Override
    public IPage<PdChatSourceDet> pageList(Page<PdChatSourceDet> page, LedgerListDto dto) {
        return this.baseMapper.pageList(page,dto);
    }
}
