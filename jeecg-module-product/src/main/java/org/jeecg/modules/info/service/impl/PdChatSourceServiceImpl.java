package org.jeecg.modules.info.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.jeecg.config.ProvinceIpGenerator;
import org.jeecg.modules.corp.dto.LedgerListDto;
import org.jeecg.modules.corp.entity.PdChatUser;
import org.jeecg.modules.corp.service.IPdChatUserService;
import org.jeecg.modules.info.dto.LedgerChatDto;
import org.jeecg.modules.info.dto.PdBatchChatDto;
import org.jeecg.modules.info.entity.PdChatSource;
import org.jeecg.modules.info.entity.PdChatSourceDet;
import org.jeecg.modules.info.entity.PdGuestUsers;
import org.jeecg.modules.info.entity.PdGuestUsersRel;
import org.jeecg.modules.info.mapper.PdChatSourceMapper;
import org.jeecg.modules.info.service.IPdChatSourceDetService;
import org.jeecg.modules.info.service.IPdChatSourceService;
import org.jeecg.modules.info.service.IPdGuestUsersRelService;
import org.jeecg.modules.info.service.IPdGuestUsersService;
import org.jeecg.modules.info.util.chat.ChatTimeGenerator;
import org.jeecg.modules.wechat.entity.PdChat;
import org.jeecg.modules.wechat.service.IPdChatService;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ThreadLocalRandom;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Description: 聊天源
 * @Author: jeecg-boot
 * @Date:   2025-04-06
 * @Version: V1.0
 */
@Service
public class PdChatSourceServiceImpl extends ServiceImpl<PdChatSourceMapper, PdChatSource> implements IPdChatSourceService {
    @Resource
    private IPdGuestUsersService guestUsersService;

    @Resource
    private IPdGuestUsersRelService guestUsersRelService;


    @Resource
    private IPdChatService pdChatService;

    @Resource
    private IPdChatUserService pdChatUserService;

    @Resource
    private IPdChatSourceDetService sourceDetService;




    @Override
    public void createChatUser(PdBatchChatDto dto) {
        if (CollectionUtil.isEmpty(dto.getPidList())) {
            return;
        }
        try {
            //1.批量创建游客
            List<PdGuestUsers> guestList =guestUsersService.createGuestUserByNum(dto);
            if (CollectionUtil.isEmpty(guestList)) {
                return;
            }
            //添加关联关系
            this.saveRel(dto.getPidList(), guestList.stream().map(PdGuestUsers::getId).collect(Collectors.toList()));
            //2.创建聊天用户,新增聊天记录
            this.creatChatUser(guestList, dto);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("创建聊天用户失败");
        }
    }

    @Override
    public String getIpAddressByTenantId(Integer tenantId) {
        return this.baseMapper.getIpAddressByTenantId(tenantId);
    }

    @Override
    public IPage<PdChatSource> pageList(Page<PdChatSource> page, LedgerListDto dto) {
        return this.baseMapper.pageList(page,dto);
    }

    private void saveRel(List<LedgerChatDto> pidList, List<String> guestIdList) {

        // 2. 准备要保存的关联关系列表
        List<PdGuestUsersRel> relationsToSave = new ArrayList<>();

        // 3. 检查已存在的关联关系
//        List<PdGuestUsersRel> existingRelations = guestUsersRelService.lambdaQuery()
//                .in(PdGuestUsersRel::getPid, pidList.stream().map(LedgerChatDto::getId).collect(Collectors.toList()))
//                .list();
//        // 4. 构建pid到已存在关系的映射
//        Map<String, PdGuestUsersRel> pidToExistingRelation = existingRelations.stream()
//                .collect(Collectors.toMap(PdGuestUsersRel::getPid, Function.identity()));

        // 5. 处理每个pid的关联关系
        for (int i = 0; i < pidList.size(); i++) {
            LedgerChatDto chatDto = pidList.get(i);
            String pid  =chatDto.getId();
            String guestId = guestIdList.get(i);

                // 不存在关系，创建新关系
                PdGuestUsersRel newRel = new PdGuestUsersRel()
                        .setPid(pid)
                        .setGuestId(guestId);
                relationsToSave.add(newRel);

        }

        // 6. 批量保存或更新关联关系
        if (!relationsToSave.isEmpty()) {
            guestUsersRelService.saveOrUpdateBatch(relationsToSave);
        }
    }


    public void creatChatUser(List<PdGuestUsers> guestList, PdBatchChatDto dto) {
        if (CollectionUtil.isEmpty(guestList)) {
            return;
        }

        Random random = new Random();
        List<PdChatUser> saveList = new ArrayList<>();

        // 取出预约信息映射（游客ID -> LedgerChatDto）
        Map<String, LedgerChatDto> ledgerChatDtoMap = dto.getLedgerChatDtoMap();

        for (PdGuestUsers guestUser : guestList) {
            PdChatUser pdChatUser = new PdChatUser();
            pdChatUser.setUserId(guestUser.getId())
                    .setIpAddress(ProvinceIpGenerator.getRandomIpForProvinceByRandom())
                    .setSource(random.nextInt(4))
                    .setUserName(guestUser.getName())
                    .setTenantId(dto.getTenantId());

            // 🔁 优先使用 LedgerChatDto 中的聊天起始时间
            if (ledgerChatDtoMap != null && ledgerChatDtoMap.containsKey(guestUser.getId())) {
                LedgerChatDto ledger = ledgerChatDtoMap.get(guestUser.getId());
                pdChatUser.setDiverData(ledger.getCreateTime()); // 假设字段名为 chatStartTime
            } else if (dto.getCreateTime() != null) {
                pdChatUser.setDiverData(dto.getCreateTime());
            } else {
                pdChatUser.setDiverData(new Date());
            }

            // IP 分省设置
            if (CollectionUtil.isNotEmpty(dto.getIpProvinceList())) {
                pdChatUser.setIpAddress(ProvinceIpGenerator.getIpByCode(
                        dto.getIpProvinceList().get(random.nextInt(dto.getIpProvinceList().size()))
                ));
            }

            saveList.add(pdChatUser);
        }

        if (CollectionUtil.isEmpty(saveList)) {
            return;
        }

        boolean success = pdChatUserService.saveBatch(saveList);
        if (success) {
            this.saveChatInfo(dto, saveList);
        }
    }

    /**
     * 批量保存聊天信息（事务处理）
     *             * @param dto 包含租户ID和基准时间
     *      * @param saveList 需要生成聊天记录的用户列表
     */
    @Transactional // 重要：确保整个操作在事务中执行
    public void saveChatInfo(PdBatchChatDto dto, List<PdChatUser> saveList) {
        if (CollectionUtils.isEmpty(saveList)) {
            // 日志：用户列表为空，无需生成聊天记录
            return;
        }

        int requiredSourceCount = saveList.size(); // 需要生成的聊天记录数量
        List<PdChatSource> selectedSources = new ArrayList<>(); // 选中的聊天源
        List<String> sourceIdsToMarkUsed = new ArrayList<>(); // 需要标记为已用的源ID
        boolean usedFallbackSources = false; // 是否使用了备用源

        // 1. 优先获取未使用的随机聊天源
        LambdaQueryWrapper<PdChatSource> unusedQuery = new LambdaQueryWrapper<PdChatSource>()
                .eq(PdChatSource::getIsUse, 0) // 查询未使用的
                .last("ORDER BY RAND() LIMIT " + requiredSourceCount); // 数据库随机排序（根据数据库类型调整）

        selectedSources = this.list(unusedQuery);

        // 2. 如果未使用的源不足，补充获取已使用的随机聊天源
        if (CollectionUtils.isEmpty(selectedSources) || selectedSources.size() < requiredSourceCount) {
            // 日志：未使用的聊天源不足，尝试使用已使用的
            usedFallbackSources = true;
            int neededMore = requiredSourceCount - selectedSources.size();

            LambdaQueryWrapper<PdChatSource> usedQuery = new LambdaQueryWrapper<PdChatSource>()
                    .eq(PdChatSource::getIsUse, 1) // 查询已使用的
                    .last("ORDER BY RAND() LIMIT " + neededMore); // 数据库随机排序

            List<PdChatSource> usedSources = this.list(usedQuery);
            selectedSources.addAll(usedSources);
        }

        // 3. 检查是否找到任何聊天源
        if (CollectionUtils.isEmpty(selectedSources)) {
            throw new RuntimeException("无法找到可用的聊天源来生成聊天记录。");
        }

        // 4. 获取选中聊天源的详细信息
        List<String> selectedSourceIds = selectedSources.stream()
                .map(PdChatSource::getId)
                .distinct() // 确保唯一性
                .collect(Collectors.toList());

        Map<String, List<PdChatSourceDet>> detailsMap = Collections.emptyMap();
        if (!CollectionUtils.isEmpty(selectedSourceIds)) {
            // 查询并按父ID和序号排序
            List<PdChatSourceDet> allDetails = sourceDetService.lambdaQuery()
                    .in(PdChatSourceDet::getPid, selectedSourceIds)
                    .orderByAsc(PdChatSourceDet::getPid, PdChatSourceDet::getNum)
                    .list();

            // 按父ID分组
            detailsMap = allDetails.stream()
                    .collect(Collectors.groupingBy(PdChatSourceDet::getPid));
        }

        // 5. 为每个用户生成聊天记录
        List<PdChat> allChatsToSave = new ArrayList<>(); // 预估初始容量
        for (int i = 0; i < saveList.size(); i++) {
            PdChatUser chatUser = saveList.get(i);

            // 获取当前用户的聊天起始时间
            Date diverData = chatUser.getDiverData();
            if (diverData == null) {
                continue; // 跳过没有起始时间的用户
            }

            // 循环选择聊天模板
            PdChatSource sourceTemplate = selectedSources.get(i % selectedSources.size());
            String sourceId = sourceTemplate.getId();

            // 获取当前源的详细信息
            List<PdChatSourceDet> currentDetails = detailsMap.getOrDefault(sourceId, Collections.emptyList());

            if (CollectionUtils.isEmpty(currentDetails)) {
                continue; // 跳过没有详细信息的模板
            }

            // 初始化当前用户的聊天时间
            long currentUserChatTimeMillis = ChatTimeGenerator.getRandomTimeInterval(diverData).getTime();

            for (PdChatSourceDet det : currentDetails) {
                Date sendTime = new Date(currentUserChatTimeMillis);

                // 使用 ChatTimeGenerator 计算下一条消息的时间
                Date nextTime = ChatTimeGenerator.nextTime(sendTime, det.getSendType());

                // 构建聊天记录对象
                PdChat chatRecord = new PdChat()
                        .setSendType(det.getSendType()) // 0-用户；1-客服
                        .setMessage(det.getMessage())
                        .setUserId(chatUser.getUserId())
                        .setTenantId(dto.getTenantId())
                        .setSendTime(nextTime) // 设置计算后的时间
                        .setCreateTime(nextTime);
                if (det.getSendType() == 1) {
                    chatRecord.setIpAddress(dto.getIpAddress());
                }else {
                    chatRecord.setIpAddress(chatUser.getIpAddress());
                }

                allChatsToSave.add(chatRecord);

                // 更新当前聊天时间戳（将 currentUserChatTimeMillis 更新为下一条消息的时间戳）
                currentUserChatTimeMillis = nextTime.getTime();
            }

            // 标记需要更新的未使用源
            if (!usedFallbackSources && sourceTemplate.getIsUse() == 0) {
                if (!sourceIdsToMarkUsed.contains(sourceId)) {
                    sourceIdsToMarkUsed.add(sourceId);
                }
            }
        }




        // 6. 批量保存聊天记录
        if (!CollectionUtils.isEmpty(allChatsToSave)) {
            boolean saveSuccess = pdChatService.saveBatch(allChatsToSave);
            if (!saveSuccess) {
                throw new RuntimeException("批量保存聊天记录失败。");
            }
        }

        // 7. 批量更新聊天源使用状态
        if (!CollectionUtils.isEmpty(sourceIdsToMarkUsed)) {
            LambdaUpdateWrapper<PdChatSource> updateWrapper = new LambdaUpdateWrapper<PdChatSource>()
                    .set(PdChatSource::getIsUse, 1) // 标记为已用
                    .in(PdChatSource::getId, sourceIdsToMarkUsed);
            boolean updateSuccess = this.update(updateWrapper);
            if (!updateSuccess) {
                // 日志：标记失败（根据业务需求决定是否抛出异常）
            }
        }
    }
}
