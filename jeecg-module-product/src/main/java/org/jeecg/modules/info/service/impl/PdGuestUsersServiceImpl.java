package org.jeecg.modules.info.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.aliyun.teautil.Common;
import org.jeecg.modules.info.dto.LedgerChatDto;
import org.jeecg.modules.info.dto.PdBatchChatDto;
import org.jeecg.modules.info.entity.PdGuestUsers;
import org.jeecg.modules.info.mapper.PdGuestUsersMapper;
import org.jeecg.modules.info.service.IPdGuestUsersRelService;
import org.jeecg.modules.info.service.IPdGuestUsersService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.concurrent.ThreadLocalRandom;
import java.util.stream.Collectors;

/**
 * @Description: 游客表
 * @Author: jeecg-boot
 * @Date:   2024-11-12
 * @Version: V1.0
 */
@Service
public class PdGuestUsersServiceImpl extends ServiceImpl<PdGuestUsersMapper, PdGuestUsers> implements IPdGuestUsersService {

    @Override
    public PdGuestUsers createGuestUser() {
        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        String tenantId = request.getHeader("tenant-id-link");
        //log.info("报价租户,游客id:{},{}",tenantId,guestId);
        if (StrUtil.isEmpty(tenantId)) {
            throw new RuntimeException("未找到租户 id");
        }
        // 生成唯一UUID
        String uuid = UUID.randomUUID().toString();

        Random random = new Random();

        // 随机生成3位数字
        int randomNum = 100 + random.nextInt(900); // 生成 100-999 的随机数
        // 创建游客用户实例并设置UUID和名称
        PdGuestUsers guestUser = new PdGuestUsers();
        guestUser.setName("游客"+randomNum+"_" + uuid.substring(0, 8)).setTenantId(Integer.valueOf(tenantId)); // 生成一个格式统一的名称，比如“游客_xxxxxxxx”

        // 保存游客用户到数据库
        this.save(guestUser);
        return guestUser;
    }

    @Override
    public List<PdGuestUsers> createGuestUserByNum(PdBatchChatDto dto) {
        List<LedgerChatDto> ledgerList = dto.getPidList();
        if (CollectionUtil.isEmpty(ledgerList)) {
            return Collections.emptyList();
        }

        List<PdGuestUsers> saveList = new ArrayList<>();
        Map<String, LedgerChatDto> ledgerChatDtoMap = new HashMap<>();

        ThreadLocalRandom random = ThreadLocalRandom.current();

        // 使用迭代器保持顺序一一对应（LedgerChatDto -> GuestUser）
        for (LedgerChatDto ledger : ledgerList) {
            PdGuestUsers guestUser = new PdGuestUsers();

            // 生成游客名称
            String uuidSuffix = UUID.randomUUID().toString().substring(0, 8);
            int randomNum = random.nextInt(100, 1000);
            guestUser.setName(String.format("游客%d_%s", randomNum, uuidSuffix));

            saveList.add(guestUser);
        }

        boolean success = this.saveBatch(saveList);

        if (success) {
            // 保存成功后，设置游客 ID，并建立与 LedgerChatDto 的关联
            for (int i = 0; i < saveList.size(); i++) {
                PdGuestUsers guestUser = saveList.get(i);
                String guestUserId = guestUser.getId(); // 假设保存后会生成 ID
                LedgerChatDto ledger = ledgerList.get(i);

                // 将游客 ID 和对应的 LedgerChatDto 关联
                ledgerChatDtoMap.put(guestUserId, ledger);
            }

            dto.setLedgerChatDtoMap(ledgerChatDtoMap); // 设置到 dto
            return saveList;
        } else {
            return Collections.emptyList();
        }
    }

}
