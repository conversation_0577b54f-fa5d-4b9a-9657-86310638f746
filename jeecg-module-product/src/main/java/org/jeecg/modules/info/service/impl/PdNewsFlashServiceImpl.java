package org.jeecg.modules.info.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.jeecg.modules.info.entity.PdNewsFlash;
import org.jeecg.modules.info.mapper.PdNewsFlashMapper;
import org.jeecg.modules.info.service.IPdNewsFlashService;
import org.jeecg.modules.wechat.vo.news.AppnewsListVO;
import org.jeecg.modules.wechat.vo.news.AppnewsVO;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * @Description: 新闻快讯
 * @Author: jeecg-boot
 * @Date:   2025-05-05
 * @Version: V1.0
 */
@Service
public class PdNewsFlashServiceImpl extends ServiceImpl<PdNewsFlashMapper, PdNewsFlash> implements IPdNewsFlashService {



    @Override
    public AppnewsListVO getNewsFlashByPage(Integer pageNo, Integer pageSize) {
        // 创建返回对象
        AppnewsListVO resultVO = new AppnewsListVO();

        // 获取当前日期
        Calendar calendar = Calendar.getInstance();
        Date today = calendar.getTime();

        // 创建日期格式化对象
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        SimpleDateFormat monthDayFormat = new SimpleDateFormat("MM/dd");

        // 获取今天的日期字符串
        String todayStr = dateFormat.format(today);

        // 获取昨天的日期
        calendar.add(Calendar.DAY_OF_MONTH, -1);
        Date yesterday = calendar.getTime();
        String yesterdayStr = dateFormat.format(yesterday);

        // 使用MyBatis-Plus的分页功能，不需要手动计算偏移量

        // 查询快讯数据，按照日期降序排序
        List<PdNewsFlash> newsList = baseMapper.selectPage(
            new Page<>(pageNo, pageSize),
            new QueryWrapper<PdNewsFlash>()
                .orderByDesc("order_time")
        ).getRecords();

        // 如果没有数据，返回空结果
        if (newsList == null || newsList.isEmpty()) {
            resultVO.setTopDate("暂无数据");
            resultVO.setCompanyList(new ArrayList<>());
            return resultVO;
        }

        // 获取第一条数据的日期
        Date firstNewsDate = newsList.get(0).getOrderTime();
        String firstNewsDateStr = dateFormat.format(firstNewsDate);

        // 设置顶部日期
        if (firstNewsDateStr.equals(todayStr)) {
            resultVO.setTopDate("今天");
        } else if (firstNewsDateStr.equals(yesterdayStr)) {
            resultVO.setTopDate("昨天");
        } else {
            resultVO.setTopDate(monthDayFormat.format(firstNewsDate));
        }

        // 转换数据
        List<AppnewsVO> companyList = new ArrayList<>();
        for (PdNewsFlash news : newsList) {
            AppnewsVO appnewsVO = new AppnewsVO();
            appnewsVO.setId(news.getId());
            appnewsVO.setName(news.getContent());

            // 格式化时间为 HH:mm
            SimpleDateFormat timeFormat = new SimpleDateFormat("HH:mm");
            appnewsVO.setNewsTime(timeFormat.format(news.getOrderTime()));
            appnewsVO.setNewsId(news.getNewsId());
            appnewsVO.setHoldNum(news.getHoldNum()); // 默认点赞数为0

            companyList.add(appnewsVO);
        }

        resultVO.setCompanyList(companyList);
        return resultVO;
    }
}
