package org.jeecg.modules.info.service.impl;

import cn.hutool.core.util.StrUtil;
import org.jeecg.modules.corp.dto.DailyConfigExcelDTO;
import org.jeecg.modules.info.entity.PdScene;
import org.jeecg.modules.info.mapper.PdSceneMapper;
import org.jeecg.modules.info.service.IPdSceneService;
import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: 场景库
 * @Author: jeecg-boot
 * @Date:   2025-04-22
 * @Version: V1.0
 */
@Service
public class PdSceneServiceImpl extends ServiceImpl<PdSceneMapper, PdScene> implements IPdSceneService {

    @Autowired
    private StringRedisTemplate redisTemplate;

    private static final String NAME_POOL_KEY = "scene:name:pool";
    private static final String USED_NAME_SET_KEY = "scene:name:used";
    private static final int CACHE_SIZE = 500;

    @Override
    public String getRandomName() {
        // 1. 弹出一个姓名
        String name = redisTemplate.opsForList().leftPop(NAME_POOL_KEY);

        // 2. 如果缓存中没了，尝试重新加载
        if (name == null) {
            refillNameCache();
            name = redisTemplate.opsForList().leftPop(NAME_POOL_KEY);
        }

        // 3. 加入已使用 Set
        if (name != null) {
            redisTemplate.opsForSet().add(USED_NAME_SET_KEY, name);
            return name;
        }

        // 4. 如果依然为空，随机生成一个写死的姓名
        List<String> fallbackNames = Arrays.asList(
                "赵子涵", "钱梓萱", "孙宇轩", "李思远", "周子墨",
                "吴语桐", "郑若曦", "王浩然", "冯雨琪", "陈思英"
        );
        // 随机返回一个
        Random random = new Random();
        return fallbackNames.get(random.nextInt(fallbackNames.size()));
    }


    @Override
    public void importExcel(MultipartFile file, String sourceType) throws Exception {
        // 2. 读取 Excel 内容，使用 ExcelImportUtil 进行导入
        InputStream inputStream = file.getInputStream();
        // 设置导入参数：跳过标题行，标题行行数为2行
        ImportParams params = new ImportParams();
        params.setTitleRows(2);  // 跳过前两行（标题行）
        params.setHeadRows(1);   // 第一行作为头行
        params.setNeedSave(true);

        // 读取 Excel 内容为 List<DailyConfigExcelDTO> 类型
        List<PdScene> rowList = ExcelImportUtil.importExcel(inputStream, PdScene.class, params);
        if (rowList.isEmpty()) {
            throw new IOException("导入的 Excel 文件没有数据");
        }
        List<PdScene> saveList = new ArrayList<>();
        for (PdScene row : rowList) {
            if (StrUtil.isNotEmpty(row.getSceneName())) {
                row.setIsUse("0");
                row.setSourceType(Integer.valueOf(sourceType));
                saveList.add(row);
            }
        }
        if (!saveList.isEmpty()) {
            this.saveBatch(saveList);
        }
    }

    @Override
    public PdScene selectOneUnusedSceneWithLock(String serverType) {
        return baseMapper.selectOneUnusedSceneWithLock(serverType);
    }

    /**
     * 补充 Redis 中的姓名池，优先从未使用中过滤
     */
    private void refillNameCache() {
        // 从 Redis 中获取已使用姓名的集合
        Set<String> usedNames = redisTemplate.opsForSet().members(USED_NAME_SET_KEY);

        // 查询姓名库，获取未使用的姓名
        List<PdScene> all = lambdaQuery()
                .eq(PdScene::getSourceType, 2)  // 姓名库
                .eq(PdScene::getIsUse, "0")    // 未使用
                .select(PdScene::getSceneName)
                .list();

        List<String> names = new ArrayList<>();
        // 筛选未使用的姓名，并加入缓存
        for (PdScene scene : all) {
            if (usedNames == null || !usedNames.contains(scene.getSceneName())) {
                names.add(scene.getSceneName());
            }
            if (names.size() >= CACHE_SIZE) {
                break;
            }
        }

        // 如果未使用姓名不足，补充已使用过的姓名
        if (names.size() < CACHE_SIZE) {
            List<String> usedFallback = lambdaQuery()
                    .eq(PdScene::getSourceType, 2)
                    .select(PdScene::getSceneName)
                    .list()
                    .stream()
                    .map(PdScene::getSceneName)
                    .filter(name -> !names.contains(name))
                    .limit(CACHE_SIZE - names.size())
                    .collect(Collectors.toList());

            names.addAll(usedFallback);
        }

        // 将姓名池写入 Redis
        if (!names.isEmpty()) {
            redisTemplate.opsForList().rightPushAll(NAME_POOL_KEY, names);
        }
    }
}
