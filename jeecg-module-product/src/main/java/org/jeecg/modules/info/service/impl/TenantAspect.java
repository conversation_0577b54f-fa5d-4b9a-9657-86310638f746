package org.jeecg.modules.info.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.jeecg.modules.system.entity.SysTenant;
import org.jeecg.modules.system.service.ISysTenantService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.jeecg.common.api.vo.Result;

import java.lang.reflect.Field;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Aspect
@Component
public class TenantAspect {
    @Autowired
    private ISysTenantService sysTenantService;

    @Around("@annotation(org.jeecg.modules.info.service.TenantFilter)")
    public Object fillTenantName(ProceedingJoinPoint joinPoint) throws Throwable {
        // 执行目标方法，获取结果
        Object result = joinPoint.proceed();

        // 检查结果是否为分页对象
        if (result instanceof Result) {
            Result<?> responseResult = (Result<?>) result;
            Object data = responseResult.getResult();

            // 判断返回的数据是否是分页对象 IPage
            if (data instanceof IPage) {
                IPage<?> page = (IPage<?>) data;

                // 获取分页对象中的实体类列表
                List<?> records = page.getRecords();

                // 提取所有的 tenantId 值
                List<String> tenantIds = records.stream()
                        .map(record -> getFieldValue(record, "tenantId"))
                        .filter(Objects::nonNull)
                        .distinct()
                        .map(Object::toString)
                        .collect(Collectors.toList());
                if (CollectionUtil.isEmpty(tenantIds)) {
                    return result;
                }

                // 根据 tenantId 查询租户名称
                QueryWrapper<SysTenant> queryWrapper = new QueryWrapper<>();
                queryWrapper.in("id", tenantIds);

                // 查询符合条件的租户列表
                List<SysTenant> list = sysTenantService.list(queryWrapper);
                if (CollectionUtil.isEmpty(list)) {
                    return result;
                }

                // 将结果转换为 Map<String, String>，tenantId 为键，name 为值
                Map<Integer, String> tenantIdToNameMap = list.stream()
                        .collect(Collectors.toMap(SysTenant::getId, SysTenant::getName));


                // 为每个记录填充 tenantName
                for (Object record : records) {
                    Integer tenantId = (Integer) getFieldValue(record, "tenantId");
                    if (tenantId != null) {
                        String tenantName = tenantIdToNameMap.get(tenantId);
                        setFieldValue(record, "tenantName", tenantName);
                    }
                }
            }
        }

        return result;
    }

    // 获取指定对象的字段值
    private Object getFieldValue(Object object, String fieldName) {
        try {
            Field field = object.getClass().getDeclaredField(fieldName);
            field.setAccessible(true);
            return field.get(object);
        } catch (Exception e) {
            return null;
        }
    }

    // 设置指定对象的字段值
    private void setFieldValue(Object object, String fieldName, Object value) {
        try {
            Field field = object.getClass().getDeclaredField(fieldName);
            field.setAccessible(true);
            field.set(object, value);
        } catch (Exception e) {
            // 异常处理
        }
    }
}