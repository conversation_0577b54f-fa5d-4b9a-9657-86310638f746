package org.jeecg.modules.info.util;


import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.wechat.service.IPdChatService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;

@Component
@Slf4j
public class AiChatHelper {

    @Resource
    private IPdChatService pdChatService;



    /**
     * 获取 AI 聊天返回的 conversation 数组
     * @param message 发送给 AI 的内容
     * @param botId 模型 ID
     * @return JSONArray 格式的 conversation
     */
    public String getAiConversation(String message, String botId) {
        String aiMessage = "";
        int retryCount = 0;
        boolean success = false;

        while (retryCount < 6 && !success) {
            try {
                 aiMessage = pdChatService.buttonRecord(message, botId);
                if (ObjectUtils.isEmpty(aiMessage)) {
                    throw new RuntimeException("AI 返回消息为空");
                }else if (aiMessage.equals("[") || aiMessage.equals("]")){
                    continue;
                }
                success = true;
            } catch (Exception e) {
                retryCount++;
                log.warn("AI 聊天获取失败，重试 {} 次: {}", retryCount, e.getMessage());

                try {
                    Thread.sleep(8000);
                } catch (InterruptedException ex) {
                    Thread.currentThread().interrupt();
                    throw new RuntimeException("线程休眠被中断", ex);
                }

                if (retryCount == 5) {
                    log.error("AI 消息解析连续 3 次失败，可能不是 JSON 格式");
                    throw new RuntimeException("AI 聊天结果解析失败", e);
                }
            }
        }

        if (ObjectUtils.isEmpty(aiMessage)) {
            throw new IllegalStateException("AI 消息中未包含有效聊天内容");
        }

        return aiMessage;
    }
}