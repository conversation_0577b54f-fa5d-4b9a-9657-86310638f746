package org.jeecg.modules.info.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.jeecg.modules.corp.service.ISysCityPlatePrefixService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.io.InputStream;
import java.util.*;

@Component
public class ChinaPlateNumberGenerator {
    private static final Map<String, List<String>> provinceMap = new HashMap<>();
    private static final Map<String, String> cityMap = new HashMap<>();
    private static final Set<String> directCities = new HashSet<>(Arrays.asList("北京市", "天津市", "上海市", "重庆市"));
    private static final Random random = new Random();

    private static ISysCityPlatePrefixService sysCityPlatePrefixService;

    @Autowired
    public void setSysCityPlatePrefixService(ISysCityPlatePrefixService service) {
        ChinaPlateNumberGenerator.sysCityPlatePrefixService = service;
    }

    static {
        try {
            // 从类路径加载JSON文件
            loadPlateData();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    /**
     * 从类路径加载车牌数据
     */
    private static void loadPlateData() throws IOException {
        ClassPathResource resource = new ClassPathResource("json/cn_json.txt");
        try (InputStream inputStream = resource.getInputStream()) {
            ObjectMapper objectMapper = new ObjectMapper();
            JsonNode rootNode = objectMapper.readTree(inputStream);

            for (JsonNode node : rootNode) {
                String code = node.get("code").asText();
                String city = node.get("city").asText();
                String province = node.get("province").asText();

                // 省份映射
                provinceMap.computeIfAbsent(province, k -> new ArrayList<>()).add(code);

                // 城市映射
                cityMap.put(city, code);
            }
        }
    }

    /**
     * 生成车牌号码
     * @param locations 传入的省份或城市名称，多个通过逗号分隔
     * @return 随机车牌号码
     */
    public static String generatePlateNumber(String locations) {
        String prefix = generatePlatePrefix(locations);
        if ("未知车牌前缀".equals(prefix)) {
            return prefix.replaceAll("\\s+", "");
        }

        // 生成完整的 5 位后缀
        String suffix = generatePlateSuffix();
        if (suffix == null || suffix.length() != 5) {
            return "生成车牌后缀失败".replaceAll("\\s+", "");
        }

        // 插入 "**" 替换中间两位（第2、3位）
        String maskedSuffix = ("" + suffix.charAt(0))       // 第1位
                + "**"                                       // 替代中间
                + suffix.substring(3);                       // 第4-5位

        // 拼接结果并去除 "-"
        String plateNumber = (prefix + maskedSuffix).replaceAll("-", "").replaceAll("\\s+", "");

        return plateNumber;
    }

    /**
     * 根据姓名返回性别称谓（张先生 / 李女士）
     */
    public static String getGenderTitle(String name) {
        if (name == null || name.trim().isEmpty()) {
            return "未知";
        }

        // 常见女性名的字（可根据业务补充）
        Set<Character> femaleChars = new HashSet<>(Arrays.asList(
                '芳', '娜', '莉', '敏', '玲', '艳', '霞', '雪', '静', '娟', '梅', '琴', '慧', '文', '玉', '霞', '红', '燕', '莹', '晶', '怡', '梦'
        ));

        // 去除空格并提取第一个字作为姓
        name = name.trim();
        String surname = String.valueOf(name.charAt(0));

        // 获取最后一个字，判断性别（可加逻辑：如果是两个字也判断中间字）
        char lastChar = name.charAt(name.length() - 1);

        boolean isFemale = femaleChars.contains(lastChar);

        return surname + (isFemale ? "女士" : "先生");
    }

    public static String convertLocationsFromJson(String provincesWithLevelJson) {
        if (provincesWithLevelJson == null || provincesWithLevelJson.trim().isEmpty()) {
            return "";
        }

        try {
            JSONArray array = JSON.parseArray(provincesWithLevelJson);
            List<String> cityList = new ArrayList<>();

            for (int i = 0; i < array.size(); i++) {
                JSONObject obj = array.getJSONObject(i);
                String city = obj.getString("city");
                if (city != null && !city.trim().isEmpty()) {
                    cityList.add(city.trim());
                }
            }

            return String.join(",", cityList); // 用英文逗号拼接
        } catch (Exception e) {
            e.printStackTrace();
            return "";
        }
    }

    public static String getProvinceByLocation(String location) {
        // 如果城市名称包含"市"，去掉"市"，但对于省级直辖市不去掉
        if (location.endsWith("市") ) {
            location = location.substring(0, location.length() - 1);
        }
        // 如果是直接的城市，查找其省份
        if (cityMap.containsKey(location)) {
            return cityMap.get(location);
        }

        // 如果是省份，直接返回
        if (provinceMap.containsKey(location)) {
            return location;
        }

        return "未知省份";
    }




    /**
     * 生成车牌前缀
     * @param locations 传入的省份或城市名称，多个通过逗号分隔
     * @return 随机车牌前缀
     */
    private static String generatePlatePrefix(String locations) {
        String[] locationArray = locations.split(",");
        List<String> validCodes = new ArrayList<>();

        for (String location : locationArray) {
            location = location.trim();

            // 如果城市名称包含"市"，去掉"市"，但对于省级直辖市不去掉
            if (location.endsWith("市") || location.endsWith("省")) {
                location = location.substring(0, location.length() - 1);
            }

            // 首先尝试从数据库查询
            if (sysCityPlatePrefixService != null) {
                try {
                    // 直接使用城市名称作为城市编码查询
                    String platePrefix = sysCityPlatePrefixService.getPlatePrefixByCityCode(location);
                    if (platePrefix != null && !platePrefix.isEmpty()) {
                        validCodes.add(platePrefix);
                    }
                } catch (Exception e) {
                    // 如果数据库查询失败，回退到文件查询
                    e.printStackTrace();
                }
            }

            // 如果数据库查询没有结果，回退到文件查询
            if (validCodes.isEmpty()) {
                // 检查是否为城市
                if (cityMap.containsKey(location)) {
                    validCodes.add(cityMap.get(location));
                }
                // 如果是省份，获取该省的所有车牌前缀
                else if (provinceMap.containsKey(location)) {
                    validCodes.addAll(provinceMap.get(location));
                }
            }
        }

        // 如果没有找到对应的车牌前缀
        if (validCodes.isEmpty()) {
            return "未知车牌前缀";
        }

        // 返回随机的车牌前缀
        return validCodes.get(random.nextInt(validCodes.size()));
    }

    /**
     * 生成车牌后缀
     * @return 随机车牌后缀
     */
    public static String generatePlateSuffix() {
        // 定义可用的字母和数字
        String letters = "ABCDEFGHJKLMNPQRSTUVWXYZ"; // 排除 I 和 O
        String digits = "0123456789";

        // 随机选择字母和数字的组合方式
        String suffix = "";
        int letterCount = random.nextInt(2) + 1; // 随机生成字母数量，1 或 2 个字母
        int digitCount = 5 - letterCount; // 剩余为数字

        // 生成字母部分
        for (int i = 0; i < letterCount; i++) {
            suffix += letters.charAt(random.nextInt(letters.length()));
        }

        // 生成数字部分
        for (int i = 0; i < digitCount; i++) {
            suffix += digits.charAt(random.nextInt(digits.length()));
        }

        return suffix;
    }

    public static void main(String[] args) {
        // 示例：传入省份或城市，返回车牌号码
        String result = generatePlateNumber("河北省,石家庄市");
        System.out.println(result);
    }


}

