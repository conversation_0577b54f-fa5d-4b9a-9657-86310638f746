package org.jeecg.modules.info.util;

import java.util.Random;

/**
 * 车牌号生成工具类
 * 根据传入的车牌前缀（如"闽A"）生成符合规范的随机车牌号
 *
 * 车牌规则：
 * 1. 传统车牌：
 *    - 第1位为省份简称（汉字）
 *    - 第2位为发牌机关代号（A-Z的字母）
 *    - 第3到第7位为序号（由字母或数字组成，但不存在字母I和O，防止和数字1、0混淆）
 *
 * 2. 新能源车牌：
 *    - 第1位和第2位与传统车牌一致
 *    - 第3到第8位为序号（比传统车牌多一位）
 *    - 小型车：第3位只能是字母D或F，第4位可以是数字或字母，第5到8位必须是数字
 *    - 大型车：第3到第7位必须是数字，第8位只能是字母D或F
 */
public class LicensePlateGenerator {

    // 车牌可用字符（不包含I和O，防止与数字1、0混淆）
    private static final String PLATE_CHARS = "ABCDEFGHJKLMNPQRSTUVWXYZ123456789";

    // 新能源车牌小型车前缀
    private static final String[] NEW_ENERGY_SMALL_PREFIX = {"D", "F"};

    // 随机数生成器
    private static final Random RANDOM = new Random();

    /**
     * 生成传统车牌号
     *
     * @param prefix 车牌前缀，如"闽A"
     * @return 完整的传统车牌号
     */
    public static String generateTraditionalPlate(String prefix) {
        if (prefix == null || prefix.length() < 2) {
            throw new IllegalArgumentException("车牌前缀格式不正确，应为省份字母");
        }

        StringBuilder sb = new StringBuilder(prefix);

        // 生成5位普通字符（字母和数字组合，不包含I和O）
        for (int i = 0; i < 5; i++) {
            sb.append(PLATE_CHARS.charAt(RANDOM.nextInt(PLATE_CHARS.length())));
        }

        return sb.toString();
    }

    /**
     * 生成新能源小型车车牌号
     *
     * @param prefix 车牌前缀，如"闽A"
     * @return 完整的新能源小型车车牌号
     */
    public static String generateNewEnergySmallPlate(String prefix) {
        if (prefix == null || prefix.length() < 2) {
            throw new IllegalArgumentException("车牌前缀格式不正确，应为省份字母");
        }

        StringBuilder sb = new StringBuilder(prefix);

        // 添加D或F作为第一位
        sb.append(NEW_ENERGY_SMALL_PREFIX[RANDOM.nextInt(NEW_ENERGY_SMALL_PREFIX.length)]);

        // 添加一个字母或数字作为第二位
        sb.append(PLATE_CHARS.charAt(RANDOM.nextInt(PLATE_CHARS.length())));

        // 添加4位数字
        for (int i = 0; i < 4; i++) {
            sb.append(RANDOM.nextInt(10));
        }

        return sb.toString();
    }

    /**
     * 生成新能源大型车车牌号
     *
     * @param prefix 车牌前缀，如"闽A"
     * @return 完整的新能源大型车车牌号
     */
    public static String generateNewEnergyLargePlate(String prefix) {
        if (prefix == null || prefix.length() < 2) {
            throw new IllegalArgumentException("车牌前缀格式不正确，应为省份+字母，如闽A");
        }

        StringBuilder sb = new StringBuilder(prefix);

        // 添加5位数字
        for (int i = 0; i < 5; i++) {
            sb.append(RANDOM.nextInt(10));
        }

        // 添加D或F作为最后一位
        sb.append(NEW_ENERGY_SMALL_PREFIX[RANDOM.nextInt(NEW_ENERGY_SMALL_PREFIX.length)]);

        return sb.toString();
    }

    /**
     * 随机生成一个车牌号（随机选择传统车牌或新能源车牌）
     *
     * @param prefix 车牌前缀，如"闽A"
     * @return 随机生成的车牌号
     */
    public static String generateRandomPlate(String prefix) {
        // 随机决定生成哪种类型的车牌
        // 传统车牌占比80%，新能源小型车占比15%，新能源大型车占比5%
        int plateType = RANDOM.nextInt(100);

        if (plateType < 80) {
            return generateTraditionalPlate(prefix);
        } else if (plateType < 95) {
            return generateNewEnergySmallPlate(prefix);
        } else {
            return generateNewEnergyLargePlate(prefix);
        }
    }

    /**
     * 验证车牌号是否符合规范
     *
     * @param plateNumber 车牌号
     * @return 是否符合规范
     */
    public static boolean validatePlateNumber(String plateNumber) {
        if (plateNumber == null || plateNumber.isEmpty()) {
            return false;
        }

        // 车牌验证正则表达式（不包含汉字后缀）
        // 传统车牌：省份简称 + 字母 + 5位字母或数字
        // 新能源小型车：省份简称 + 字母 + D/F + 字母或数字 + 4位数字
        // 新能源大型车：省份简称 + 字母 + 5位数字 + D/F
        String plateNumMatch = "^(([京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领][A-Z](([0-9]{5}[DF])|([DF]([A-HJ-NP-Z0-9])[0-9]{4})))|([京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领][A-Z][A-HJ-NP-Z0-9]{5}))$";

        return plateNumber.matches(plateNumMatch);
    }

    /**
     * 测试方法
     */
    public static void main(String[] args) {
        // 测试传统车牌生成
        System.out.println("传统车牌示例：");
        for (int i = 0; i < 5; i++) {
            String plate = generateTraditionalPlate("闽A");
            System.out.println(plate + " - 验证结果: " + validatePlateNumber(plate));
        }

        // 测试新能源小型车车牌生成
        System.out.println("\n新能源小型车车牌示例：");
        for (int i = 0; i < 5; i++) {
            String plate = generateNewEnergySmallPlate("闽A");
            System.out.println(plate + " - 验证结果: " + validatePlateNumber(plate));
        }

        // 测试新能源大型车车牌生成
        System.out.println("\n新能源大型车车牌示例：");
        for (int i = 0; i < 5; i++) {
            String plate = generateNewEnergyLargePlate("闽A");
            System.out.println(plate + " - 验证结果: " + validatePlateNumber(plate));
        }

        // 测试随机车牌生成
        System.out.println("\n随机车牌示例：");
        for (int i = 0; i < 10; i++) {
            String plate = generateRandomPlate("闽A");
            System.out.println(plate + " - 验证结果: " + validatePlateNumber(plate));
        }
    }
}
