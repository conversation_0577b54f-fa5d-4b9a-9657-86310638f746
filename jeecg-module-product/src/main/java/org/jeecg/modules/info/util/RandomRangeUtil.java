package org.jeecg.modules.info.util;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Random;

/**
 * 随机区间工具类
 * 用于在指定区间内生成随机值，支持小幅度浮动
 */
public class RandomRangeUtil {

    private static final Random random = new Random();

    /**
     * 在指定范围内生成随机BigDecimal值
     *
     * @param min 最小值
     * @param max 最大值
     * @param scale 小数位数
     * @return 随机值
     */
    public static BigDecimal randomInRange(BigDecimal min, BigDecimal max, int scale) {
        if (min.compareTo(max) > 0) {
            BigDecimal temp = min;
            min = max;
            max = temp;
        }

        BigDecimal range = max.subtract(min);
        BigDecimal randomValue = min.add(range.multiply(BigDecimal.valueOf(random.nextDouble())));
        return randomValue.setScale(scale, RoundingMode.HALF_UP);
    }

    /**
     * 在指定范围内生成随机Integer值
     *
     * @param min 最小值
     * @param max 最大值
     * @return 随机值
     */
    public static Integer randomInRange(Integer min, Integer max) {
        if (min > max) {
            int temp = min;
            min = max;
            max = temp;
        }
        return min + random.nextInt(max - min + 1);
    }

    /**
     * 基于原值进行小幅度浮动，确保在最小最大值范围内
     *
     * @param originalValue 原始值
     * @param min 最小值
     * @param max 最大值
     * @param fluctuationPercent 浮动百分比(0-1之间)
     * @param scale 小数位数
     * @return 浮动后的值
     */
    public static BigDecimal fluctuateInRange(BigDecimal originalValue, BigDecimal min, BigDecimal max,
                                              double fluctuationPercent, int scale) {
        if (originalValue == null) {
            return randomInRange(min, max, scale);
        }

        // 计算浮动范围
        BigDecimal range = max.subtract(min);
        BigDecimal fluctuation = range.multiply(BigDecimal.valueOf(fluctuationPercent));

        // 计算浮动区间
        BigDecimal lowerBound = originalValue.subtract(fluctuation);
        BigDecimal upperBound = originalValue.add(fluctuation);

        // 确保浮动区间在min和max范围内
        lowerBound = lowerBound.compareTo(min) < 0 ? min : lowerBound;
        upperBound = upperBound.compareTo(max) > 0 ? max : upperBound;

        // 在浮动区间内生成随机值
        return randomInRange(lowerBound, upperBound, scale);
    }

    /**
     * 基于原值进行小幅度浮动，确保在最小最大值范围内
     *
     * @param originalValue 原始值
     * @param min 最小值
     * @param max 最大值
     * @param fluctuationPercent 浮动百分比(0-1之间)
     * @return 浮动后的值
     */
    public static Integer fluctuateInRange(Integer originalValue, Integer min, Integer max, double fluctuationPercent) {
        if (originalValue == null) {
            return randomInRange(min, max);
        }

        // 计算浮动范围
        int range = max - min;
        int fluctuation = (int) Math.ceil(range * fluctuationPercent);

        // 计算浮动区间
        int lowerBound = originalValue - fluctuation;
        int upperBound = originalValue + fluctuation;

        // 确保浮动区间在min和max范围内
        lowerBound = Math.max(lowerBound, min);
        upperBound = Math.min(upperBound, max);

        // 在浮动区间内生成随机值
        return randomInRange(lowerBound, upperBound);
    }

    /**
     * 生成指定范围内的百分比值(如0-100%)
     *
     * @param minPercent 最小百分比值
     * @param maxPercent 最大百分比值
     * @param scale 小数位数
     * @return 随机百分比值
     */
    public static BigDecimal randomPercent(BigDecimal minPercent, BigDecimal maxPercent, int scale) {
        return randomInRange(minPercent, maxPercent, scale);
    }

    /**
     * 基于原始百分比值进行小幅度浮动
     *
     * @param originalPercent 原始百分比值
     * @param minPercent 最小百分比值
     * @param maxPercent 最大百分比值
     * @param fluctuationPercent 浮动百分比(0-1之间)
     * @param scale 小数位数
     * @return 浮动后的百分比值
     */
    public static BigDecimal fluctuatePercent(BigDecimal originalPercent, BigDecimal minPercent,
                                              BigDecimal maxPercent, double fluctuationPercent, int scale) {
        return fluctuateInRange(originalPercent, minPercent, maxPercent, fluctuationPercent, scale);
    }
}