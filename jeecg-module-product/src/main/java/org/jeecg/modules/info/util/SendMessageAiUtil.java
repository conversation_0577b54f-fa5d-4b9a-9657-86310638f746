package org.jeecg.modules.info.util;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.info.dto.CozeConfigDto;
import org.jeecg.modules.info.entity.PdChatSource;
import org.jeecg.modules.info.entity.PdChatSourceDet;
import org.jeecg.modules.info.service.IPdChatSourceDetService;
import org.jeecg.modules.info.service.IPdChatSourceService;
import org.jeecg.modules.info.vo.ChatEnum;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;

@Slf4j
@Component
public class SendMessageAiUtil {

    @Resource
    private AiChatHelper chatHelper;

    @Resource
    private ServiceSceneUtil serviceSceneUtil;
    @Resource
    private IPdChatSourceDetService pdChatSourceDetService;
    @Resource
    private IPdChatSourceService pdChatSourceService;

    @Value("${coze.chatBotIdAdd}")
    private String chatBotIdAdd;

    @Value("${coze.singleBotId}")
    private String singleBotId;


    @Async
    public void sendMessageSaveChat(CozeConfigDto dto) {
        sendMessageSaveChatInternal(dto);
    }

    /**
     * 同步版本的聊天源生成方法，返回是否成功生成
     * @param dto 配置参数
     * @return 是否成功生成聊天源
     */
    public boolean sendMessageSaveChatSync(CozeConfigDto dto) {
        try {
            return sendMessageSaveChatInternal(dto);
        } catch (Exception e) {
            log.error("[聊天生成] 生成聊天源失败", e);
            return false;
        }
    }

    /**
     * 内部实现方法，处理聊天源生成逻辑
     * @param dto 配置参数
     * @return 是否成功生成聊天源
     */
    private boolean sendMessageSaveChatInternal(CozeConfigDto dto) {
        // 1. 确定使用的 botId
        String botId = "";
        if (dto.getType() == ChatEnum.COZE_FLOW.getCode()) {
            botId = singleBotId;
        }

        // 2. 转换入参消息
        dtoHandle(dto);

        // 3. 获取 AI 回复
        String response = chatHelper.getAiConversation(dto.getMessageContent(), botId);

        if (StrUtil.isBlank(response)) {
            log.warn("[聊天生成] 响应为空，跳过生成聊天源。messageContent={}", dto.getMessageContent());
            return false;
        } else {
            log.info("[聊天生成] 响应成功");
        }

        // 4. 解析回复为 JSON 对话数组
        JSONArray conversation = parseConversationResponse(response);
        if (CollectionUtils.isEmpty(conversation)) {
            log.warn("[聊天生成] 解析响应为空，跳过生成聊天源。messageContent={}", dto.getMessageContent());
            return false;
        }

        // 5. 收集聊天明细数据
        List<PdChatSourceDet> detailList = new ArrayList<>();
        for (int i = 0; i < conversation.size(); i++) {
            JSONObject entry = conversation.getJSONObject(i);
            int num = entry.getIntValue("index") + 1;

            // 用户消息 1（主）
            if (entry.containsKey("userMessage") && StrUtil.isNotBlank(entry.getString("userMessage"))) {
                detailList.add(new PdChatSourceDet()
                        .setSendType(0)
                        .setMessage(entry.getString("userMessage"))
                        .setNum(num));
            }

            // 用户消息 2（补充）
            if (entry.containsKey("userMessagetow") && StrUtil.isNotBlank(entry.getString("userMessagetow"))) {
                detailList.add(new PdChatSourceDet()
                        .setSendType(0)
                        .setMessage(entry.getString("userMessagetow"))
                        .setNum(num));
            }

            // 客服消息
            if (entry.containsKey("customerServiceReply") && StrUtil.isNotBlank(entry.getString("customerServiceReply"))) {
                detailList.add(new PdChatSourceDet()
                        .setSendType(1)
                        .setMessage(entry.getString("customerServiceReply"))
                        .setNum(num));
            }
        }

        // 6. 没有有效内容就不创建主表
        if (CollectionUtils.isEmpty(detailList)) {
            log.warn("[聊天生成] 解析后没有有效内容，跳过生成聊天源");
            return false;
        }

        // 7. 创建聊天源主表
        PdChatSource pdChatSource = new PdChatSource();
        pdChatSource.setChatType(dto.getLinkType()).setIsUse(0);
        pdChatSourceService.save(pdChatSource);

        // 8. 回填主表 ID 并批量插入明细
        for (PdChatSourceDet det : detailList) {
            det.setPid(pdChatSource.getId());
        }
        pdChatSourceDetService.saveBatch(detailList);

        log.info("[聊天生成] 成功生成聊天源，ID={}, 类型={}", pdChatSource.getId(), dto.getLinkType());
        return true;
    }

    private void dtoHandle(CozeConfigDto dto) {
        log.info("dtoHandle");
        if (dto.getLinkChengType() != 1) {
            return;
        }
        if (dto.getLinkType() == 0) {
            // 根据链接替换 message
            dto.setMessageContent(serviceSceneUtil.getRandomScene("0"));
        } else if (dto.getLinkType() == 1) {
            // 根据链接替换 message
            dto.setMessageContent(serviceSceneUtil.getRandomScene("1"));
        } else if (dto.getLinkType() == 2) {
            // 根据链接替换 message
            dto.setMessageContent(serviceSceneUtil.getRandomScene("2"));
        }
    }


    // 解析扣子返回的 JSON 格式的对话记录
    private JSONArray parseConversationResponse(String response) {
        try {
            if (StrUtil.isBlank(response)) {
                return new JSONArray();
            }

            // 如果是数组开头，说明返回的是标准 JSON 数组
            response = response.trim();
            if (response.startsWith("[")) {
                return JSON.parseArray(response);
            }

            // 否则尝试作为对象解析
            JSONObject rootNode = JSON.parseObject(response);

            if (rootNode.containsKey("output")) {
                // 如果包含 output 字段，尝试解析为嵌套 JSON 字符串
                String output = rootNode.getString("output");
                if (StrUtil.isNotBlank(output)) {
                    JSONObject outputObj = JSON.parseObject(output);
                    return outputObj.getJSONArray("conversation");
                }
            } else if (rootNode.containsKey("conversation")) {
                return rootNode.getJSONArray("conversation");
            }

        } catch (Exception e) {
            log.error("❌ 解析 conversation JSON 失败，原始内容: {}\n异常信息: ", response, e);
        }
        return new JSONArray(); // 返回空数组，避免 null
    }
}
