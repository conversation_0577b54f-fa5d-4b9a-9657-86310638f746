package org.jeecg.modules.info.util;

import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.info.entity.PdScene;
import org.jeecg.modules.info.service.IPdSceneService;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

@Slf4j
@Component
public class ServiceSceneUtil {

    @Resource
    private IPdSceneService pdSceneService;

    /**
     * 获取未使用场景（一次查一条，带锁防止并发）
     * @param serverType 业务类型
     * @return 场景内容
     */
    @Transactional(rollbackFor = Exception.class)
    public String getRandomScene(String serverType) {
        PdScene scene = pdSceneService.selectOneUnusedSceneWithLock(serverType);

        if (scene == null) {
           throw new RuntimeException("没有可用的场景");
        }

        // 查到后立刻标记为已使用
        PdScene updateScene = new PdScene();
        updateScene.setId(scene.getId());
        updateScene.setIsUse("1");
        pdSceneService.updateById(updateScene);

        log.info("获取场景：{}", scene.getSceneName());

        return scene.getSceneName();
    }
}