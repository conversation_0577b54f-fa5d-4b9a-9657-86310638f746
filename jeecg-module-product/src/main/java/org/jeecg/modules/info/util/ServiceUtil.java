package org.jeecg.modules.info.util;
import java.util.Arrays;
import java.util.List;
import java.util.Random;

public class ServiceUtil {

    /**
     * 车险相关枚举主题
     */
    public enum CarInsuranceTopic {
        GLASS_INSURANCE("玻璃险忘买了怎么办"),
        FAMILY_DRIVING_ACCIDENT("家属开车出事能赔吗"),
        REPAIR_PROCESS("修车流程复杂吗"),
        CLAIM_DELAY("理赔太慢了咋办"),
        PARKING_SCRATCH("停车刮蹭赔不赔"),
        ACCIDENT_PHOTO_LOST("没拍照还能赔吗"),
        DESIGNATED_REPAIR_SHOP("我能选修理厂吗"),
        DRIVING_LICENSE_EXPIRED("驾照快过期了出险怎么办"),
        CAR_LOAN_INSURANCE("贷款车理赔流程一样吗"),
        ADD_EXTRA_COVERAGE("中途能加保险项目吗");

        private final String topic;

        CarInsuranceTopic(String topic) {
            this.topic = topic;
        }

        public String getTopic() {
            return topic;
        }
    }

    /**
     * 根据传入关键词返回一个随机车险相关主题
     *
     * @param keyword 输入的关键词，例如 "车险"
     * @return 一个随机简洁主题，如果关键词不是车险，则返回 null
     */
    public static String getRandomTopic(String keyword) {
        if (keyword == null || !keyword.contains("车险")) {
            return null;
        }
        List<CarInsuranceTopic> topics = Arrays.asList(CarInsuranceTopic.values());
        Random random = new Random();
        CarInsuranceTopic selected = topics.get(random.nextInt(topics.size()));
        return selected.getTopic();
    }
}
