package org.jeecg.modules.info.util.chat;
import java.util.Calendar;
import java.util.Date;
import java.util.Random;

public class ChatTimeGenerator {

    private static final Random random = new Random();

    /**
     * 获取随机时间间隔，1-3天或4-9天，80%几率是1-3天，20%几率是4-9天
     * 确保生成的时间不会超过当前时间
     * @param startDate 起始日期
     * @return 随机加上的时间
     */
    public static Date getRandomTimeInterval(Date startDate) {
        // 获取当前时间
        Date currentTime = new Date();

        // 获取起始时间的时间戳
        long startTimeMillis = startDate.getTime();

        // 计算从起始时间到当前时间的最大可用时间间隔（毫秒）
        long maxAvailableMillis = currentTime.getTime() - startTimeMillis;

        // 如果起始时间已经超过或等于当前时间，则直接返回起始时间
        if (maxAvailableMillis <= 0) {
            return startDate;
        }

        // 获取 0-1 之间的随机数
        double p = random.nextDouble();

        long randomMillis;

        // 80% 机会，1-3 天
        if (p < 0.8) {
            int days = 1 + random.nextInt(3);  // 随机 1-3 天
            randomMillis = days * 24 * 60 * 60 * 1000L;
        } else {
            // 20% 机会，4-9 天
            int days = 4 + random.nextInt(6);  // 随机 4-9 天
            randomMillis = days * 24 * 60 * 60 * 1000L;
        }

        // 确保随机时间间隔不超过最大可用时间间隔
        randomMillis = Math.min(randomMillis, maxAvailableMillis);

        // 获取新的时间，并返回加上随机时间间隔后的时间
        Date randomDate = new Date(startTimeMillis + randomMillis);

        // 在新的时间基础上，随机生成时分秒
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(randomDate);

        // 根据概率生成时、分、秒
        int hour = generateRandomHour();
        int minute = random.nextInt(60); // 随机分钟
        int second = random.nextInt(60); // 随机秒数

        calendar.set(Calendar.HOUR_OF_DAY, hour);
        calendar.set(Calendar.MINUTE, minute);
        calendar.set(Calendar.SECOND, second);

        // 再次检查生成的时间是否超过当前时间
        Date result = calendar.getTime();
        if (result.after(currentTime)) {
            return currentTime;
        }

        return result;
    }

    /**
     * 根据概率生成小时
     * @return 随机生成的小时
     */
    private static int generateRandomHour() {
        double p = random.nextDouble();

        int hour;

        // 94.5% 的时间在 7:00 到 24:00
        if (p < 0.945) {
            hour = 7 + random.nextInt(18);  // 随机 7:00 到 24:00
        } else if (p < 0.995) {
            // 5% 的时间在 0:00 到 2:00
            hour = random.nextInt(3);  // 随机 0:00 到 2:00
        } else {
            // 0.5% 的时间在 2:00 到 7:00
            hour = 2 + random.nextInt(5);  // 随机 2:00 到 7:00
        }

        return hour;
    }

    /**
     * 获取下一条消息的发送时间
     * 确保生成的时间不会超过当前时间
     * @param lastSendTime 上一条发送时间
     * @param sendType 0 = 用户，1 = 客服
     * @return 下一条消息的时间
     */
    public static Date nextTime(Date lastSendTime, int sendType) {
        // 获取当前时间
        Date currentTime = new Date();

        // 如果上一条发送时间已经超过或等于当前时间，则直接返回上一条发送时间
        if (lastSendTime.compareTo(currentTime) >= 0) {
            return lastSendTime;
        }

        long baseTime = lastSendTime.getTime();
        long currentTimeMillis = currentTime.getTime();

        // 计算从上一条发送时间到当前时间的最大可用时间间隔（毫秒）
        long maxAvailableMillis = currentTimeMillis - baseTime;

        // 如果没有可用时间间隔，则直接返回上一条发送时间
        if (maxAvailableMillis <= 0) {
            return lastSendTime;
        }

        int gapSeconds;
        long gapMillis;

        if (sendType == 0) {
            // 用户发言：根据分布概率生成间隔
            double p = random.nextDouble();

            if (p < 0.3) {
                gapSeconds = 10 + random.nextInt(21); // 10-30 秒
            } else if (p < 0.7) {
                gapSeconds = 31 + random.nextInt(30); // 31-60 秒
            } else if (p < 0.95) {
                gapSeconds = 61 + random.nextInt(60); // 61-120 秒
            } else {
                // 5% 概率：沉默段（5~30 分钟）
                int silenceMinutes = 5 + random.nextInt(26); // 5-30 分钟
                gapMillis = silenceMinutes * 60_000L;

                // 确保不超过最大可用时间间隔
                gapMillis = Math.min(gapMillis, maxAvailableMillis);

                Date result = new Date(baseTime + gapMillis);
                return result.after(currentTime) ? currentTime : result;
            }

        } else {
            // 客服发言：仅允许 10-30 秒
            gapSeconds = 10 + random.nextInt(21); // 10-30 秒
        }

        gapMillis = gapSeconds * 1000L;

        // 确保不超过最大可用时间间隔
        gapMillis = Math.min(gapMillis, maxAvailableMillis);

        Date result = new Date(baseTime + gapMillis);

        // 再次检查生成的时间是否超过当前时间
        return result.after(currentTime) ? currentTime : result;
    }
}
