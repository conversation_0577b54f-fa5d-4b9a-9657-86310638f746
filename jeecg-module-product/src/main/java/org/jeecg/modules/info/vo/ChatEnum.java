package org.jeecg.modules.info.vo;

import lombok.Getter;

@Getter
public enum ChatEnum {
    /**
     * 1 - 去系统源聊天数据
     */
    SYSTEM_CHAT(1, "去系统源聊天数据"),

    /**
     * 2 - Coze 对话流（无上下文）
     */
    COZE_FLOW(2, "Coze 对话流"),

    /**
     * 3 - Coze 单会话对话流（有上下文）
     */
    COZE_SINGLE_SESSION(3, "Coze 对话流 - 单会话有上下文"),

    /**
     * 4 - Coze 工作流
     */
    COZE_WORKFLOW(4, "Coze 工作流");

    private final int code;
    private final String description;

    ChatEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }


}