package org.jeecg.modules.info.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 聊天源队列统计信息列表
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "ChatSourceQueueStatsListVO", description = "聊天源队列统计信息列表")
public class ChatSourceQueueStatsListVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 各类型队列统计信息
     */
    @ApiModelProperty(value = "各类型队列统计信息")
    private List<ChatSourceQueueStatsVO> typeStats = new ArrayList<>();

    /**
     * 总统计信息
     */
    @ApiModelProperty(value = "总统计信息")
    private ChatSourceQueueStatsVO totalStats = new ChatSourceQueueStatsVO();

    /**
     * 当前可用并发数
     */
    @ApiModelProperty(value = "当前可用并发数")
    private Integer availableConcurrency = 0;

    /**
     * 已使用并发数
     */
    @ApiModelProperty(value = "已使用并发数")
    private Integer usedConcurrency = 0;

    /**
     * 最大并发数
     */
    @ApiModelProperty(value = "最大并发数")
    private Integer maxConcurrency = 200;
}
