package org.jeecg.modules.info.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

/**
 * 聊天源队列统计信息
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "ChatSourceQueueStatsVO", description = "聊天源队列统计信息")
public class ChatSourceQueueStatsVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 链接类型
     * 0-车险; 1-财险; 2-增值服务
     */
    @ApiModelProperty(value = "链接类型：0-车险; 1-财险; 2-增值服务")
    private Integer linkType;

    /**
     * 待处理任务数量
     */
    @ApiModelProperty(value = "待处理任务数量")
    private Long pendingCount = 0L;

    /**
     * 处理中任务数量
     */
    @ApiModelProperty(value = "处理中任务数量")
    private Long processingCount = 0L;

    /**
     * 成功任务数量
     */
    @ApiModelProperty(value = "成功任务数量")
    private Long successCount = 0L;

    /**
     * 失败任务数量
     */
    @ApiModelProperty(value = "失败任务数量")
    private Long failedCount = 0L;

    /**
     * 总任务数量
     */
    @ApiModelProperty(value = "总任务数量")
    private Long totalCount = 0L;

    /**
     * 成功率
     */
    @ApiModelProperty(value = "成功率")
    private Double successRate = 0.0;

    /**
     * 当前可用并发数
     */
    @ApiModelProperty(value = "当前可用并发数")
    private Integer availableConcurrency = 0;

    /**
     * 已使用并发数
     */
    @ApiModelProperty(value = "已使用并发数")
    private Integer usedConcurrency = 0;

    /**
     * 最大并发数
     */
    @ApiModelProperty(value = "最大并发数")
    private Integer maxConcurrency = 200;

    /**
     * 其他统计信息
     */
    @ApiModelProperty(value = "其他统计信息")
    private Map<String, Object> extraStats = new HashMap<>();
}
