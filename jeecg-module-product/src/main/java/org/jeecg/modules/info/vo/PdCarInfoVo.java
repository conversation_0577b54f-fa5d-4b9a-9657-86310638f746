package org.jeecg.modules.info.vo;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import java.io.Serializable;

@Data
@Accessors(chain = true)
public class PdCarInfoVo implements Serializable {
    /**主键*/
    @ApiModelProperty(value = "主键")
    private String id;
    /**信息来源*/
    @ApiModelProperty(value = "信息来源")
    private String source;

    @ApiModelProperty(value = "商业险多选，1-交强险; 2-第三者责任险; 3-车损险; 4-车上人员责任险; 5-盗抢险; 6-玻璃单独破碎险; 7-自燃险; 8-不计免赔险; 9-发动机涉水险")
    private String commerceInsuranceOptions;

    @ApiModelProperty(value = "车牌号")
    private String licensePlateNumber;

    @ApiModelProperty(value = "所有人")
    private String owner;

    @ApiModelProperty(value = "发动机号码")
    private String engineNumber;

    @ApiModelProperty(value = "品牌型号")
    private String model;

    @ApiModelProperty(value = "使用性质")
    private String useNature;

    @ApiModelProperty(value = "车辆类型")
    private String vehicleType;

    @ApiModelProperty(value = "身份证号码")
    private String idNumber;

    @ApiModelProperty(value = "出生日期")
    private String birthDate;

    @ApiModelProperty(value = "车辆识别代码")
    private String vinCode;

    @ApiModelProperty(value = "性别")
    private String sex;

    @ApiModelProperty(value = "民族")
    private String ethnicity;

    @ApiModelProperty(value = "多租户名称")
    private String tenantName;
}
