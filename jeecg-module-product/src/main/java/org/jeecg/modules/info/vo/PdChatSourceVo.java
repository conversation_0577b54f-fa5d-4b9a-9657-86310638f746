package org.jeecg.modules.info.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.jeecg.modules.info.entity.PdChatSourceDet;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class PdChatSourceVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**主键*/
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private String id;
    /**创建日期*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private Date createTime;
    /**类型*/
    @Excel(name = "类型", width = 15)
    @ApiModelProperty(value = "类型")
    private Integer chatType;
    /**使用状态*/
    @Excel(name = "使用状态", width = 15)
    @ApiModelProperty(value = "使用状态")
    private Integer isUse;
    /**生成下标*/
    @Excel(name = "生成下标", width = 15)
    @ApiModelProperty(value = "生成下标")
    private Integer num;

    private List<PdChatSourceDet> chatSourceDetList;
}
