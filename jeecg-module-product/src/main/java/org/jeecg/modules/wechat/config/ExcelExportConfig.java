package org.jeecg.modules.wechat.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * Excel导出配置类
 * 用于配置百万级数据导出的相关参数
 * 
 * <AUTHOR>
 * @date 2024-11-01
 */
@Component
@ConfigurationProperties(prefix = "excel.export")
public class ExcelExportConfig {
    
    /**
     * 单sheet模式的数据量阈值（默认10万）
     * 超过此阈值将使用多sheet模式
     */
    private long singleSheetThreshold = 100000L;
    
    /**
     * 每个sheet的最大行数（默认50万）
     * Excel理论最大值为1048576行，设置为50万留有余量
     */
    private int maxRowsPerSheet = 500000;
    
    /**
     * 分页查询的页大小（默认1万）
     * 用于控制每次从数据库查询的数据量
     */
    private int pageSize = 10000;
    
    /**
     * 批量查询关联数据的批次大小（默认1000）
     * 用于批量查询租户、用户等关联信息
     */
    private int batchQuerySize = 1000;
    
    /**
     * 导出超时时间（秒，默认30分钟）
     */
    private int exportTimeoutSeconds = 1800;
    
    /**
     * 是否启用导出进度监控
     */
    private boolean enableProgressMonitor = true;
    
    /**
     * 内存使用监控阈值（MB，默认500MB）
     * 当内存使用超过此阈值时会记录警告日志
     */
    private long memoryWarningThreshold = 500L;

    // Getter and Setter methods
    
    public long getSingleSheetThreshold() {
        return singleSheetThreshold;
    }

    public void setSingleSheetThreshold(long singleSheetThreshold) {
        this.singleSheetThreshold = singleSheetThreshold;
    }

    public int getMaxRowsPerSheet() {
        return maxRowsPerSheet;
    }

    public void setMaxRowsPerSheet(int maxRowsPerSheet) {
        this.maxRowsPerSheet = maxRowsPerSheet;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public int getBatchQuerySize() {
        return batchQuerySize;
    }

    public void setBatchQuerySize(int batchQuerySize) {
        this.batchQuerySize = batchQuerySize;
    }

    public int getExportTimeoutSeconds() {
        return exportTimeoutSeconds;
    }

    public void setExportTimeoutSeconds(int exportTimeoutSeconds) {
        this.exportTimeoutSeconds = exportTimeoutSeconds;
    }

    public boolean isEnableProgressMonitor() {
        return enableProgressMonitor;
    }

    public void setEnableProgressMonitor(boolean enableProgressMonitor) {
        this.enableProgressMonitor = enableProgressMonitor;
    }

    public long getMemoryWarningThreshold() {
        return memoryWarningThreshold;
    }

    public void setMemoryWarningThreshold(long memoryWarningThreshold) {
        this.memoryWarningThreshold = memoryWarningThreshold;
    }
    
    /**
     * 获取内存使用情况（MB）
     */
    public long getCurrentMemoryUsage() {
        Runtime runtime = Runtime.getRuntime();
        return (runtime.totalMemory() - runtime.freeMemory()) / 1024 / 1024;
    }
    
    /**
     * 检查是否需要内存警告
     */
    public boolean shouldWarnMemoryUsage() {
        return getCurrentMemoryUsage() > memoryWarningThreshold;
    }
}
