package org.jeecg.modules.wechat.config;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Date;

@Data
@TableName("pd_chat_config")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="pd_chat_config对象", description="聊天记录")
public class PdChatConfig implements Serializable {
    private static final long serialVersionUID = 1L;
    /** 主键 */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private String id;
    /** 聊天长度（最大7） */
    @ApiModelProperty(value = "聊天长度，取值范围：1-7")
    private String chatLengthRate;

    /** 聊天标题 */
    @ApiModelProperty(value = "聊天标题，如：交强险、车损险等")
    private String chatTitle;

    /** 聊天情绪（0-客气，1-不耐烦，2-请教，3-懒） */
    @ApiModelProperty(value = "聊天情绪，0-客气，1-不耐烦，2-请教，3-懒")
    private String chatMoodRate;

    /** 回复时长（0-一分钟内，1-3分钟内，2-十几分钟内） */
    @ApiModelProperty(value = "回复时长，0-一分钟内，1-3分钟内，2-十几分钟内")
    private String responseTimeRate;

    @ApiModelProperty(value = "聊天配置")
    private String settingJson;

    @ApiModelProperty(value = "类型:1-聊天配置")
    private String settingType;

    /** 感谢是否结尾（0-否，1-是） */
    @ApiModelProperty(value = "感谢是否结尾，0-否，1-是")
    private BigDecimal thankAtEndRate;

    @ApiModelProperty(value = "生成条数")
    private Integer numberGenerated;
}
