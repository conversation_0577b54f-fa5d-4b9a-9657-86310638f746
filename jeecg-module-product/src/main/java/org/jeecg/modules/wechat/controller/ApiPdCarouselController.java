package org.jeecg.modules.wechat.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.modules.info.service.TenantFilter;
import org.jeecg.modules.wechat.dto.news.PdCarouselDto;
import org.jeecg.modules.wechat.entity.PdCarousel;
import org.jeecg.modules.wechat.service.IPdCarouselService;
import org.jeecg.modules.wechat.service.IWeChatService;
import org.jeecg.modules.wechat.vo.PdCarouselVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;

@Api(tags="一期app轮播图")
@RestController
@RequestMapping("app/wechat/pdCarousel")
@Slf4j
public class ApiPdCarouselController {

    @Autowired
    private IPdCarouselService pdCarouselService;
    @Resource
    private IWeChatService weChatService;

    /**
     * 分页列表查询
     *
     * @return
     */
    //@AutoLog(value = "app轮播图-分页列表查询")
    @ApiOperation(value="app轮播图-分页列表查询", notes="app轮播图-分页列表查询")
    @GetMapping(value = "/list")
    @TenantFilter
    public Result<List<PdCarousel>> queryPageList() {
        String shelfConfig = weChatService.getShelfConfig();
        QueryWrapper<PdCarousel> queryWrapper = new QueryWrapper<>();
        if ("1".equals(shelfConfig)) {
            queryWrapper.eq("type", 1);
        }else if ("2".equals(shelfConfig)) {
            queryWrapper.eq("type", 2);
        }
        List<PdCarousel> list = pdCarouselService.list(queryWrapper);
        return Result.OK(list);
    }

    @ApiOperation(value="app轮播图-分页列表查询", notes="app轮播图-分页列表查询")
    @GetMapping(value = "/queryPageList")
    public Result<IPage<PdCarouselVO>> queryPageList(@RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
                                                     @RequestParam(name="pageSize", defaultValue="99999") Integer pageSize,
                                                     PdCarouselDto dto) {
        String shelfConfig = weChatService.getShelfConfig();
        if ("1".equals(shelfConfig)) {
            dto.setType(1);
        }else  {
            dto.setType(3);
        }
        Page<PdCarouselVO> page = new Page<PdCarouselVO>(pageNo, pageSize);
        IPage<PdCarouselVO> pageList = pdCarouselService.queryPageList(page, dto);
        return Result.OK(pageList);
    }

    @GetMapping(value = "/getById")
    public Result<String> getByTenant() {
        return Result.OK(pdCarouselService.getByTenant());
    }
}
