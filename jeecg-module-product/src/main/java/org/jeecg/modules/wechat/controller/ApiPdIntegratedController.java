package org.jeecg.modules.wechat.controller;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.modules.corp.entity.PdLinkInfo;
import org.jeecg.modules.wechat.entity.PdIntegrated;
import org.jeecg.modules.wechat.service.IPdIntegratedService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

@Api(tags="一期移动端-综合服务")
@RestController
@RequestMapping("api/wechat/pdIntegrated")
@Slf4j
public class ApiPdIntegratedController {

    @Autowired
    private IPdIntegratedService pdIntegratedService;

    /**
     * 分页列表查询
     *
     * @return
     */
    //@AutoLog(value = "综合服务-分页列表查询")
    @ApiOperation(value="综合服务类型-分页列表查询", notes="财险报价中的多选类型")
    @GetMapping(value = "/list")
        @ApiImplicitParams(
            @ApiImplicitParam(value = "链接类型:(0-车险;1-财险;2-增值服务)")
    )
    public Result<List<PdIntegrated>> queryPageList(@RequestParam(name = "linkType") String linkType) {

        LambdaQueryWrapper<PdIntegrated> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PdIntegrated::getType, linkType).orderByAsc(PdIntegrated::getSortNum);
        List<PdIntegrated> list = pdIntegratedService.list(queryWrapper);
        list.forEach(item-> item.setInputType(StrUtil.isNotEmpty(item.getInputType()) && item.getInputType().equals("Y")?"Y":null));
        return Result.OK(list);
    }
}
