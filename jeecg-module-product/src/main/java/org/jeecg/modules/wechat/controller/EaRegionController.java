package org.jeecg.modules.wechat.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.modules.wechat.service.IEaRegionService;
import org.jeecg.modules.wechat.vo.CityTreeVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @Description: 区域信息
 * @Author: jeecg-boot
 * @Date: 2024-11-12
 * @Version: V1.0
 */
@Api(tags = "区域信息")
@RestController
@RequestMapping("/wechat/eaRegion")
@Slf4j
public class EaRegionController {

    @Autowired
    private IEaRegionService eaRegionService;

    /**
     * 获取城市树状结构（一级和二级城市）
     *
     * 返回格式：
     * [
     *   {
     *     "id": 21014,
     *     "code": "110000",
     *     "name": "北京市",
     *     "cityLevel": "1",
     *     "children": [
     *       {
     *         "id": 21015,
     *         "code": "110100",
     *         "name": "北京市",
     *         "cityLevel": "1"
     *       }
     *     ]
     *   },
     *   ...
     * ]
     *
     * @return 城市树状结构
     */
    @ApiOperation(value = "获取城市树状结构", notes = "获取城市树状结构（一级和二级城市）")
    @GetMapping("/getCityTree")
    @Cacheable(cacheNames = "cityTree", key = "#root.methodName")
    public Result<List<CityTreeVO>> getCityTree() {
        List<CityTreeVO> cityTree = eaRegionService.getCityTree();
        return Result.OK(cityTree);
    }
}
