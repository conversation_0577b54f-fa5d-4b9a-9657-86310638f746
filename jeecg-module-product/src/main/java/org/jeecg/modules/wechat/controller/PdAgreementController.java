package org.jeecg.modules.wechat.controller;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.info.service.TenantFilter;
import org.jeecg.modules.wechat.entity.PdAgreement;
import org.jeecg.modules.wechat.service.IPdAgreementService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: 须知协议
 * @Author: jeecg-boot
 * @Date:   2024-11-01
 * @Version: V1.0
 */
@Api(tags="须知协议")
@RestController
@RequestMapping("/wechat/pdAgreement")
@Slf4j
public class PdAgreementController extends JeecgController<PdAgreement, IPdAgreementService> {
	@Autowired
	private IPdAgreementService pdAgreementService;
	
	/**
	 * 分页列表查询
	 *
	 * @param pdAgreement
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "须知协议-分页列表查询")
	@ApiOperation(value="须知协议-分页列表查询", notes="须知协议-分页列表查询")
	@GetMapping(value = "/list")
	@TenantFilter
	public Result<IPage<PdAgreement>> queryPageList(PdAgreement pdAgreement,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<PdAgreement> queryWrapper = QueryGenerator.initQueryWrapper(pdAgreement, req.getParameterMap());
		LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
		queryWrapper.in("tenant_id", Arrays.asList(sysUser.getRelTenantIds().split(",")));
		Page<PdAgreement> page = new Page<PdAgreement>(pageNo, pageSize);
		IPage<PdAgreement> pageList = pdAgreementService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param pdAgreement
	 * @return
	 */
	@AutoLog(value = "须知协议-添加")
	@ApiOperation(value="须知协议-添加", notes="须知协议-添加")
	@RequiresPermissions("wechat:pd_agreement:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody PdAgreement pdAgreement) {
		pdAgreementService.save(pdAgreement);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param pdAgreement
	 * @return
	 */
	@AutoLog(value = "须知协议-编辑")
	@ApiOperation(value="须知协议-编辑", notes="须知协议-编辑")
	@RequiresPermissions("wechat:pd_agreement:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody PdAgreement pdAgreement) {
		pdAgreementService.updateById(pdAgreement);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "须知协议-通过id删除")
	@ApiOperation(value="须知协议-通过id删除", notes="须知协议-通过id删除")
	@RequiresPermissions("wechat:pd_agreement:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		pdAgreementService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "须知协议-批量删除")
	@ApiOperation(value="须知协议-批量删除", notes="须知协议-批量删除")
	@RequiresPermissions("wechat:pd_agreement:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.pdAgreementService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "须知协议-通过id查询")
	@ApiOperation(value="须知协议-通过id查询", notes="须知协议-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<PdAgreement> queryById(@RequestParam(name="id",required=true) String id) {
		PdAgreement pdAgreement = pdAgreementService.getById(id);
		if(pdAgreement==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(pdAgreement);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param pdAgreement
    */
    @RequiresPermissions("wechat:pd_agreement:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, PdAgreement pdAgreement) {
        return super.exportXls(request, pdAgreement, PdAgreement.class, "须知协议");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("wechat:pd_agreement:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, PdAgreement.class);
    }

}
