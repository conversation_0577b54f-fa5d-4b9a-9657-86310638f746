package org.jeecg.modules.wechat.controller;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.info.service.TenantFilter;
import org.jeecg.modules.wechat.dto.news.PdCarouselDto;
import org.jeecg.modules.wechat.entity.PdAgreement;
import org.jeecg.modules.wechat.entity.PdCarousel;
import org.jeecg.modules.wechat.service.IPdCarouselService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.modules.wechat.vo.PdCarouselVO;
import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: app轮播图
 * @Author: jeecg-boot
 * @Date:   2024-11-02
 * @Version: V1.0
 */
@Api(tags="app轮播图")
@RestController
@RequestMapping("/wechat/pdCarousel")
@Slf4j
public class PdCarouselController extends JeecgController<PdCarousel, IPdCarouselService> {
	@Autowired
	private IPdCarouselService pdCarouselService;
	
	/**
	 * 分页列表查询
	 *
	 * @param pageNo
	 * @param pageSize
	 * @return
	 */
	//@AutoLog(value = "app轮播图-分页列表查询")
	@ApiOperation(value="app轮播图-分页列表查询", notes="app轮播图-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<PdCarouselVO>> queryPageList(@RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
													 @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
													 PdCarouselDto dto) {

		Page<PdCarouselVO> page = new Page<PdCarouselVO>(pageNo, pageSize);
		IPage<PdCarouselVO> pageList = pdCarouselService.queryPageList(page, dto);
		return Result.OK(pageList);
	}

	
	/**
	 *   添加
	 *
	 * @param pdCarousel
	 * @return
	 */
	@AutoLog(value = "app轮播图-添加")
	@ApiOperation(value="app轮播图-添加", notes="app轮播图-添加")
	@RequiresPermissions("wechat:pd_carousel:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody PdCarousel pdCarousel) {
		pdCarouselService.save(pdCarousel);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param pdCarousel
	 * @return
	 */
	@AutoLog(value = "app轮播图-编辑")
	@ApiOperation(value="app轮播图-编辑", notes="app轮播图-编辑")
	@RequiresPermissions("wechat:pd_carousel:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody PdCarousel pdCarousel) {
		pdCarouselService.updateById(pdCarousel);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "app轮播图-通过id删除")
	@ApiOperation(value="app轮播图-通过id删除", notes="app轮播图-通过id删除")
	@RequiresPermissions("wechat:pd_carousel:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		pdCarouselService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "app轮播图-批量删除")
	@ApiOperation(value="app轮播图-批量删除", notes="app轮播图-批量删除")
	@RequiresPermissions("wechat:pd_carousel:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.pdCarouselService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "app轮播图-通过id查询")
	@ApiOperation(value="app轮播图-通过id查询", notes="app轮播图-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<PdCarousel> queryById(@RequestParam(name="id",required=true) String id) {
		PdCarousel pdCarousel = pdCarouselService.getById(id);
		if(pdCarousel==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(pdCarousel);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param pdCarousel
    */
    @RequiresPermissions("wechat:pd_carousel:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, PdCarousel pdCarousel) {
        return super.exportXls(request, pdCarousel, PdCarousel.class, "app轮播图");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("wechat:pd_carousel:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, PdCarousel.class);
    }

}
