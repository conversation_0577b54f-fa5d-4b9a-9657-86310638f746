package org.jeecg.modules.wechat.controller;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.wechat.entity.PdImgConfig;
import org.jeecg.modules.wechat.service.IPdImgConfigService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: 图片配置
 * @Author: jeecg-boot
 * @Date:   2024-11-02
 * @Version: V1.0
 */
@Api(tags="图片配置")
@RestController
@RequestMapping("/wechat/pdImgConfig")
@Slf4j
public class PdImgConfigController extends JeecgController<PdImgConfig, IPdImgConfigService> {
	@Autowired
	private IPdImgConfigService pdImgConfigService;
	
	/**
	 * 分页列表查询
	 *
	 * @param pdImgConfig
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "图片配置-分页列表查询")
	@ApiOperation(value="图片配置-分页列表查询", notes="图片配置-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<PdImgConfig>> queryPageList(PdImgConfig pdImgConfig,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<PdImgConfig> queryWrapper = QueryGenerator.initQueryWrapper(pdImgConfig, req.getParameterMap());
		Page<PdImgConfig> page = new Page<PdImgConfig>(pageNo, pageSize);
		IPage<PdImgConfig> pageList = pdImgConfigService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param pdImgConfig
	 * @return
	 */
	@AutoLog(value = "图片配置-添加")
	@ApiOperation(value="图片配置-添加", notes="图片配置-添加")
	@RequiresPermissions("wechat:pd_img_config:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody PdImgConfig pdImgConfig) {
		pdImgConfigService.save(pdImgConfig);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param pdImgConfig
	 * @return
	 */
	@AutoLog(value = "图片配置-编辑")
	@ApiOperation(value="图片配置-编辑", notes="图片配置-编辑")
	@RequiresPermissions("wechat:pd_img_config:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody PdImgConfig pdImgConfig) {
		pdImgConfigService.updateById(pdImgConfig);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "图片配置-通过id删除")
	@ApiOperation(value="图片配置-通过id删除", notes="图片配置-通过id删除")
	@RequiresPermissions("wechat:pd_img_config:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		pdImgConfigService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "图片配置-批量删除")
	@ApiOperation(value="图片配置-批量删除", notes="图片配置-批量删除")
	@RequiresPermissions("wechat:pd_img_config:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.pdImgConfigService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "图片配置-通过id查询")
	@ApiOperation(value="图片配置-通过id查询", notes="图片配置-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<PdImgConfig> queryById(@RequestParam(name="id",required=true) String id) {
		PdImgConfig pdImgConfig = pdImgConfigService.getById(id);
		if(pdImgConfig==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(pdImgConfig);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param pdImgConfig
    */
    @RequiresPermissions("wechat:pd_img_config:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, PdImgConfig pdImgConfig) {
        return super.exportXls(request, pdImgConfig, PdImgConfig.class, "图片配置");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("wechat:pd_img_config:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, PdImgConfig.class);
    }

}
