package org.jeecg.modules.wechat.controller;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.info.service.TenantFilter;
import org.jeecg.modules.wechat.entity.PdIntegrated;
import org.jeecg.modules.wechat.service.IPdIntegratedService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: 综合服务
 * @Author: jeecg-boot
 * @Date:   2024-10-31
 * @Version: V1.0
 */
@Api(tags="综合服务")
@RestController
@RequestMapping("/wechat/pdIntegrated")
@Slf4j
public class PdIntegratedController extends JeecgController<PdIntegrated, IPdIntegratedService> {
	@Autowired
	private IPdIntegratedService pdIntegratedService;
	
	/**
	 * 分页列表查询
	 *
	 * @param pdIntegrated
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "综合服务-分页列表查询")
	@ApiOperation(value="综合服务-分页列表查询", notes="综合服务-分页列表查询")
	@GetMapping(value = "/list")
	@TenantFilter
	public Result<IPage<PdIntegrated>> queryPageList(PdIntegrated pdIntegrated,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<PdIntegrated> queryWrapper = QueryGenerator.initQueryWrapper(pdIntegrated, req.getParameterMap());
		LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
		queryWrapper.in("tenant_id", Arrays.asList(sysUser.getRelTenantIds().split(",")));
		Page<PdIntegrated> page = new Page<PdIntegrated>(pageNo, pageSize);
		IPage<PdIntegrated> pageList = pdIntegratedService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param pdIntegrated
	 * @return
	 */
	@AutoLog(value = "综合服务-添加")
	@ApiOperation(value="综合服务-添加", notes="综合服务-添加")
	@RequiresPermissions("wechat:pd_integrated:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody PdIntegrated pdIntegrated) {
		pdIntegratedService.save(pdIntegrated);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param pdIntegrated
	 * @return
	 */
	@AutoLog(value = "综合服务-编辑")
	@ApiOperation(value="综合服务-编辑", notes="综合服务-编辑")
	@RequiresPermissions("wechat:pd_integrated:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody PdIntegrated pdIntegrated) {
		pdIntegratedService.updateById(pdIntegrated);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "综合服务-通过id删除")
	@ApiOperation(value="综合服务-通过id删除", notes="综合服务-通过id删除")
	@RequiresPermissions("wechat:pd_integrated:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		pdIntegratedService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "综合服务-批量删除")
	@ApiOperation(value="综合服务-批量删除", notes="综合服务-批量删除")
	@RequiresPermissions("wechat:pd_integrated:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.pdIntegratedService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "综合服务-通过id查询")
	@ApiOperation(value="综合服务-通过id查询", notes="综合服务-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<PdIntegrated> queryById(@RequestParam(name="id",required=true) String id) {
		PdIntegrated pdIntegrated = pdIntegratedService.getById(id);
		if(pdIntegrated==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(pdIntegrated);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param pdIntegrated
    */
    @RequiresPermissions("wechat:pd_integrated:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, PdIntegrated pdIntegrated) {
        return super.exportXls(request, pdIntegrated, PdIntegrated.class, "综合服务");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("wechat:pd_integrated:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, PdIntegrated.class);
    }

}
