package org.jeecg.modules.wechat.controller;

import java.util.Arrays;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.modules.wechat.dto.config.DeployConfigDTO;
import org.jeecg.modules.wechat.entity.SysDeployConfig;
import org.jeecg.modules.wechat.service.ISysDeployConfigService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;

/**
 * @Description: 配置类型表
 * @Author: jeecg-boot
 * @Date:   2025-04-22
 * @Version: V1.0
 */
@Api(tags="配置类型表")
@RestController
@RequestMapping("/wechat/sysDeployConfig")
@Slf4j
public class SysDeployConfigController extends JeecgController<SysDeployConfig, ISysDeployConfigService> {
	@Autowired
	private ISysDeployConfigService sysDeployConfigService;

	/**
	 * 分页列表查询
	 *
	 * @param sysDeployConfig
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "配置类型表-分页列表查询")
	@ApiOperation(value="配置类型表-分页列表查询", notes="配置类型表-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<SysDeployConfig>> queryPageList(SysDeployConfig sysDeployConfig,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<SysDeployConfig> queryWrapper = QueryGenerator.initQueryWrapper(sysDeployConfig, req.getParameterMap());
		Page<SysDeployConfig> page = new Page<SysDeployConfig>(pageNo, pageSize);
		IPage<SysDeployConfig> pageList = sysDeployConfigService.page(page, queryWrapper);
		return Result.OK(pageList);
	}

	/**
	 *   添加
	 *
	 * @param sysDeployConfig
	 * @return
	 */
	@AutoLog(value = "配置类型表-添加")
	@ApiOperation(value="配置类型表-添加", notes="配置类型表-添加")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody SysDeployConfig sysDeployConfig) {
		sysDeployConfigService.save(sysDeployConfig);
		// 清除Redis缓存
		sysDeployConfigService.clearConfigCache(sysDeployConfig.getDeployType());
		return Result.OK("添加成功！");
	}

	/**
	 *  编辑
	 *
	 * @param sysDeployConfig
	 * @return
	 */
	@AutoLog(value = "配置类型表-编辑")
	@ApiOperation(value="配置类型表-编辑", notes="配置类型表-编辑")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody SysDeployConfig sysDeployConfig) {
		sysDeployConfigService.updateById(sysDeployConfig);

		// 清除Redis缓存，使用常量中定义的键前缀
		sysDeployConfigService.clearConfigCache(sysDeployConfig.getDeployType());
		return Result.OK("编辑成功!");
	}

	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "配置类型表-通过id删除")
	@ApiOperation(value="配置类型表-通过id删除", notes="配置类型表-通过id删除")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		sysDeployConfigService.removeById(id);
		return Result.OK("删除成功!");
	}

	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "配置类型表-批量删除")
	@ApiOperation(value="配置类型表-批量删除", notes="配置类型表-批量删除")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.sysDeployConfigService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}

	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "配置类型表-通过id查询")
	@ApiOperation(value="配置类型表-通过id查询", notes="配置类型表-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<SysDeployConfig> queryById(@RequestParam(name="id",required=true) String id) {

		SysDeployConfig sysDeployConfig = sysDeployConfigService.lambdaQuery().eq(SysDeployConfig::getDeployType, Integer.valueOf(id)).one();
		if(sysDeployConfig==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(sysDeployConfig);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param sysDeployConfig
    */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, SysDeployConfig sysDeployConfig) {
        return super.exportXls(request, sysDeployConfig, SysDeployConfig.class, "配置类型表");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, SysDeployConfig.class);
    }

    /**
     * 获取系统配置信息
     *
     * @return 配置信息DTO
     */
    @ApiOperation(value="获取系统配置信息", notes="获取系统配置信息")
    @GetMapping(value = "/getConfig")
    public Result<DeployConfigDTO> getConfig() {
        DeployConfigDTO config = sysDeployConfigService.getDeployConfig();
        return Result.OK(config);
    }

    /**
     * 更新系统配置信息
     *
     * @param deployConfig 配置信息DTO
     * @return 操作结果
     */
    @AutoLog(value = "更新系统配置信息")
    @ApiOperation(value="更新系统配置信息", notes="更新系统配置信息")
    @PostMapping(value = "/updateConfig")
    public Result<String> updateConfig(@RequestBody DeployConfigDTO deployConfig) {
        boolean result = sysDeployConfigService.saveOrUpdateDeployConfig(deployConfig);
        if (result) {
            return Result.OK("配置更新成功！");
        } else {
            return Result.error("配置更新失败！");
        }
    }
}
