package org.jeecg.modules.wechat.controller;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.ocr_api20210707.Client;
import com.aliyun.ocr_api20210707.models.*;
import com.aliyun.tea.TeaException;
import com.aliyun.teautil.Common;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.gson.Gson;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.formula.functions.T;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.modules.demo.firmInformation.entity.WhClick;
import org.jeecg.modules.demo.firmInformation.service.IWhClickService;
import org.jeecg.modules.wechat.dto.*;
import org.jeecg.modules.wechat.entity.AppNews;
import org.jeecg.modules.wechat.entity.PdAgreement;
import org.jeecg.modules.wechat.entity.PdChat;
import org.jeecg.modules.wechat.service.IAppNewsService;
import org.jeecg.modules.wechat.service.IPdAgreementService;
import org.jeecg.modules.wechat.service.IPdChatService;
import org.jeecg.modules.wechat.service.IWeChatService;
import org.jeecg.modules.wechat.vo.*;
import org.jeecg.modules.wechat.vo.chat.ChatIdVO;
import org.jeecg.modules.wechat.vo.chat.ChatListVO;
import org.jeecg.modules.wechat.vo.chat.ReplyVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.List;
import java.util.Map;

@Api(tags="一期移动端-广告内容提交")
@RestController
@RequestMapping("api/app/weChat")
@Slf4j
public class WeChatController {

    @Resource
    private IWeChatService weChatService;

    @Autowired
    private IPdAgreementService pdAgreementService;

    @Autowired
    private IPdChatService chatService;

    @Resource
    private Client client;

    @ApiOperation(value="须知协议-分页列表查询", notes="隐私政策列表详情")
    @GetMapping(value = "/list")
    public Result<String> queryPageList() {
        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        String tenantId = request.getHeader("tenant-id-link");
        //log.info("报价租户,游客id:{},{}",tenantId,guestId);
        if (StrUtil.isEmpty(tenantId)) {
            throw new RuntimeException("未找到租户 id");
        }
        LambdaQueryWrapper<PdAgreement> queryWrapper = new LambdaQueryWrapper<>();

        StringBuilder content = new StringBuilder();
        //不查询content字段
        queryWrapper.eq(PdAgreement::getType, "Y").eq(PdAgreement::getTenantId, tenantId);
        List<PdAgreement> list = pdAgreementService.list(queryWrapper);
        if (list == null){
            return Result.error("未找到对应数据");
        }
        for (PdAgreement pdAgreement : list) {
            content.append(pdAgreement.getContent());
        }
        return Result.OK(content.toString());
    }




    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    @ApiOperation(value="须知协议-通过id查询", notes="须知协议-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<PdAgreement> queryById(@RequestParam(name="id") String id,@RequestParam(name="name") String name) {
        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        String tenantId = request.getHeader("tenant-id-link");
        if (StrUtil.isEmpty(tenantId)) {
            return Result.error("未找到租户 id");
        }
        PdAgreement pdAgreement = null;
        if (StrUtil.isNotEmpty(id)) {
            pdAgreement = pdAgreementService.getById(id);
        }
        if (StrUtil.isNotEmpty(name)) {
            pdAgreement =  pdAgreementService.lambdaQuery().eq(PdAgreement::getName, name).eq(PdAgreement::getTenantId, tenantId).one();
        }
        if(pdAgreement==null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(pdAgreement);
    }


    @ApiOperation(value = "聊天历史记录-列表")
    @GetMapping(value = "/wechatList")
    public Result<ChatListVO> wechatList(@RequestParam String userId)  {
        ChatListVO chatListVO = weChatService.wechatList(userId);
        return Result.OK(chatListVO);
    }

    @ApiOperation(value = "聊天-发送文字")
    @PostMapping(value = "/sendMessage")
    public Result<T>  sendMessage(@RequestBody SendMessageDto dto)  {
        weChatService.sendMessage(dto);
        return Result.OK();
    }

    @ApiOperation(value = "聊天-发送文字")
    @PostMapping(value = "/sendCz")
    public Result<T>  sendCz(@RequestBody SendMessageDto dto)  {
        pdAgreementService.sendCz(dto);
        return Result.OK();
    }

    @ApiOperation(value = "聊天-获取消息返回")
    @GetMapping(value = "/getTheMessage")
    public Result<ReplyVO> getTheMessage(@RequestBody ChatIdVO dto)  {
        return Result.OK(chatService.getTheMessage(dto));
    }

    @ApiOperation(value = "车辆报价-提交")
    @PostMapping(value = "/carPrice")
    public Result<CarPriceVO> carPrice(@RequestBody QuotesDto dto)  {
        return Result.OK(weChatService.carPrice(dto));
    }

    @ApiOperation(value = "财险/增值服务-提交")
    @PostMapping(value = "/property")
    public Result<CarPriceVO> property(@RequestBody PropertyDto dto)  {
        return Result.OK(weChatService.property(dto));
    }

    @ApiOperation(value = "一期遗留-险种获取")
    @GetMapping(value = "/getRisk")
    public Result<List<Map<String, Object>>> getRisk()  {
        return Result.OK(weChatService.getRisk());
    }

    @ApiOperation(value = "一期遗留-城市省份获取")
    @GetMapping(value = "/getCity")
    @Cacheable(value = "getCity", key = "'get_city'")
    public Result<List<PropertyVO>> getCity()  {
        return Result.OK(weChatService.getCity());
    }

    @ApiOperation(value = "一期遗留-获取上架配置 1上架页;2开发页面")
    @GetMapping(value = "/getShelfConfig")
    public Result<String> getShelfConfig()  {
        return Result.OK(weChatService.getShelfConfig());
    }

    @ApiOperation(value = "一期遗留-获取上架配置 1上架页;2开发页面")
    @GetMapping(value = "/getTenant")
    public Result<String> getTenant()  {
        //获取租户中的对应字段
        return Result.OK(weChatService.getTenant());
    }


    @ApiOperation(value = "驾驶证-身份证ocr识别")
    @PostMapping(value = "/ocr", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @ApiImplicitParams(
            @ApiImplicitParam(value = "识别类型:0-身份证;1-驾驶证")
    )
    public RecognitionVO OcrTest(@RequestParam("file") MultipartFile file, @RequestParam Integer type) throws IOException {
        RecognitionVO vo = new RecognitionVO();
        vo.setType(type);
        if (type == 1) { // 驾驶证OCR识别
            log.info("ocr-驾驶证识别");
            RecognizeVehicleLicenseRequest request = new RecognizeVehicleLicenseRequest();
            request.setBody(file.getInputStream());
            try {
                RecognizeVehicleLicenseResponse response = client.recognizeVehicleLicense(request);
                CapacityVO capacityVO = new CapacityVO();

                // 假设 response.getBody() 返回的 JSON 数据包含所需的字段
                String data = response.getBody().getData(); // 获取 JSON 字符串
                JSONObject info = JSONObject.parseObject(data);
                JSONObject jsonObject = info.getJSONObject("data").getJSONObject("face").getJSONObject("data");


                // 根据返回的内容设置 CapacityVO 的属性
                capacityVO.setOwner(jsonObject.getString("owner"));
                capacityVO.setEngineNumber(jsonObject.getString("engineNumber"));
                capacityVO.setIssueDate(jsonObject.getString("issueDate"));
                capacityVO.setModel(jsonObject.getString("model"));
                capacityVO.setLicensePlateNumber(jsonObject.getString("licensePlateNumber"));
                capacityVO.setRegistrationDate(jsonObject.getString("registrationDate"));
                capacityVO.setUseNature(jsonObject.getString("useNature"));
                capacityVO.setVehicleType(jsonObject.getString("vehicleType"));
                capacityVO.setVinCode(jsonObject.getString("vinCode"));
                capacityVO.setIssueAuthority(jsonObject.getString("issueAuthority"));

                vo.setCapacityVO(capacityVO);
            } catch (TeaException error) {
                Result.error("驾驶证识别异常");
            } catch (Exception e) {
                Result.error("驾驶证解析异常");
            }
        } else if (type == 0) { // 身份证OCR识别
            log.info("ocr-身份证识别");
            RecognizeIdcardRequest idcardRequest = new RecognizeIdcardRequest();
            idcardRequest.setBody(file.getInputStream());
            try {
                RecognizeIdcardResponse idcardResponse = client.recognizeIdcard(idcardRequest);
                DrivingVO drivingVO = new DrivingVO();

                // 解析返回的 data 字段
                String datas = idcardResponse.getBody().getData();
                JSONObject jsonObject = JSONObject.parseObject(datas);
                // 获取 face 数据
                JSONObject info = jsonObject.getJSONObject("data").getJSONObject("face").getJSONObject("data");

                // 根据返回的内容设置 DrivingVO 的属性
                drivingVO.setName(info.getString("name"));
                drivingVO.setSex(info.getString("sex"));
                drivingVO.setEthnicity(info.getString("ethnicity"));
                drivingVO.setBirthDate(info.getString("birthDate"));
                drivingVO.setAddress(info.getString("address"));
                drivingVO.setIdNumber(info.getString("idNumber"));

                vo.setDrivingVO(drivingVO);
            } catch (TeaException error) {
                Result.error("身份证识别异常");
            } catch (Exception e) {
                Result.error("身份证解析异常");
            }
        }
        log.info("ocr-识别成功:{}",vo);

        return vo;
    }


}
