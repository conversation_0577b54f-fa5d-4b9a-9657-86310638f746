package org.jeecg.modules.wechat.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Data
@Accessors(chain = true)
public class AdditionalMessageDto implements Serializable {
    /**
     * 发送这条消息的实体。取值：
     * user：用户发送的消息。
     * assistant：智能体发送的消息。
     */
    private String role;

    /**
     * 消息的内容，支持纯文本、多模态等多种类型的内容。
     */
    private String content;

    /**
     * 消息内容的类型，支持设置为：
     * text：文本。
     * object_string：多模态内容。
     * card：卡片（仅在接口响应中出现）。
     */
    private String contentType;
}
