package org.jeecg.modules.wechat.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 查询条件
 */
@Data
public class ExcelChatDto implements Serializable {
    @ApiModelProperty(value = "标题名称",example = "保险")
    private String name;

    @ApiModelProperty(value = "结束日期")
    private String startDate;

    @ApiModelProperty(value = "开始日期")
    private String endDate;

    @ApiModelProperty(value = "多租户")
    private java.lang.Integer tenantId;

    @ApiModelProperty(value = "广告类型")
    private String wideType;

}
