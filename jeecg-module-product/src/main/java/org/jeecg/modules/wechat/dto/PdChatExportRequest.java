package org.jeecg.modules.wechat.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 聊天记录导出请求参数
 * <AUTHOR>
 * @date 2024-11-01
 */
@Data
@ApiModel(value = "PdChatExportRequest", description = "聊天记录导出请求参数")
public class PdChatExportRequest {

    @ApiModelProperty(value = "租户ID")
    private String tenantId;

    @ApiModelProperty(value = "日期范围，格式：开始日期,结束日期")
    private String dateRange;

    @ApiModelProperty(value = "选中的数据ID列表，逗号分隔")
    private String selections;

    @ApiModelProperty(value = "排序字段")
    private String column;

    @ApiModelProperty(value = "页码")
    private Integer pageNo;

    @ApiModelProperty(value = "页大小")
    private Integer pageSize;

    @ApiModelProperty(value = "用户ID")
    private String userId;

    @ApiModelProperty(value = "消息内容")
    private String message;

    @ApiModelProperty(value = "发送类型：0-用户，1-客服")
    private Integer sendType;

    @ApiModelProperty(value = "发送时间开始")
    private String sendTimeStart;

    @ApiModelProperty(value = "发送时间结束")
    private String sendTimeEnd;

    @ApiModelProperty(value = "创建时间开始")
    private String createTimeStart;

    @ApiModelProperty(value = "创建时间结束")
    private String createTimeEnd;
}
