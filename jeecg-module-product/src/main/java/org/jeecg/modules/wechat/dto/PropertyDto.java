package org.jeecg.modules.wechat.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;

@Data
@Accessors(chain = true)
public class PropertyDto implements Serializable {

    @ApiModelProperty(value = "用户来源 3-小程序; 4-h5 ; 5-app",required = true)
    @NotBlank(message = "用户来源不能为空")
    private Integer userSource;

    @ApiModelProperty(value = "链接类型(0-车险;1-财险;2-增值服务)",required = true)
    private java.lang.Integer linkType;

    @ApiModelProperty(value = "手机号",required = true)
    @NotBlank(message = "手机号不能为空")
    private String phone;

    @ApiModelProperty(value ="姓名",required = true)
    @NotBlank(message = "姓名不能为空")
    private String name;

    @ApiModelProperty(value = "综合服务类型")
    @NotBlank(message = "服务类型不能为空")
    private List<String> offerList;

    @ApiModelProperty("选择服务商")
    private String tenantId;
}
