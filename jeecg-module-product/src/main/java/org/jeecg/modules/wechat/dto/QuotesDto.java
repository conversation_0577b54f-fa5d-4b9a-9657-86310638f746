package org.jeecg.modules.wechat.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NonNull;
import org.jeecg.modules.wechat.vo.CapacityVO;
import org.jeecg.modules.wechat.vo.DrivingVO;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

@Data
public class QuotesDto implements Serializable {

    @ApiModelProperty(value = "用户来源 3-小程序; 4-h5 ; 5-app",required = true)
    @NotBlank(message = "用户来源不能为空")
    private Integer userSource;

    @ApiModelProperty(value = "手机号")
    private String phone;

    @ApiModelProperty(value = "商业险多选，1-交强险; 2-第三者责任险; 3-车损险; 4-车上人员责任险; 5-盗抢险; 6-玻璃单独破碎险; 7-自燃险; 8-不计免赔险; 9-发动机涉水险", required = true)
    private List<QuotesCarDto> mercialList;;

    @ApiModelProperty(value ="姓名",required = true)
    @NotBlank(message = "姓名不能为空")
    private String name;

    @ApiModelProperty(value ="新车未上牌",required = true)
    @NotBlank(message = "新车未上牌 0-否;1-是")
    private String newCarType;

    @ApiModelProperty(value ="城市",required = true)
    @NotBlank(message = "城市")
    private String city;

    @ApiModelProperty("选择服务商")
    private String tenantId;

    @ApiModelProperty("意向公司,多选")
    private List<String> companyList;

    @ApiModelProperty(value = "驾驶证" , required = true)
    @NotBlank(message = "驾驶证不能为空")
    private CapacityVO capacityVO;

    @ApiModelProperty(value = "身份证",required = true)
    @NotBlank(message = "身份证不能为空")
    private DrivingVO drivingVO;
}
