package org.jeecg.modules.wechat.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

@Data
@Accessors(chain = true)
public class SendChatDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 要进行会话聊天的智能体ID。
     * 确保当前使用的访问密钥已被授予智能体所属空间的 chat 权限。
     */
    private String botId;

    /**
     * 标识当前与智能体的用户，由使用方自行定义、生成与维护。
     * 不同的 user_id，其对话的上下文消息、数据库等对话记忆数据互相隔离。
     */
    private String userId;

    /**
     * 是否自动保存历史消息。
     */
    private boolean autoSaveHistory;
    private boolean stream=false;

    /**
     * 附加的对话信息，最多传入 100 条消息。
     * 若未设置，智能体收到的消息只有会话中已有的消息内容。
     */
    private List<AdditionalMessageDto> additionalMessages;
}
