package org.jeecg.modules.wechat.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

@Data
@Accessors(chain = true)
public class SendMessageDto implements Serializable {

    @ApiModelProperty(value = "用户发送消息",required = true)
    @NotBlank(message = "用户发送消息不能为空")
    private String message;

    @ApiModelProperty(value = "游客id",required = true)
    @NotBlank(message = "游客id不能为空")
    private String userId;

    @ApiModelProperty(value = "ip")
    private String sendIp;
}
