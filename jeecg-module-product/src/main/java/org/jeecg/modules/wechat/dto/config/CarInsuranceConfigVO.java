package org.jeecg.modules.wechat.dto.config;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 车险配置实体类
 * 对应deployType=2的配置项
 */
@Data
@Accessors(chain = true)
public class CarInsuranceConfigVO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "快照类型(0-车险;1-财险;2-增值服务)")
    private Integer configType;

    @ApiModelProperty(value = "跳出率开始区间(%)")
    private BigDecimal bounceRateStart;

    @ApiModelProperty(value = "跳出率结束区间(%)")
    private BigDecimal bounceRateEnd;

    @ApiModelProperty(value = "转化率开始区间(%)")
    private BigDecimal conversionRateStart;

    @ApiModelProperty(value = "转化率结束区间(%)")
    private BigDecimal conversionRateEnd;

    @ApiModelProperty(value = "表单提交数开始区间")
    private Integer formSubmissionsStart;

    @ApiModelProperty(value = "表单提交数结束区间")
    private Integer formSubmissionsEnd;

    @ApiModelProperty(value = "点击率开始区间(%)")
    private BigDecimal ctrStart;

    @ApiModelProperty(value = "点击率结束区间(%)")
    private BigDecimal ctrEnd;

    @ApiModelProperty(value = "平均停留时长开始区间(秒)")
    private Integer avgStayTimeStart;

    @ApiModelProperty(value = "平均停留时长结束区间(秒)")
    private Integer avgStayTimeEnd;

    @ApiModelProperty(value = "返回率开始区间(%)")
    private BigDecimal returnRateStart;

    @ApiModelProperty(value = "返回率结束区间(%)")
    private BigDecimal returnRateEnd;

    @ApiModelProperty(value = "内容完成率开始区间(%)")
    private BigDecimal completionRateStart;

    @ApiModelProperty(value = "内容完成率结束区间(%)")
    private BigDecimal completionRateEnd;

    @ApiModelProperty(value = "首屏CTR值开始区间(%)")
    private BigDecimal firstScreenCtrStart;

    @ApiModelProperty(value = "首屏CTR值结束区间(%)")
    private BigDecimal firstScreenCtrEnd;

    @ApiModelProperty(value = "内容Jump率开始区间(%)")
    private BigDecimal contentJumpRateStart;

    @ApiModelProperty(value = "内容Jump率结束区间(%)")
    private BigDecimal contentJumpRateEnd;

    @ApiModelProperty(value = "内容Return率开始区间(%)")
    private BigDecimal contentReturnRateStart;

    @ApiModelProperty(value = "内容Return率结束区间(%)")
    private BigDecimal contentReturnRateEnd;

    @ApiModelProperty(value = "Click深度分布开始区间(层)")
    private BigDecimal clickDepthStart;

    @ApiModelProperty(value = "Click深度分布结束区间(层)")
    private BigDecimal clickDepthEnd;

    @ApiModelProperty(value = "Click间隔时间开始区间(秒)")
    private BigDecimal clickIntervalTimeStart;

    @ApiModelProperty(value = "Click间隔时间结束区间(秒)")
    private BigDecimal clickIntervalTimeEnd;

    @ApiModelProperty(value = "Engagement得分开始区间(分)")
    private BigDecimal engagementScoreStart;

    @ApiModelProperty(value = "Engagement得分结束区间(分)")
    private BigDecimal engagementScoreEnd;

    @ApiModelProperty(value = "翻页率比例开始")
    private Integer pageStartNum;

    @ApiModelProperty(value = "翻页率结束")
    private Integer pageEndNum;

    @ApiModelProperty(value = "独立Query数开始区间")
    private Integer uniqueQueryStart;

    @ApiModelProperty(value = "独立Query数结束区间")
    private Integer uniqueQueryEnd;

    @ApiModelProperty(value = "TOP3 PV-CTR开始区间(%)")
    private BigDecimal top3PvCtrStart;

    @ApiModelProperty(value = "TOP3 PV-CTR结束区间(%)")
    private BigDecimal top3PvCtrEnd;

    @ApiModelProperty(value = "租户id")
    private Integer tenantId;
}
