package org.jeecg.modules.wechat.dto.news;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.jeecg.common.aspect.annotation.Dict;

import java.io.Serializable;

@Data
public class PdCarouselDto implements Serializable {
    @ApiModelProperty(value = "类型 1小程序展示;2企业管理展示;3-资讯页轮播图")
    @Dict(dicCode = "page_code")
    private Integer type;

    @ApiModelProperty(value = "多租户")
    private Integer tenantId;

    @ApiModelProperty(value = "资讯名称")
    private  String detName;

}
