package org.jeecg.modules.wechat.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: app资讯
 * @Author: jeecg-boot
 * @Date:   2024-10-27
 * @Version: V1.0
 */
@Data
@TableName("app_news")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="app_news对象", description="app资讯")
public class AppNews implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private String id;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private Date updateTime;
	/**所属部门*/
    @ApiModelProperty(value = "所属部门")
    private String sysOrgCode;
	/**类型*/
	@Excel(name = "类型", width = 15)
    @Dict(dicCode = "appnew_code")
    @ApiModelProperty(value = "类型 1-资讯；2-问答 3-避坑 4-快讯")
    private Integer type;
	/**资讯内容*/
	@Excel(name = "资讯内容", width = 15)
    @ApiModelProperty(value = "资讯内容")
    private String content;
	/**标题*/
	@Excel(name = "标题", width = 15)
    @ApiModelProperty(value = "标题")
    private String name;
    @ApiModelProperty(value = "资讯日期")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Date newsTime;
    @TableField(exist = false)
    @Excel(name = "点击数", width = 15)
    private transient Integer clicksNum;
	/**图片*/
	@Excel(name = "图片", width = 15)
    @ApiModelProperty(value = "图片")
    private String image;
    @ApiModelProperty(value = "链接")
    private String link;
    @ApiModelProperty(value = "标语")
    private String slogan;
	/**点击数主键*/
	@Excel(name = "点击数主键", width = 15)
    @ApiModelProperty(value = "点击数主键")
    private String clicksId;
	/**标题人*/
	@Excel(name = "标题人", width = 15)
    @ApiModelProperty(value = "标题人")
    private String newsName;

    @ApiModelProperty(value = "点赞数")
    private Integer holdNum;
	/**多租户*/
	@Excel(name = "多租户", width = 15)
    @ApiModelProperty(value = "多租户")
    private Integer tenantId;
    @ApiModelProperty(value = "多租户名称")
    private transient String tenantName;

}
