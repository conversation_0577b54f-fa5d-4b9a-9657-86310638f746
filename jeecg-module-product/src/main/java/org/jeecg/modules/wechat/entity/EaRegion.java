package org.jeecg.modules.wechat.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;

import java.io.Serializable;
import java.util.Date;

@Data
@TableName("ea_region")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="ea_region对象", description="ea_region")
public class EaRegion implements Serializable {
    private static final long serialVersionUID = 1L;

    /** id */
    @Excel(name = "id", width = 15)
    @ApiModelProperty(value = "id")
    private Long id;

    /** nodeType */
    @Excel(name = "节点类型", width = 15)
    @ApiModelProperty(value = "节点类型")
    private Integer nodeType;

    /** code */
    @Excel(name = "编码", width = 15)
    @ApiModelProperty(value = "编码")
    private String code;

    /** name */
    @Excel(name = "名称", width = 15)
    @ApiModelProperty(value = "名称")
    private String name;

    /** createTime */
    @Excel(name = "创建时间", width = 15)
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /** parentId */
    @Excel(name = "父类ID", width = 15)
    @ApiModelProperty(value = "父类ID")
    private Long parentId;

    /** cityLevel */
    @Excel(name = "城市等级", width = 15)
    @ApiModelProperty(value = "城市等级")
    private String cityLevel;
}