package org.jeecg.modules.wechat.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: app轮播图
 * @Author: jeecg-boot
 * @Date:   2024-11-02
 * @Version: V1.0
 */
@Data
@TableName("pd_carousel")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="pd_carousel对象", description="app轮播图")
public class PdCarousel implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private String id;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private Date updateTime;
	/**轮播图*/
	@Excel(name = "轮播图", width = 15)
    @ApiModelProperty(value = "轮播图")
    private String carouselImg;
	/**名称*/
	@Excel(name = "名称", width = 15)
    @ApiModelProperty(value = "名称")
    private String name;
    /**名称*/
    @ApiModelProperty(value = "副标题")
    private String subName;
    @ApiModelProperty(value = "类型 1小程序展示;2企业管理展示;3-资讯页轮播图")
    @Dict(dicCode = "page_code")
    private Integer type;
	/**置顶*/
	@Excel(name = "置顶", width = 15)
    @ApiModelProperty(value = "置顶")
    private String top;
	/**删除*/
	@Excel(name = "删除", width = 15)
    @ApiModelProperty(value = "删除")
    private Integer isDelete;
    /**多租户*/
    @Excel(name = "多租户", width = 15)
    @ApiModelProperty(value = "多租户")
    private Integer tenantId;
    @ApiModelProperty(value = "多租户名称")
    private transient String tenantName;
}
