package org.jeecg.modules.wechat.entity;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.jeecg.common.aspect.annotation.Dict;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 聊天记录
 * @Author: jeecg-boot
 * @Date:   2024-11-01
 * @Version: V1.0
 */
@Data
@TableName("pd_chat")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="pd_chat对象", description="聊天记录")
public class PdChat implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private String id;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private Date createTime;
	/**ip地址*/
	@Excel(name = "ip地址", width = 15)
    @ApiModelProperty(value = "ip地址")
    private String ipAddress;
	/**对话用户*/
    @ApiModelProperty(value = "对话用户")
    private String userId;
    @Excel(name = "对话用户", width = 20)
    private transient String userName;

    @ApiModelProperty(value = "类型 0-用户;1-客服")
    @Excel(name = "对话方", width = 15, dicCode = "send_type")
    @Dict(dicCode = "send_type")
    private Integer sendType;
    @Excel(name = "消息内容", width = 40)
    @ApiModelProperty(value = "消息内容")
    private String message;
	/**发送时间*/
	@Excel(name = "发送时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "发送时间")
    private Date sendTime;
	/**多租户id*/
    @ApiModelProperty(value = "多租户id")
    private Integer tenantId;
    @Excel(name = "多租户名称", width = 21)
    @ApiModelProperty(value = "多租户名称")
    private transient String tenantName;
}
