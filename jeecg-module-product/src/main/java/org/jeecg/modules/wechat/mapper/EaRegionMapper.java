package org.jeecg.modules.wechat.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.wechat.entity.EaRegion;

import java.util.List;

/**
 * @Description: 区域信息
 * @Author: jeecg-boot
 * @Date: 2024-11-12
 * @Version: V1.0
 */
public interface EaRegionMapper extends BaseMapper<EaRegion> {

    /**
     * 根据城市名称模糊查询城市信息
     * @param cityName 城市名称
     * @return 城市信息列表
     */
    List<EaRegion> findByCityNameLike(@Param("cityName") String cityName);

    /**
     * 根据城市编码查询城市信息
     * @param code 城市编码
     * @return 城市信息
     */
    EaRegion findByCode(@Param("code") String code);

    /**
     * 根据城市ID查询完整城市路径名称
     * @param id 城市ID
     * @return 完整城市路径名称
     */
    String getFullCityNameById(@Param("id") Long id);

    /**
     * 根据城市ID查询顶级城市名称
     * @param id 城市ID
     * @return 顶级城市名称
     */
    String getTopCityNameById(@Param("id") Long id);

    /**
     * 根据父ID列表查询子城市
     * @param parentIds 父ID列表
     * @return 子城市列表
     */
    List<EaRegion> findByParentIds(@Param("parentIds") List<Long> parentIds);

    /**
     * 查询所有一级节点（省份）
     * @return 省份列表
     */
    List<EaRegion> findProvinces();

    /**
     * 根据父ID查询子城市
     * @param parentId 父ID
     * @return 子城市列表
     */
    List<EaRegion> findCitiesByParentId(@Param("parentId") Long parentId);
}
