package org.jeecg.modules.wechat.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.wechat.dto.news.PdCarouselDto;
import org.jeecg.modules.wechat.entity.PdCarousel;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.jeecg.modules.wechat.vo.PdCarouselVO;

/**
 * @Description: app轮播图
 * @Author: jeecg-boot
 * @Date:   2024-11-02
 * @Version: V1.0
 */
public interface PdCarouselMapper extends BaseMapper<PdCarousel> {

    IPage<PdCarouselVO> queryPageList(Page<PdCarouselVO> page, @Param("dto") PdCarouselDto dto);

    String getByTenant(String tenantId);
}
