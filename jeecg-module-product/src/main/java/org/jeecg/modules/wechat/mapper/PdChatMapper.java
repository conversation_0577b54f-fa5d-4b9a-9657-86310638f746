package org.jeecg.modules.wechat.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.wechat.entity.PdChat;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * @Description: 聊天记录
 * @Author: jeecg-boot
 * @Date:   2024-11-01
 * @Version: V1.0
 */
public interface PdChatMapper extends BaseMapper<PdChat> {

    String getShelfConfig();

    String getConfig(@Param("tenantId") String tenantId);

    /**
     * 分页查询聊天记录，并联查游客名称
     *
     * @param page 分页参数
     * @param queryWrapper 查询条件
     * @return 分页结果
     */
    IPage<PdChat> pageList(Page<PdChat> page, @Param("ew") com.baomidou.mybatisplus.core.conditions.Wrapper<PdChat> queryWrapper);

    /**
     * 导出查询聊天记录，使用DTO参数
     *
     * @param page 分页参数
     * @param exportRequest 导出请求参数
     * @return 分页结果
     */
    IPage<PdChat> exportPageList(Page<PdChat> page, @Param("dto") org.jeecg.modules.wechat.dto.PdChatExportRequest exportRequest);
}
