<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.wechat.mapper.AppNewsMapper">


    <select id="getShelfConfig" resultType="java.lang.String">
        SELECT is_top FROM sys_tenant WHERE id = #{tenantId}
    </select>
    <select id="getRisk" resultType="java.util.Map">
        SELECT item.item_value,item.item_text
        FROM sys_dict_item item
                 join sys_dict dict on dict.id = item.dict_id
        WHERE dict.dict_code = 'commerce_code'
    </select>
    <select id="getTenant" resultType="java.lang.String">
    SELECT is_top FROM sys_tenant WHERE id = #{tenantId}

    </select>
    <select id="queryPageList" resultType="org.jeecg.modules.wechat.entity.AppNews">
        SELECT
        n.id,
        n.create_time AS createTime,
        n.type,
        n.name,
        n.news_time AS newsTime,
        n.slogan,
        n.clicks_id AS clicksId,
        n.news_name AS newsName,
        n.hold_num AS holdNum,
        t.name AS tenantName,
        IFNULL(w.wh_num, 0) AS clicksNum
        FROM app_news n
        LEFT JOIN sys_tenant t ON n.tenant_id = t.id
        LEFT JOIN wh_click w ON n.clicks_id = w.id
        WHERE 1=1
        <if test="dto.type != null and dto.type.size > 0">
            AND n.type IN
            <foreach collection="dto.type" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="dto.name != null and dto.name != ''">
            AND n.name LIKE CONCAT('%', #{dto.name}, '%')
        </if>
        ORDER BY n.news_time DESC
    </select>


</mapper>