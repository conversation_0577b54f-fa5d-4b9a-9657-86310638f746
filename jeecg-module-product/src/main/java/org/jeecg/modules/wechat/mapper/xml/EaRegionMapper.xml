<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.wechat.mapper.EaRegionMapper">

    <!-- 根据城市名称模糊查询城市信息 -->
    <select id="findByCityNameLike" resultType="org.jeecg.modules.wechat.entity.EaRegion">
        SELECT
            id,
            node_type as nodeType,
            code,
            name,
            create_time as createTime,
            parent_id as parentId,
            city_level as cityLevel
        FROM
            ea_region
        WHERE
            name LIKE CONCAT('%', #{cityName}, '%')
    </select>

    <!-- 根据城市编码查询城市信息 -->
    <select id="findByCode" resultType="org.jeecg.modules.wechat.entity.EaRegion">
        SELECT
            id,
            node_type as nodeType,
            code,
            name,
            create_time as createTime,
            parent_id as parentId,
            city_level as cityLevel
        FROM
            ea_region
        WHERE
            code = #{code}
        LIMIT 1
    </select>

    <!-- 根据城市ID查询完整城市路径名称 -->
    <select id="getFullCityNameById" resultType="java.lang.String">
        SELECT name FROM ea_region WHERE id = #{id}
    </select>

    <!-- 根据城市ID查询顶级城市名称 -->
    <select id="getTopCityNameById" resultType="java.lang.String">
        SELECT
            (SELECT p.name FROM ea_region p
             WHERE p.id = (SELECT r.parent_id FROM ea_region r WHERE r.id = #{id})
             AND p.node_type = 1
            ) AS top_city
    </select>

    <!-- 根据父ID列表查询子城市 -->
    <select id="findByParentIds" resultType="org.jeecg.modules.wechat.entity.EaRegion">
        SELECT
            id,
            node_type as nodeType,
            code,
            name,
            create_time as createTime,
            parent_id as parentId,
            city_level as cityLevel
        FROM
            ea_region
        WHERE
            parent_id IN
            <foreach collection="parentIds" item="parentId" open="(" separator="," close=")">
                #{parentId}
            </foreach>
        ORDER BY code ASC
    </select>

    <!-- 查询所有一级节点（省份） -->
    <select id="findProvinces" resultType="org.jeecg.modules.wechat.entity.EaRegion">
        SELECT
            id,
            node_type as nodeType,
            code,
            name,
            create_time as createTime,
            parent_id as parentId,
            city_level as cityLevel
        FROM
            ea_region
        WHERE
            parent_id = 0 OR parent_id IS NULL
        ORDER BY code ASC
    </select>

    <!-- 根据父ID查询子城市 -->
    <select id="findCitiesByParentId" resultType="org.jeecg.modules.wechat.entity.EaRegion">
        SELECT
            id,
            node_type as nodeType,
            code,
            name,
            create_time as createTime,
            parent_id as parentId,
            city_level as cityLevel
        FROM
            ea_region
        WHERE
            parent_id = #{parentId}
        ORDER BY code ASC
    </select>

</mapper>
