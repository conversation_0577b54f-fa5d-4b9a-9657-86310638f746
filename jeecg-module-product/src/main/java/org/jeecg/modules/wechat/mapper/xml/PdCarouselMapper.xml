<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.wechat.mapper.PdCarouselMapper">
    <select id="queryPageList" resultType="org.jeecg.modules.wechat.vo.PdCarouselVO">
        SELECT
        c.id,
        c.create_by AS createBy,
        c.create_time AS createTime,
        c.update_by AS updateBy,
        c.update_time AS updateTime,
        c.carousel_img AS carouselImg,
        c.name,
        c.sub_name AS subName,
        c.type,
        c.top,
        c.is_delete AS isDelete,
        c.tenant_id AS tenantId,
        t.name AS tenantName,
        c.det_id AS detId,
        n.name AS detName
        FROM pd_carousel c
        LEFT JOIN app_news n ON c.det_id = n.id
        LEFT JOIN sys_tenant t ON c.tenant_id = t.id
        WHERE c.is_delete = 0
        <if test="dto.type != null">
            AND c.type = #{dto.type}
        </if>
        <if test="dto.tenantId != null">
            AND c.tenant_id = #{dto.tenantId}
        </if>
        <if test="dto.detName != null and dto.detName != ''">
            AND n.name LIKE CONCAT('%', #{dto.detName}, '%')
        </if>
        ORDER BY c.top DESC, c.create_time DESC
    </select>
    <select id="getByTenant" resultType="java.lang.String">
        SELECT car.carousel_img AS carouselImg
        FROM pd_carousel car
        WHERE car.tenant_id = #{tenantId} AND car.type = 4 LIMIT 1
    </select>

</mapper>