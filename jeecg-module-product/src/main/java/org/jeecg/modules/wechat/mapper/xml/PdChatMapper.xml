<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.wechat.mapper.PdChatMapper">

    <select id="getShelfConfig" resultType="java.lang.String">
        SELECT item.item_value FROM sys_dict_item item
            join  sys_dict dict  on dict.id =item.dict_id
            WHERE dict.dict_code = 'page_code' AND item.status=1
    </select>
    <select id="getConfig" resultType="java.lang.String">
        SELECT is_top FROM sys_tenant WHERE id = #{tenantId}
    </select>

    <!-- 分页查询聊天记录，并联查游客名称 -->
    <select id="pageList" resultType="org.jeecg.modules.wechat.entity.PdChat">
        SELECT
            c.id,
            c.create_by,
            c.create_time,
            c.ip_address,
            c.user_id,
            c.send_type,
            c.message,
            c.send_time,
            c.tenant_id,
            g.name AS userName,
            t.name AS tenantName
        FROM pd_chat c
        LEFT JOIN pd_guest_users g ON c.user_id = g.id
        LEFT JOIN sys_tenant t ON c.tenant_id = t.id
        ${ew.customSqlSegment}
        ORDER BY c.send_time DESC
    </select>

    <!-- 导出查询聊天记录，使用DTO参数 -->
    <select id="exportPageList" resultType="org.jeecg.modules.wechat.entity.PdChat">
        SELECT
            c.id,
            c.create_by,
            c.create_time,
            c.ip_address,
            c.user_id,
            c.send_type,
            c.message,
            c.send_time,
            c.tenant_id,
            g.name AS userName,
            t.name AS tenantName
        FROM pd_chat c
        LEFT JOIN pd_guest_users g ON c.user_id = g.id
        LEFT JOIN sys_tenant t ON c.tenant_id = t.id
        <where>
            <if test="dto.tenantId != null and dto.tenantId != ''">
                AND c.tenant_id = #{dto.tenantId}
            </if>
            <if test="dto.userId != null and dto.userId != ''">
                AND c.user_id = #{dto.userId}
            </if>
            <if test="dto.message != null and dto.message != ''">
                AND c.message LIKE CONCAT('%', #{dto.message}, '%')
            </if>
            <if test="dto.sendType != null">
                AND c.send_type = #{dto.sendType}
            </if>
            <if test="dto.sendTimeStart != null and dto.sendTimeStart != ''">
                AND c.send_time >= #{dto.sendTimeStart}
            </if>
            <if test="dto.sendTimeEnd != null and dto.sendTimeEnd != ''">
                AND c.send_time &lt;= #{dto.sendTimeEnd}
            </if>
            <if test="dto.createTimeStart != null and dto.createTimeStart != ''">
                AND c.create_time >= #{dto.createTimeStart}
            </if>
            <if test="dto.createTimeEnd != null and dto.createTimeEnd != ''">
                AND c.create_time &lt;= #{dto.createTimeEnd}
            </if>
            <if test="dto.dateRange != null and dto.dateRange != ''">
                <bind name="dateArray" value="dto.dateRange.split(',')"/>
                <if test="dateArray.length == 2">
                    AND c.send_time >= CONCAT(#{dateArray[0]}, ' 00:00:00')
                    AND c.send_time &lt;= CONCAT(#{dateArray[1]}, ' 23:59:59')
                </if>
            </if>
            <if test="dto.selections != null and dto.selections != ''">
                <bind name="selectionArray" value="dto.selections.split(',')"/>
                AND c.id IN
                <foreach collection="selectionArray" item="id" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
        </where>
        ORDER BY
        c.create_time DESC
    </select>
</mapper>