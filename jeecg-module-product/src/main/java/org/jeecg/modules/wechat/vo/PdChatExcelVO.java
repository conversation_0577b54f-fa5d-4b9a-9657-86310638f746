package org.jeecg.modules.wechat.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * 聊天记录导出Excel VO
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class PdChatExcelVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 租户名称
     */
    @ExcelProperty("租户名称")
    @ApiModelProperty(value = "租户名称")
    private String tenantName;

    /**
     * 用户名称
     */
    @ExcelProperty("用户名称")
    @ApiModelProperty(value = "用户名称")
    private String userName;

    /**
     * 消息内容
     */
    @ExcelProperty("消息内容")
    @ApiModelProperty(value = "消息内容")
    private String message;

    /**
     * 发送类型
     */
    @ExcelProperty("发送方")
    @ApiModelProperty(value = "发送方")
    private String sendTypeStr;



    /**
     * 发送时间
     */
    @ExcelProperty("发送时间")
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "发送时间")
    private Date sendTime;

    /**
     * 创建时间
     */
    @ExcelProperty("创建时间")
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
}
