# Excel导出配置
excel:
  export:
    # 单sheet模式的数据量阈值（默认10万）
    # 超过此阈值将使用多sheet模式
    single-sheet-threshold: 100000
    
    # 每个sheet的最大行数（默认50万）
    # Excel理论最大值为1048576行，设置为50万留有余量
    max-rows-per-sheet: 500000
    
    # 分页查询的页大小（默认1万）
    # 用于控制每次从数据库查询的数据量
    page-size: 10000
    
    # 批量查询关联数据的批次大小（默认1000）
    # 用于批量查询租户、用户等关联信息
    batch-query-size: 1000
    
    # 导出超时时间（秒，默认30分钟）
    export-timeout-seconds: 1800
    
    # 是否启用导出进度监控
    enable-progress-monitor: true
    
    # 内存使用监控阈值（MB，默认500MB）
    # 当内存使用超过此阈值时会记录警告日志
    memory-warning-threshold: 500

# 针对不同环境的配置
---
spring:
  profiles: dev
excel:
  export:
    # 开发环境使用较小的阈值便于测试
    single-sheet-threshold: 10000
    max-rows-per-sheet: 50000
    page-size: 1000
    memory-warning-threshold: 200

---
spring:
  profiles: test
excel:
  export:
    single-sheet-threshold: 50000
    max-rows-per-sheet: 200000
    page-size: 5000
    memory-warning-threshold: 300

---
spring:
  profiles: prod
excel:
  export:
    # 生产环境使用更大的阈值以提高性能
    single-sheet-threshold: 200000
    max-rows-per-sheet: 800000
    page-size: 20000
    memory-warning-threshold: 1000
