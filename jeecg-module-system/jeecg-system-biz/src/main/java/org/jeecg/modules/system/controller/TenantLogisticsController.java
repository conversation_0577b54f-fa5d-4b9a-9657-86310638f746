package org.jeecg.modules.system.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.modules.system.entity.WlRegionUserRel;
import org.jeecg.modules.system.service.IWlRegionUserRelService;
import org.jeecg.modules.system.service.ITenantLogisticsService;
import org.jeecg.modules.system.vo.TenantLogisticsStaffVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;

/**
 * @Description: 租户后勤人员配置Controller
 * @Author: jeecg-boot
 * @Date: 2024-01-01
 * @Version: V1.0
 */
@Api(tags="租户后勤人员配置管理")
@RestController
@RequestMapping("/sys/tenant/logistics")
@Slf4j
public class TenantLogisticsController extends JeecgController<WlRegionUserRel, IWlRegionUserRelService> {

    @Autowired
    private IWlRegionUserRelService wlRegionUserRelService;

    @Autowired
    private ITenantLogisticsService tenantLogisticsService;

    /**
     * 根据租户、保险公司、地区查询后勤人员列表
     * @param userTenantId 租户ID
     * @param insuranceCompanyId 保险公司ID
     * @param regionCode 地区编码
     * @return
     */
    @AutoLog(value = "租户后勤人员配置-查询后勤人员列表")
    @ApiOperation(value="租户后勤人员配置-查询后勤人员列表", notes="租户后勤人员配置-查询后勤人员列表")
    @GetMapping(value = "/list")
    public Result<List<TenantLogisticsStaffVO>> queryLogisticsStaffList(
            @RequestParam(name="userTenantId") String userTenantId,
            @RequestParam(name="insuranceCompanyId") String insuranceCompanyId,
            @RequestParam(name="regionCode") String regionCode) {
        try {
            List<TenantLogisticsStaffVO> list = tenantLogisticsService.getLogisticsStaffList(userTenantId, insuranceCompanyId, regionCode);
            return Result.OK(list);
        } catch (Exception e) {
            log.error("查询后勤人员列表失败", e);
            return Result.error("查询后勤人员列表失败：" + e.getMessage());
        }
    }

    /**
     * 添加后勤人员关系
     * @param userTenantId 租户ID
     * @param insuranceCompanyId 保险公司ID
     * @param regionCode 地区编码
     * @param userIds 用户ID列表（逗号分隔）
     * @return
     */
    @AutoLog(value = "租户后勤人员配置-添加后勤人员")
    @ApiOperation(value="租户后勤人员配置-添加后勤人员", notes="租户后勤人员配置-添加后勤人员")
    @PostMapping(value = "/add")
    public Result<String> addLogisticsStaff(
            @RequestParam(name="userTenantId") String userTenantId,
            @RequestParam(name="insuranceCompanyId") String insuranceCompanyId,
            @RequestParam(name="regionCode") String regionCode,
            @RequestParam(name="userIds") String userIds) {
        try {
            List<String> userIdList = Arrays.asList(userIds.split(","));
            boolean success = tenantLogisticsService.addLogisticsStaff(userTenantId, insuranceCompanyId, regionCode, userIdList);
            if (success) {
                return Result.OK("添加后勤人员成功！");
            } else {
                return Result.error("添加后勤人员失败！");
            }
        } catch (Exception e) {
            log.error("添加后勤人员失败", e);
            return Result.error("添加后勤人员失败：" + e.getMessage());
        }
    }

    /**
     * 批量添加后勤人员关系
     * @param userTenantId 租户ID
     * @param insuranceCompanyId 保险公司ID
     * @param batchData 批量数据，格式：userId1:regionCode1,regionCode2;userId2:regionCode3
     * @return
     */
    @AutoLog(value = "租户后勤人员配置-批量添加后勤人员")
    @ApiOperation(value="租户后勤人员配置-批量添加后勤人员", notes="租户后勤人员配置-批量添加后勤人员")
    @PostMapping(value = "/batchAdd")
    public Result<String> batchAddLogisticsStaff(
            @RequestParam(name="userTenantId") String userTenantId,
            @RequestParam(name="insuranceCompanyId") String insuranceCompanyId,
            @RequestParam(name="batchData") String batchData) {
        try {
            boolean success = tenantLogisticsService.batchAddLogisticsStaff(userTenantId, insuranceCompanyId, batchData);
            if (success) {
                return Result.OK("批量添加后勤人员成功！");
            } else {
                return Result.error("批量添加后勤人员失败！");
            }
        } catch (Exception e) {
            log.error("批量添加后勤人员失败", e);
            return Result.error("批量添加后勤人员失败：" + e.getMessage());
        }
    }

    /**
     * 移除后勤人员关系
     * @param userTenantId 租户ID
     * @param insuranceCompanyId 保险公司ID
     * @param regionCode 地区编码
     * @param userIds 用户ID列表（逗号分隔）
     * @return
     */
    @AutoLog(value = "租户后勤人员配置-移除后勤人员")
    @ApiOperation(value="租户后勤人员配置-移除后勤人员", notes="租户后勤人员配置-移除后勤人员")
    @DeleteMapping(value = "/remove")
    public Result<String> removeLogisticsStaff(
            @RequestParam(name="userTenantId") String userTenantId,
            @RequestParam(name="insuranceCompanyId") String insuranceCompanyId,
            @RequestParam(name="regionCode") String regionCode,
            @RequestParam(name="userIds") String userIds) {
        try {
            List<String> userIdList = Arrays.asList(userIds.split(","));
            boolean success = tenantLogisticsService.removeLogisticsStaff(userTenantId, insuranceCompanyId, regionCode, userIdList);
            if (success) {
                return Result.OK("移除后勤人员成功！");
            } else {
                return Result.error("移除后勤人员失败！");
            }
        } catch (Exception e) {
            log.error("移除后勤人员失败", e);
            return Result.error("移除后勤人员失败：" + e.getMessage());
        }
    }

    /**
     * 根据关系ID删除后勤人员
     * @param id 关系ID
     * @return
     */
    @AutoLog(value = "租户后勤人员配置-删除后勤人员")
    @ApiOperation(value="租户后勤人员配置-删除后勤人员", notes="租户后勤人员配置-删除后勤人员")
    @DeleteMapping(value = "/deleteById")
    public Result<String> deleteLogisticsStaffById(@RequestParam(name="id") String id) {
        try {
            boolean success = wlRegionUserRelService.removeById(id);
            if (success) {
                return Result.OK("删除后勤人员成功！");
            } else {
                return Result.error("删除后勤人员失败！");
            }
        } catch (Exception e) {
            log.error("删除后勤人员失败", e);
            return Result.error("删除后勤人员失败：" + e.getMessage());
        }
    }

    /**
     * 批量删除后勤人员
     * @param ids 关系ID列表（逗号分隔）
     * @return
     */
    @AutoLog(value = "租户后勤人员配置-批量删除后勤人员")
    @ApiOperation(value="租户后勤人员配置-批量删除后勤人员", notes="租户后勤人员配置-批量删除后勤人员")
    @DeleteMapping(value = "/batchDelete")
    public Result<String> batchDeleteLogisticsStaff(@RequestParam(name="ids") String ids) {
        try {
            List<String> idList = Arrays.asList(ids.split(","));
            boolean success = wlRegionUserRelService.removeBatchByIds(idList);
            if (success) {
                return Result.OK("批量删除后勤人员成功！");
            } else {
                return Result.error("批量删除后勤人员失败！");
            }
        } catch (Exception e) {
            log.error("批量删除后勤人员失败", e);
            return Result.error("批量删除后勤人员失败：" + e.getMessage());
        }
    }

    /**
     * 根据租户ID查询所有后勤人员配置
     * @param userTenantId 租户ID
     * @return
     */
    @AutoLog(value = "租户后勤人员配置-查询租户所有后勤人员")
    @ApiOperation(value="租户后勤人员配置-查询租户所有后勤人员", notes="租户后勤人员配置-查询租户所有后勤人员")
    @GetMapping(value = "/listByTenant")
    public Result<List<TenantLogisticsStaffVO>> queryLogisticsStaffByTenant(
            @RequestParam(name="userTenantId") String userTenantId) {
        try {
            List<TenantLogisticsStaffVO> list = tenantLogisticsService.getLogisticsStaffByTenant(userTenantId);
            return Result.OK(list);
        } catch (Exception e) {
            log.error("查询租户后勤人员失败", e);
            return Result.error("查询租户后勤人员失败：" + e.getMessage());
        }
    }
}
