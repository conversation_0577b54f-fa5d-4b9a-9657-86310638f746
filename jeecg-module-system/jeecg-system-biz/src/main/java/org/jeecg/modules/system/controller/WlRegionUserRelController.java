package org.jeecg.modules.system.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.modules.system.entity.WlRegionUserRel;
import org.jeecg.modules.system.service.IWlRegionUserRelService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.List;

/**
 * @Description: 地区用户关系表Controller
 * @Author: jeecg-boot
 * @Date: 2024-01-01
 * @Version: V1.0
 */
@Api(tags="后勤人员配置管理")
@RestController
@RequestMapping("/sys/wlRegionUserRel")
@Slf4j
public class WlRegionUserRelController extends JeecgController<WlRegionUserRel, IWlRegionUserRelService> {

    @Autowired
    private IWlRegionUserRelService wlRegionUserRelService;

    /**
     * 分页列表查询
     * @param wlRegionUserRel
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @AutoLog(value = "地区用户关系表-分页列表查询")
    @ApiOperation(value="地区用户关系表-分页列表查询", notes="地区用户关系表-分页列表查询")
    @GetMapping(value = "/list")
    public Result<IPage<WlRegionUserRel>> queryPageList(WlRegionUserRel wlRegionUserRel,
                                                        @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
                                                        @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
                                                        HttpServletRequest req) {
        QueryWrapper<WlRegionUserRel> queryWrapper = QueryGenerator.initQueryWrapper(wlRegionUserRel, req.getParameterMap());
        Page<WlRegionUserRel> page = new Page<WlRegionUserRel>(pageNo, pageSize);
        IPage<WlRegionUserRel> pageList = wlRegionUserRelService.page(page, queryWrapper);
        return Result.OK(pageList);
    }

    /**
     * 根据租户ID查询后勤人员关系列表
     * @param tenantId
     * @return
     */
    @AutoLog(value = "地区用户关系表-根据租户ID查询")
    @ApiOperation(value="地区用户关系表-根据租户ID查询", notes="地区用户关系表-根据租户ID查询")
    @GetMapping(value = "/listByTenantId")
    public Result<List<WlRegionUserRel>> queryByTenantId(@RequestParam(name="tenantId") String tenantId) {
        List<WlRegionUserRel> list = wlRegionUserRelService.getByTenantId(tenantId);
        return Result.OK(list);
    }

    /**
     * 根据租户ID和地区编码查询后勤人员关系列表
     * @param tenantId
     * @param regionCode
     * @return
     */
    @AutoLog(value = "地区用户关系表-根据租户ID和地区编码查询")
    @ApiOperation(value="地区用户关系表-根据租户ID和地区编码查询", notes="地区用户关系表-根据租户ID和地区编码查询")
    @GetMapping(value = "/listByTenantIdAndRegionCode")
    public Result<List<WlRegionUserRel>> queryByTenantIdAndRegionCode(@RequestParam(name="tenantId") String tenantId,
                                                                      @RequestParam(name="regionCode") String regionCode) {
        List<WlRegionUserRel> list = wlRegionUserRelService.getByTenantIdAndRegionCode(tenantId, regionCode);
        return Result.OK(list);
    }

    /**
     * 根据租户ID、保险公司ID和地区编码查询后勤人员关系列表
     * @param tenantId
     * @param insuranceCompanyId
     * @param regionCode
     * @return
     */
    @AutoLog(value = "地区用户关系表-根据租户保险公司地区查询")
    @ApiOperation(value="地区用户关系表-根据租户保险公司地区查询", notes="地区用户关系表-根据租户保险公司地区查询")
    @GetMapping(value = "/listByTenantInsuranceRegion")
    public Result<List<WlRegionUserRel>> queryByTenantInsuranceRegion(@RequestParam(name="tenantId") String tenantId,
                                                                      @RequestParam(name="insuranceCompanyId") String insuranceCompanyId,
                                                                      @RequestParam(name="regionCode") String regionCode) {
        List<WlRegionUserRel> list = wlRegionUserRelService.getByTenantInsuranceRegion(tenantId, insuranceCompanyId, regionCode);
        return Result.OK(list);
    }

    /**
     * 根据用户ID查询后勤人员关系列表
     * @param userId
     * @return
     */
    @AutoLog(value = "地区用户关系表-根据用户ID查询")
    @ApiOperation(value="地区用户关系表-根据用户ID查询", notes="地区用户关系表-根据用户ID查询")
    @GetMapping(value = "/listByUserId")
    public Result<List<WlRegionUserRel>> queryByUserId(@RequestParam(name="userId") String userId) {
        List<WlRegionUserRel> list = wlRegionUserRelService.getByUserId(userId);
        return Result.OK(list);
    }

    /**
     * 添加后勤人员关系
     * @param tenantId
     * @param insuranceCompanyId
     * @param regionCode
     * @param userIds
     * @return
     */
    @AutoLog(value = "地区用户关系表-添加后勤人员关系")
    @ApiOperation(value="地区用户关系表-添加后勤人员关系", notes="地区用户关系表-添加后勤人员关系")
    @PostMapping(value = "/addRelation")
    public Result<String> addRelation(@RequestParam(name="tenantId") String tenantId,
                                      @RequestParam(name="insuranceCompanyId") String insuranceCompanyId,
                                      @RequestParam(name="regionCode") String regionCode,
                                      @RequestParam(name="userIds") String userIds) {
        try {
            List<String> userIdList = Arrays.asList(userIds.split(","));
            boolean success = wlRegionUserRelService.addRegionUserRel(tenantId, insuranceCompanyId, regionCode, userIdList);
            if (success) {
                return Result.OK("添加成功！");
            } else {
                return Result.error("添加失败！");
            }
        } catch (Exception e) {
            log.error("添加后勤人员关系失败", e);
            return Result.error("添加失败：" + e.getMessage());
        }
    }

    /**
     * 移除后勤人员关系
     * @param tenantId
     * @param insuranceCompanyId
     * @param regionCode
     * @param userIds
     * @return
     */
    @AutoLog(value = "地区用户关系表-移除后勤人员关系")
    @ApiOperation(value="地区用户关系表-移除后勤人员关系", notes="地区用户关系表-移除后勤人员关系")
    @DeleteMapping(value = "/removeRelation")
    public Result<String> removeRelation(@RequestParam(name="tenantId") String tenantId,
                                         @RequestParam(name="insuranceCompanyId") String insuranceCompanyId,
                                         @RequestParam(name="regionCode") String regionCode,
                                         @RequestParam(name="userIds") String userIds) {
        try {
            List<String> userIdList = Arrays.asList(userIds.split(","));
            boolean success = wlRegionUserRelService.removeRegionUserRel(tenantId, insuranceCompanyId, regionCode, userIdList);
            if (success) {
                return Result.OK("移除成功！");
            } else {
                return Result.error("移除失败！");
            }
        } catch (Exception e) {
            log.error("移除后勤人员关系失败", e);
            return Result.error("移除失败：" + e.getMessage());
        }
    }

    /**
     * 通过id删除
     * @param id
     * @return
     */
    @AutoLog(value = "地区用户关系表-通过id删除")
    @ApiOperation(value="地区用户关系表-通过id删除", notes="地区用户关系表-通过id删除")
    @DeleteMapping(value = "/delete")
    public Result<String> delete(@RequestParam(name="id") String id) {
        try {
            wlRegionUserRelService.removeById(id);
            return Result.OK("删除成功!");
        } catch (Exception e) {
            log.error("删除失败", e);
            return Result.error("删除失败!");
        }
    }

    /**
     * 批量删除
     * @param ids
     * @return
     */
    @AutoLog(value = "地区用户关系表-批量删除")
    @ApiOperation(value="地区用户关系表-批量删除", notes="地区用户关系表-批量删除")
    @DeleteMapping(value = "/deleteBatch")
    public Result<String> deleteBatch(@RequestParam(name="ids") String ids) {
        try {
            List<String> idList = Arrays.asList(ids.split(","));
            boolean success = wlRegionUserRelService.removeBatchByIds(idList);
            if (success) {
                return Result.OK("批量删除成功!");
            } else {
                return Result.error("批量删除失败!");
            }
        } catch (Exception e) {
            log.error("批量删除失败", e);
            return Result.error("批量删除失败!");
        }
    }

    /**
     * 通过id查询
     * @param id
     * @return
     */
    @AutoLog(value = "地区用户关系表-通过id查询")
    @ApiOperation(value="地区用户关系表-通过id查询", notes="地区用户关系表-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<WlRegionUserRel> queryById(@RequestParam(name="id") String id) {
        WlRegionUserRel wlRegionUserRel = wlRegionUserRelService.getById(id);
        if (wlRegionUserRel == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(wlRegionUserRel);
    }
}
