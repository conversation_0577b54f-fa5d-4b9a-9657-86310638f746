package org.jeecg.modules.system.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.modules.system.entity.WlTenantInsuranceRel;
import org.jeecg.modules.system.service.IWlTenantInsuranceRelService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.List;

/**
 * @Description: 租户保险公司关系表Controller
 * @Author: jeecg-boot
 * @Date: 2024-01-01
 * @Version: V1.0
 */
@Api(tags="租户保险公司关系管理")
@RestController
@RequestMapping("/sys/wlTenantInsuranceRel")
@Slf4j
public class WlTenantInsuranceRelController extends JeecgController<WlTenantInsuranceRel, IWlTenantInsuranceRelService> {

    @Autowired
    private IWlTenantInsuranceRelService wlTenantInsuranceRelService;

    /**
     * 分页列表查询
     * @param wlTenantInsuranceRel
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @AutoLog(value = "租户保险公司关系表-分页列表查询")
    @ApiOperation(value="租户保险公司关系表-分页列表查询", notes="租户保险公司关系表-分页列表查询")
    @GetMapping(value = "/list")
    public Result<IPage<WlTenantInsuranceRel>> queryPageList(WlTenantInsuranceRel wlTenantInsuranceRel,
                                                             @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
                                                             @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
                                                             HttpServletRequest req) {
        QueryWrapper<WlTenantInsuranceRel> queryWrapper = QueryGenerator.initQueryWrapper(wlTenantInsuranceRel, req.getParameterMap());
        Page<WlTenantInsuranceRel> page = new Page<WlTenantInsuranceRel>(pageNo, pageSize);
        IPage<WlTenantInsuranceRel> pageList = wlTenantInsuranceRelService.page(page, queryWrapper);
        return Result.OK(pageList);
    }

    /**
     * 根据租户ID查询保险公司关系列表
     * @param tenantId
     * @return
     */
    @AutoLog(value = "租户保险公司关系表-根据租户ID查询")
    @ApiOperation(value="租户保险公司关系表-根据租户ID查询", notes="租户保险公司关系表-根据租户ID查询")
    @GetMapping(value = "/listByTenantId")
    public Result<List<WlTenantInsuranceRel>> queryByTenantId(@RequestParam(name="tenantId") String tenantId) {
        List<WlTenantInsuranceRel> list = wlTenantInsuranceRelService.getByTenantId(tenantId);
        return Result.OK(list);
    }

    /**
     * 根据保险公司ID查询租户关系列表
     * @param insuranceCompanyId
     * @return
     */
    @AutoLog(value = "租户保险公司关系表-根据保险公司ID查询")
    @ApiOperation(value="租户保险公司关系表-根据保险公司ID查询", notes="租户保险公司关系表-根据保险公司ID查询")
    @GetMapping(value = "/listByInsuranceCompanyId")
    public Result<List<WlTenantInsuranceRel>> queryByInsuranceCompanyId(@RequestParam(name="insuranceCompanyId") String insuranceCompanyId) {
        List<WlTenantInsuranceRel> list = wlTenantInsuranceRelService.getByInsuranceCompanyId(insuranceCompanyId);
        return Result.OK(list);
    }

    /**
     * 添加租户保险公司关系
     * @param tenantId
     * @param insuranceCompanyIds
     * @return
     */
    @AutoLog(value = "租户保险公司关系表-添加关系")
    @ApiOperation(value="租户保险公司关系表-添加关系", notes="租户保险公司关系表-添加关系")
    @PostMapping(value = "/addRelation")
    public Result<String> addRelation(@RequestParam(name="tenantId") String tenantId,
                                      @RequestParam(name="insuranceCompanyIds") String insuranceCompanyIds) {
        try {
            List<String> idList = Arrays.asList(insuranceCompanyIds.split(","));
            boolean success = wlTenantInsuranceRelService.addTenantInsuranceRel(tenantId, idList);
            if (success) {
                return Result.OK("添加成功！");
            } else {
                return Result.error("添加失败！");
            }
        } catch (Exception e) {
            log.error("添加租户保险公司关系失败", e);
            return Result.error("添加失败：" + e.getMessage());
        }
    }

    /**
     * 移除租户保险公司关系
     * @param tenantId
     * @param insuranceCompanyIds
     * @return
     */
    @AutoLog(value = "租户保险公司关系表-移除关系")
    @ApiOperation(value="租户保险公司关系表-移除关系", notes="租户保险公司关系表-移除关系")
    @DeleteMapping(value = "/removeRelation")
    public Result<String> removeRelation(@RequestParam(name="tenantId") String tenantId,
                                         @RequestParam(name="insuranceCompanyIds") String insuranceCompanyIds) {
        try {
            List<String> idList = Arrays.asList(insuranceCompanyIds.split(","));
            boolean success = wlTenantInsuranceRelService.removeTenantInsuranceRel(tenantId, idList);
            if (success) {
                return Result.OK("移除成功！");
            } else {
                return Result.error("移除失败！");
            }
        } catch (Exception e) {
            log.error("移除租户保险公司关系失败", e);
            return Result.error("移除失败：" + e.getMessage());
        }
    }

    /**
     * 通过id删除
     * @param id
     * @return
     */
    @AutoLog(value = "租户保险公司关系表-通过id删除")
    @ApiOperation(value="租户保险公司关系表-通过id删除", notes="租户保险公司关系表-通过id删除")
    @DeleteMapping(value = "/delete")
    public Result<String> delete(@RequestParam(name="id") String id) {
        try {
            wlTenantInsuranceRelService.removeById(id);
            return Result.OK("删除成功!");
        } catch (Exception e) {
            log.error("删除失败", e);
            return Result.error("删除失败!");
        }
    }

    /**
     * 批量删除
     * @param ids
     * @return
     */
    @AutoLog(value = "租户保险公司关系表-批量删除")
    @ApiOperation(value="租户保险公司关系表-批量删除", notes="租户保险公司关系表-批量删除")
    @DeleteMapping(value = "/deleteBatch")
    public Result<String> deleteBatch(@RequestParam(name="ids") String ids) {
        try {
            List<String> idList = Arrays.asList(ids.split(","));
            boolean success = wlTenantInsuranceRelService.removeBatchByIds(idList);
            if (success) {
                return Result.OK("批量删除成功!");
            } else {
                return Result.error("批量删除失败!");
            }
        } catch (Exception e) {
            log.error("批量删除失败", e);
            return Result.error("批量删除失败!");
        }
    }

    /**
     * 通过id查询
     * @param id
     * @return
     */
    @AutoLog(value = "租户保险公司关系表-通过id查询")
    @ApiOperation(value="租户保险公司关系表-通过id查询", notes="租户保险公司关系表-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<WlTenantInsuranceRel> queryById(@RequestParam(name="id") String id) {
        WlTenantInsuranceRel wlTenantInsuranceRel = wlTenantInsuranceRelService.getById(id);
        if (wlTenantInsuranceRel == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(wlTenantInsuranceRel);
    }
}
