package org.jeecg.modules.system.mapper;

import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.system.vo.TenantLogisticsStaffVO;

import java.util.List;

/**
 * @Description: 租户后勤人员配置Mapper接口
 * @Author: jeecg-boot
 * @Date: 2024-01-01
 * @Version: V1.0
 */
public interface TenantLogisticsMapper {

    /**
     * 根据租户、保险公司、地区查询后勤人员列表
     * @param tenantId 租户ID
     * @param insuranceCompanyId 保险公司ID
     * @param regionCode 地区编码
     * @return 后勤人员列表
     */
    List<TenantLogisticsStaffVO> selectLogisticsStaffList(@Param("tenantId") String tenantId,
                                                          @Param("insuranceCompanyId") String insuranceCompanyId,
                                                          @Param("regionCode") String regionCode);

    /**
     * 根据租户ID查询所有后勤人员配置
     * @param tenantId 租户ID
     * @return 后勤人员列表
     */
    List<TenantLogisticsStaffVO> selectLogisticsStaffByTenant(@Param("tenantId") String tenantId);

    /**
     * 根据用户ID查询后勤人员配置
     * @param userId 用户ID
     * @return 后勤人员配置列表
     */
    List<TenantLogisticsStaffVO> selectLogisticsStaffByUser(@Param("userId") String userId);

    /**
     * 根据保险公司ID查询后勤人员配置
     * @param insuranceCompanyId 保险公司ID
     * @return 后勤人员配置列表
     */
    List<TenantLogisticsStaffVO> selectLogisticsStaffByInsuranceCompany(@Param("insuranceCompanyId") String insuranceCompanyId);

    /**
     * 根据地区编码查询后勤人员配置
     * @param regionCode 地区编码
     * @return 后勤人员配置列表
     */
    List<TenantLogisticsStaffVO> selectLogisticsStaffByRegion(@Param("regionCode") String regionCode);

    /**
     * 根据租户和保险公司删除所有后勤人员配置
     * @param tenantId 租户ID
     * @param insuranceCompanyId 保险公司ID
     * @return 删除数量
     */
    int deleteByTenantAndInsuranceCompany(@Param("tenantId") String tenantId, @Param("insuranceCompanyId") String insuranceCompanyId);

    /**
     * 根据租户和地区删除所有后勤人员配置
     * @param tenantId 租户ID
     * @param regionCode 地区编码
     * @return 删除数量
     */
    int deleteByTenantAndRegion(@Param("tenantId") String tenantId, @Param("regionCode") String regionCode);
}
