package org.jeecg.modules.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Delete;
import org.jeecg.modules.system.entity.WlRegionUserRel;

import java.util.List;

/**
 * @Description: 地区用户关系表Mapper接口
 * @Author: jeecg-boot
 * @Date: 2024-01-01
 * @Version: V1.0
 */
public interface WlRegionUserRelMapper extends BaseMapper<WlRegionUserRel> {

    /**
     * 根据租户ID查询后勤人员关系列表
     * @param tenantId 租户ID
     * @return 关系列表
     */
    @Select("SELECT * FROM wl_region_user_rel WHERE tenant_id = #{tenantId} ORDER BY create_time DESC")
    List<WlRegionUserRel> selectByTenantId(@Param("tenantId") String tenantId);

    /**
     * 根据租户ID和地区编码查询后勤人员关系列表
     * @param tenantId 租户ID
     * @param regionCode 地区编码
     * @return 关系列表
     */
    @Select("SELECT * FROM wl_region_user_rel WHERE tenant_id = #{tenantId} AND region_code = #{regionCode} ORDER BY create_time DESC")
    List<WlRegionUserRel> selectByTenantIdAndRegionCode(@Param("tenantId") String tenantId, @Param("regionCode") String regionCode);

    /**
     * 根据租户ID、保险公司ID和地区编码查询后勤人员关系列表
     * @param tenantId 租户ID
     * @param insuranceCompanyId 保险公司ID
     * @param regionCode 地区编码
     * @return 关系列表
     */
    @Select("SELECT * FROM wl_region_user_rel WHERE tenant_id = #{tenantId} AND insurance_company_id = #{insuranceCompanyId} AND region_code = #{regionCode} ORDER BY create_time DESC")
    List<WlRegionUserRel> selectByTenantInsuranceRegion(@Param("tenantId") String tenantId, 
                                                        @Param("insuranceCompanyId") String insuranceCompanyId, 
                                                        @Param("regionCode") String regionCode);

    /**
     * 根据用户ID查询后勤人员关系列表
     * @param userId 用户ID
     * @return 关系列表
     */
    @Select("SELECT * FROM wl_region_user_rel WHERE user_id = #{userId} ORDER BY create_time DESC")
    List<WlRegionUserRel> selectByUserId(@Param("userId") String userId);

    /**
     * 检查后勤人员关系是否存在
     * @param tenantId 租户ID
     * @param insuranceCompanyId 保险公司ID
     * @param regionCode 地区编码
     * @param userId 用户ID
     * @return 关系记录
     */
    @Select("SELECT * FROM wl_region_user_rel WHERE tenant_id = #{tenantId} AND insurance_company_id = #{insuranceCompanyId} AND region_code = #{regionCode} AND user_id = #{userId}")
    WlRegionUserRel selectByTenantInsuranceRegionUser(@Param("tenantId") String tenantId, 
                                                      @Param("insuranceCompanyId") String insuranceCompanyId, 
                                                      @Param("regionCode") String regionCode, 
                                                      @Param("userId") String userId);

    /**
     * 根据租户ID删除所有关系
     * @param tenantId 租户ID
     * @return 删除数量
     */
    @Delete("DELETE FROM wl_region_user_rel WHERE tenant_id = #{tenantId}")
    int deleteByTenantId(@Param("tenantId") String tenantId);

    /**
     * 根据用户ID删除所有关系
     * @param userId 用户ID
     * @return 删除数量
     */
    @Delete("DELETE FROM wl_region_user_rel WHERE user_id = #{userId}")
    int deleteByUserId(@Param("userId") String userId);

    /**
     * 根据租户ID和地区编码删除关系
     * @param tenantId 租户ID
     * @param regionCode 地区编码
     * @return 删除数量
     */
    @Delete("DELETE FROM wl_region_user_rel WHERE tenant_id = #{tenantId} AND region_code = #{regionCode}")
    int deleteByTenantIdAndRegionCode(@Param("tenantId") String tenantId, @Param("regionCode") String regionCode);

    /**
     * 根据租户ID、保险公司ID和地区编码删除关系
     * @param tenantId 租户ID
     * @param insuranceCompanyId 保险公司ID
     * @param regionCode 地区编码
     * @return 删除数量
     */
    @Delete("DELETE FROM wl_region_user_rel WHERE tenant_id = #{tenantId} AND insurance_company_id = #{insuranceCompanyId} AND region_code = #{regionCode}")
    int deleteByTenantInsuranceRegion(@Param("tenantId") String tenantId, 
                                      @Param("insuranceCompanyId") String insuranceCompanyId, 
                                      @Param("regionCode") String regionCode);

    /**
     * 批量删除关系
     * @param ids 关系ID列表
     * @return 删除数量
     */
    @Delete("<script>" +
            "DELETE FROM wl_region_user_rel WHERE id IN " +
            "<foreach collection='ids' item='id' open='(' separator=',' close=')'>" +
            "#{id}" +
            "</foreach>" +
            "</script>")
    int deleteBatchByIds(@Param("ids") List<String> ids);
}
