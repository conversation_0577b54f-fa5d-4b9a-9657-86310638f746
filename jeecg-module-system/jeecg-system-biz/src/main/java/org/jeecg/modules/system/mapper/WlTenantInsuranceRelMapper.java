package org.jeecg.modules.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Delete;
import org.jeecg.modules.system.entity.WlTenantInsuranceRel;

import java.util.List;

/**
 * @Description: 租户保险公司关系表Mapper接口
 * @Author: jeecg-boot
 * @Date: 2024-01-01
 * @Version: V1.0
 */
public interface WlTenantInsuranceRelMapper extends BaseMapper<WlTenantInsuranceRel> {

    /**
     * 根据租户ID查询保险公司关系列表
     * @param tenantId 租户ID
     * @return 关系列表
     */
    @Select("SELECT * FROM wl_tenant_insurance_rel WHERE tenant_id = #{tenantId} ORDER BY create_time DESC")
    List<WlTenantInsuranceRel> selectByTenantId(@Param("tenantId") String tenantId);

    /**
     * 根据保险公司ID查询租户关系列表
     * @param insuranceCompanyId 保险公司ID
     * @return 关系列表
     */
    @Select("SELECT * FROM wl_tenant_insurance_rel WHERE insurance_company_id = #{insuranceCompanyId} ORDER BY create_time DESC")
    List<WlTenantInsuranceRel> selectByInsuranceCompanyId(@Param("insuranceCompanyId") String insuranceCompanyId);

    /**
     * 检查租户和保险公司关系是否存在
     * @param tenantId 租户ID
     * @param insuranceCompanyId 保险公司ID
     * @return 关系记录
     */
    @Select("SELECT * FROM wl_tenant_insurance_rel WHERE tenant_id = #{tenantId} AND insurance_company_id = #{insuranceCompanyId}")
    WlTenantInsuranceRel selectByTenantAndInsurance(@Param("tenantId") String tenantId, @Param("insuranceCompanyId") String insuranceCompanyId);

    /**
     * 根据租户ID删除所有关系
     * @param tenantId 租户ID
     * @return 删除数量
     */
    @Delete("DELETE FROM wl_tenant_insurance_rel WHERE tenant_id = #{tenantId}")
    int deleteByTenantId(@Param("tenantId") String tenantId);

    /**
     * 根据保险公司ID删除所有关系
     * @param insuranceCompanyId 保险公司ID
     * @return 删除数量
     */
    @Delete("DELETE FROM wl_tenant_insurance_rel WHERE insurance_company_id = #{insuranceCompanyId}")
    int deleteByInsuranceCompanyId(@Param("insuranceCompanyId") String insuranceCompanyId);

    /**
     * 批量删除关系
     * @param ids 关系ID列表
     * @return 删除数量
     */
    @Delete("<script>" +
            "DELETE FROM wl_tenant_insurance_rel WHERE id IN " +
            "<foreach collection='ids' item='id' open='(' separator=',' close=')'>" +
            "#{id}" +
            "</foreach>" +
            "</script>")
    int deleteBatchByIds(@Param("ids") List<String> ids);
}
