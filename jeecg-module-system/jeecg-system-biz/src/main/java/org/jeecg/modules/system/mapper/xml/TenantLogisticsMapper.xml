<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.system.mapper.TenantLogisticsMapper">

    <!-- 租户后勤人员配置VO结果映射 -->
    <resultMap id="TenantLogisticsStaffVO" type="org.jeecg.modules.system.vo.TenantLogisticsStaffVO">
        <result column="id" property="id" jdbcType="VARCHAR"/>
        <result column="tenant_id" property="tenantId" jdbcType="VARCHAR"/>
        <result column="tenant_name" property="tenantName" jdbcType="VARCHAR"/>
        <result column="insurance_company_id" property="insuranceCompanyId" jdbcType="VARCHAR"/>
        <result column="insurance_company_name" property="insuranceCompanyName" jdbcType="VARCHAR"/>
        <result column="insurance_company_icon" property="insuranceCompanyIcon" jdbcType="VARCHAR"/>
        <result column="region_code" property="regionCode" jdbcType="VARCHAR"/>
        <result column="region_name" property="regionName" jdbcType="VARCHAR"/>
        <result column="user_id" property="userId" jdbcType="VARCHAR"/>
        <result column="username" property="username" jdbcType="VARCHAR"/>
        <result column="realname" property="realname" jdbcType="VARCHAR"/>
        <result column="avatar" property="avatar" jdbcType="VARCHAR"/>
        <result column="phone" property="phone" jdbcType="VARCHAR"/>
        <result column="email" property="email" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
        <result column="org_code" property="orgCode" jdbcType="VARCHAR"/>
        <result column="org_code_txt" property="orgCodeTxt" jdbcType="VARCHAR"/>
        <result column="wechat_qrcode" property="wechatQrcode" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
    </resultMap>


    <!-- 根据租户、保险公司、地区查询后勤人员列表 -->
    <select id="selectLogisticsStaffList" resultMap="TenantLogisticsStaffVO">
        SELECT
            r.id,
            r.tenant_id,
            t.name as tenant_name,
            r.insurance_company_id,
            ic.name as insurance_company_name,
            ic.icon_img as insurance_company_icon,
            r.region_code,
            reg.name as region_name,
            r.user_id,
            u.username,
            u.realname,
            u.avatar,
            u.phone,
            u.email,
            u.status,
            u.org_code,
            d.depart_name as org_code_txt,
            u.wechat_qrcode,
            r.create_time
        FROM wl_region_user_rel r
        LEFT JOIN sys_tenant t ON r.tenant_id = t.id
        LEFT JOIN pd_company ic ON r.insurance_company_id = ic.id
        LEFT JOIN sys_user u ON r.user_id = u.id
        LEFT JOIN sys_depart d ON u.org_code = d.org_code
        LEFT JOIN ea_region reg ON r.region_code COLLATE utf8mb4_general_ci = reg.code
        WHERE r.tenant_id = #{tenantId}
        AND r.insurance_company_id = #{insuranceCompanyId}
        AND r.region_code = #{regionCode}
        ORDER BY r.create_time DESC
    </select>

    <!-- 根据租户ID查询所有后勤人员配置 -->
    <select id="selectLogisticsStaffByTenant" resultMap="TenantLogisticsStaffVO">
        SELECT
            r.id,
            r.tenant_id,
            t.name as tenant_name,
            r.insurance_company_id,
            ic.name as insurance_company_name,
            ic.icon_img as insurance_company_icon,
            r.region_code,
            reg.name as region_name,
            r.user_id,
            u.username,
            u.realname,
            u.avatar,
            u.phone,
            u.email,
            u.status,
            u.org_code,
            d.depart_name as org_code_txt,
            u.wechat_qrcode,
            r.create_time
        FROM wl_region_user_rel r
        LEFT JOIN sys_tenant t ON r.tenant_id = t.id
        LEFT JOIN pd_company ic ON r.insurance_company_id = ic.id
        LEFT JOIN sys_user u ON r.user_id = u.id
        LEFT JOIN sys_depart d ON u.org_code = d.org_code
        LEFT JOIN ea_region reg ON r.region_code COLLATE utf8mb4_general_ci = reg.code
        WHERE r.tenant_id = #{tenantId}
        ORDER BY r.create_time DESC
    </select>

    <!-- 根据用户ID查询后勤人员配置 -->
    <select id="selectLogisticsStaffByUser" resultMap="TenantLogisticsStaffVO">
        SELECT
            r.id,
            r.tenant_id,
            t.name as tenant_name,
            r.insurance_company_id,
            ic.name as insurance_company_name,
            ic.icon_img as insurance_company_icon,
            r.region_code,
            reg.name as region_name,
            r.user_id,
            u.username,
            u.realname,
            u.avatar,
            u.phone,
            u.email,
            u.status,
            u.org_code,
            d.depart_name as org_code_txt,
            u.wechat_qrcode,
            r.create_time
        FROM wl_region_user_rel r
        LEFT JOIN sys_tenant t ON r.tenant_id = t.id
        LEFT JOIN pd_company ic ON r.insurance_company_id = ic.id
        LEFT JOIN sys_user u ON r.user_id = u.id
        LEFT JOIN sys_depart d ON u.org_code = d.org_code
        LEFT JOIN ea_region reg ON r.region_code COLLATE utf8mb4_general_ci = reg.code
        WHERE r.user_id = #{userId}
        ORDER BY r.create_time DESC
    </select>

    <!-- 根据保险公司ID查询后勤人员配置 -->
    <select id="selectLogisticsStaffByInsuranceCompany" resultMap="TenantLogisticsStaffVO">
        SELECT
            r.id,
            r.tenant_id,
            t.name as tenant_name,
            r.insurance_company_id,
            ic.name as insurance_company_name,
            ic.icon_img as insurance_company_icon,
            r.region_code,
            reg.name as region_name,
            r.user_id,
            u.username,
            u.realname,
            u.avatar,
            u.phone,
            u.email,
            u.status,
            u.org_code,
            d.depart_name as org_code_txt,
            u.wechat_qrcode,
            r.create_time
        FROM wl_region_user_rel r
        LEFT JOIN sys_tenant t ON r.tenant_id = t.id
        LEFT JOIN pd_company ic ON r.insurance_company_id = ic.id
        LEFT JOIN sys_user u ON r.user_id = u.id
        LEFT JOIN sys_depart d ON u.org_code = d.org_code
        LEFT JOIN ea_region reg ON r.region_code COLLATE utf8mb4_general_ci = reg.code
        WHERE r.insurance_company_id = #{insuranceCompanyId}
        ORDER BY r.create_time DESC
    </select>

    <!-- 根据地区编码查询后勤人员配置 -->
    <select id="selectLogisticsStaffByRegion" resultMap="TenantLogisticsStaffVO">
        SELECT
            r.id,
            r.tenant_id,
            t.name as tenant_name,
            r.insurance_company_id,
            ic.name as insurance_company_name,
            ic.icon_img as insurance_company_icon,
            r.region_code,
            reg.name as region_name,
            r.user_id,
            u.username,
            u.realname,
            u.avatar,
            u.phone,
            u.email,
            u.status,
            u.org_code,
            d.depart_name as org_code_txt,
            u.wechat_qrcode,
            r.create_time
        FROM wl_region_user_rel r
        LEFT JOIN sys_tenant t ON r.tenant_id = t.id
        LEFT JOIN pd_company ic ON r.insurance_company_id = ic.id
        LEFT JOIN sys_user u ON r.user_id = u.id
        LEFT JOIN sys_depart d ON u.org_code = d.org_code
        LEFT JOIN ea_region reg ON r.region_code COLLATE utf8mb4_general_ci = reg.code
        WHERE r.region_code = #{regionCode}
        ORDER BY r.create_time DESC
    </select>

    <!-- 根据租户和保险公司删除所有后勤人员配置 -->
    <delete id="deleteByTenantAndInsuranceCompany">
        DELETE FROM wl_region_user_rel
        WHERE tenant_id = #{tenantId}
        AND insurance_company_id = #{insuranceCompanyId}
    </delete>

    <!-- 根据租户和地区删除所有后勤人员配置 -->
    <delete id="deleteByTenantAndRegion">
        DELETE FROM wl_region_user_rel
        WHERE tenant_id = #{tenantId}
        AND region_code = #{regionCode}
    </delete>
</mapper>