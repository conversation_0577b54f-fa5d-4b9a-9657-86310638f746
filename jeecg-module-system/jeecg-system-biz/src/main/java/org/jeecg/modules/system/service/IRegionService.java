package org.jeecg.modules.system.service;

import java.util.Map;
import java.util.List;

/**
 * @Description: 地区服务接口
 * @Author: jeecg-boot
 * @Date: 2024-01-01
 * @Version: V1.0
 */
public interface IRegionService {

    /**
     * 根据地区编码获取地区名称
     * @param regionCode 地区编码
     * @return 地区名称
     */
    String getRegionNameByCode(String regionCode);

    /**
     * 批量获取地区名称
     * @param regionCodes 地区编码列表
     * @return 地区编码和名称的映射
     */
    Map<String, String> getRegionNamesByCode(List<String> regionCodes);

    /**
     * 获取地区树数据
     * @return 地区树数据
     */
    Object getRegionTree();
}
