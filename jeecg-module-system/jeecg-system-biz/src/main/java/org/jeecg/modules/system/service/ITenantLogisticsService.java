package org.jeecg.modules.system.service;

import org.jeecg.modules.system.vo.TenantLogisticsStaffVO;

import java.util.List;

/**
 * @Description: 租户后勤人员配置Service接口
 * @Author: jeecg-boot
 * @Date: 2024-01-01
 * @Version: V1.0
 */
public interface ITenantLogisticsService {

    /**
     * 根据租户、保险公司、地区查询后勤人员列表
     * @param tenantId 租户ID
     * @param insuranceCompanyId 保险公司ID
     * @param regionCode 地区编码
     * @return 后勤人员列表
     */
    List<TenantLogisticsStaffVO> getLogisticsStaffList(String tenantId, String insuranceCompanyId, String regionCode);

    /**
     * 添加后勤人员关系
     * @param tenantId 租户ID
     * @param insuranceCompanyId 保险公司ID
     * @param regionCode 地区编码
     * @param userIds 用户ID列表
     * @return 是否成功
     */
    boolean addLogisticsStaff(String tenantId, String insuranceCompanyId, String regionCode, List<String> userIds);

    /**
     * 批量添加后勤人员关系
     * @param tenantId 租户ID
     * @param insuranceCompanyId 保险公司ID
     * @param batchData 批量数据，格式：userId1:regionCode1,regionCode2;userId2:regionCode3
     * @return 是否成功
     */
    boolean batchAddLogisticsStaff(String tenantId, String insuranceCompanyId, String batchData);

    /**
     * 移除后勤人员关系
     * @param tenantId 租户ID
     * @param insuranceCompanyId 保险公司ID
     * @param regionCode 地区编码
     * @param userIds 用户ID列表
     * @return 是否成功
     */
    boolean removeLogisticsStaff(String tenantId, String insuranceCompanyId, String regionCode, List<String> userIds);

    /**
     * 根据租户ID查询所有后勤人员配置
     * @param tenantId 租户ID
     * @return 后勤人员列表
     */
    List<TenantLogisticsStaffVO> getLogisticsStaffByTenant(String tenantId);

    /**
     * 根据用户ID查询后勤人员配置
     * @param userId 用户ID
     * @return 后勤人员配置列表
     */
    List<TenantLogisticsStaffVO> getLogisticsStaffByUser(String userId);

    /**
     * 根据保险公司ID查询后勤人员配置
     * @param insuranceCompanyId 保险公司ID
     * @return 后勤人员配置列表
     */
    List<TenantLogisticsStaffVO> getLogisticsStaffByInsuranceCompany(String insuranceCompanyId);

    /**
     * 根据地区编码查询后勤人员配置
     * @param regionCode 地区编码
     * @return 后勤人员配置列表
     */
    List<TenantLogisticsStaffVO> getLogisticsStaffByRegion(String regionCode);

    /**
     * 检查用户是否为指定租户、保险公司、地区的后勤人员
     * @param tenantId 租户ID
     * @param insuranceCompanyId 保险公司ID
     * @param regionCode 地区编码
     * @param userId 用户ID
     * @return 是否为后勤人员
     */
    boolean isLogisticsStaff(String tenantId, String insuranceCompanyId, String regionCode, String userId);

    /**
     * 根据租户和保险公司删除所有后勤人员配置
     * @param tenantId 租户ID
     * @param insuranceCompanyId 保险公司ID
     * @return 是否成功
     */
    boolean removeByTenantAndInsuranceCompany(String tenantId, String insuranceCompanyId);

    /**
     * 根据租户和地区删除所有后勤人员配置
     * @param tenantId 租户ID
     * @param regionCode 地区编码
     * @return 是否成功
     */
    boolean removeByTenantAndRegion(String tenantId, String regionCode);
}
