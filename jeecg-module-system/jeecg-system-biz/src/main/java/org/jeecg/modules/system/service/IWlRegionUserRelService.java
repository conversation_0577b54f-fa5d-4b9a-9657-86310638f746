package org.jeecg.modules.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.system.entity.WlRegionUserRel;

import java.util.List;

/**
 * @Description: 地区用户关系表Service接口
 * @Author: jeecg-boot
 * @Date: 2024-01-01
 * @Version: V1.0
 */
public interface IWlRegionUserRelService extends IService<WlRegionUserRel> {

    /**
     * 根据租户ID查询后勤人员关系列表
     * @param tenantId 租户ID
     * @return 关系列表
     */
    List<WlRegionUserRel> getByTenantId(String tenantId);

    /**
     * 根据租户ID和地区编码查询后勤人员关系列表
     * @param tenantId 租户ID
     * @param regionCode 地区编码
     * @return 关系列表
     */
    List<WlRegionUserRel> getByTenantIdAndRegionCode(String tenantId, String regionCode);

    /**
     * 根据租户ID、保险公司ID和地区编码查询后勤人员关系列表
     * @param tenantId 租户ID
     * @param insuranceCompanyId 保险公司ID
     * @param regionCode 地区编码
     * @return 关系列表
     */
    List<WlRegionUserRel> getByTenantInsuranceRegion(String tenantId, String insuranceCompanyId, String regionCode);

    /**
     * 根据用户ID查询后勤人员关系列表
     * @param userId 用户ID
     * @return 关系列表
     */
    List<WlRegionUserRel> getByUserId(String userId);

    /**
     * 添加后勤人员关系
     * @param tenantId 租户ID
     * @param insuranceCompanyId 保险公司ID
     * @param regionCode 地区编码
     * @param userIds 用户ID列表
     * @return 是否成功
     */
    boolean addRegionUserRel(String tenantId, String insuranceCompanyId, String regionCode, List<String> userIds);

    /**
     * 移除后勤人员关系
     * @param tenantId 租户ID
     * @param insuranceCompanyId 保险公司ID
     * @param regionCode 地区编码
     * @param userIds 用户ID列表
     * @return 是否成功
     */
    boolean removeRegionUserRel(String tenantId, String insuranceCompanyId, String regionCode, List<String> userIds);

    /**
     * 检查后勤人员关系是否存在
     * @param tenantId 租户ID
     * @param insuranceCompanyId 保险公司ID
     * @param regionCode 地区编码
     * @param userId 用户ID
     * @return 是否存在
     */
    boolean existsRelation(String tenantId, String insuranceCompanyId, String regionCode, String userId);

    /**
     * 根据租户ID删除所有关系
     * @param tenantId 租户ID
     * @return 是否成功
     */
    boolean removeByTenantId(String tenantId);

    /**
     * 根据用户ID删除所有关系
     * @param userId 用户ID
     * @return 是否成功
     */
    boolean removeByUserId(String userId);

    /**
     * 根据租户ID和地区编码删除关系
     * @param tenantId 租户ID
     * @param regionCode 地区编码
     * @return 是否成功
     */
    boolean removeByTenantIdAndRegionCode(String tenantId, String regionCode);

    /**
     * 根据租户ID、保险公司ID和地区编码删除关系
     * @param tenantId 租户ID
     * @param insuranceCompanyId 保险公司ID
     * @param regionCode 地区编码
     * @return 是否成功
     */
    boolean removeByTenantInsuranceRegion(String tenantId, String insuranceCompanyId, String regionCode);

    /**
     * 批量删除关系
     * @param ids 关系ID列表
     * @return 是否成功
     */
    boolean removeBatchByIds(List<String> ids);
}
