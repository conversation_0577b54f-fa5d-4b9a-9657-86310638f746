package org.jeecg.modules.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.system.entity.WlTenantInsuranceRel;

import java.util.List;

/**
 * @Description: 租户保险公司关系表Service接口
 * @Author: jeecg-boot
 * @Date: 2024-01-01
 * @Version: V1.0
 */
public interface IWlTenantInsuranceRelService extends IService<WlTenantInsuranceRel> {

    /**
     * 根据租户ID查询保险公司关系列表
     * @param tenantId 租户ID
     * @return 关系列表
     */
    List<WlTenantInsuranceRel> getByTenantId(String tenantId);

    /**
     * 根据保险公司ID查询租户关系列表
     * @param insuranceCompanyId 保险公司ID
     * @return 关系列表
     */
    List<WlTenantInsuranceRel> getByInsuranceCompanyId(String insuranceCompanyId);

    /**
     * 添加租户保险公司关系
     * @param tenantId 租户ID
     * @param insuranceCompanyIds 保险公司ID列表
     * @return 是否成功
     */
    boolean addTenantInsuranceRel(String tenantId, List<String> insuranceCompanyIds);

    /**
     * 移除租户保险公司关系
     * @param tenantId 租户ID
     * @param insuranceCompanyIds 保险公司ID列表
     * @return 是否成功
     */
    boolean removeTenantInsuranceRel(String tenantId, List<String> insuranceCompanyIds);

    /**
     * 检查租户和保险公司关系是否存在
     * @param tenantId 租户ID
     * @param insuranceCompanyId 保险公司ID
     * @return 是否存在
     */
    boolean existsRelation(String tenantId, String insuranceCompanyId);

    /**
     * 根据租户ID删除所有关系
     * @param tenantId 租户ID
     * @return 是否成功
     */
    boolean removeByTenantId(String tenantId);

    /**
     * 根据保险公司ID删除所有关系
     * @param insuranceCompanyId 保险公司ID
     * @return 是否成功
     */
    boolean removeByInsuranceCompanyId(String insuranceCompanyId);

    /**
     * 批量删除关系
     * @param ids 关系ID列表
     * @return 是否成功
     */
    boolean removeBatchByIds(List<String> ids);
}
