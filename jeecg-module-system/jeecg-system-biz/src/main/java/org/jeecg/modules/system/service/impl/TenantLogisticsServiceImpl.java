package org.jeecg.modules.system.service.impl;

import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.system.entity.WlRegionUserRel;
import org.jeecg.modules.system.mapper.TenantLogisticsMapper;
import org.jeecg.modules.system.service.ITenantLogisticsService;
import org.jeecg.modules.system.service.IWlRegionUserRelService;
import org.jeecg.modules.system.vo.TenantLogisticsStaffVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * @Description: 租户后勤人员配置Service实现
 * @Author: jeecg-boot
 * @Date: 2024-01-01
 * @Version: V1.0
 */
@Service
@Slf4j
public class TenantLogisticsServiceImpl implements ITenantLogisticsService {

    @Autowired
    private TenantLogisticsMapper tenantLogisticsMapper;

    @Autowired
    private IWlRegionUserRelService wlRegionUserRelService;

    @Override
    public List<TenantLogisticsStaffVO> getLogisticsStaffList(String tenantId, String insuranceCompanyId, String regionCode) {
        return tenantLogisticsMapper.selectLogisticsStaffList(tenantId, insuranceCompanyId, regionCode);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean addLogisticsStaff(String tenantId, String insuranceCompanyId, String regionCode, List<String> userIds) {
        try {
            Date now = new Date();
            for (String userId : userIds) {
                // 检查关系是否已存在
                if (!wlRegionUserRelService.existsRelation(tenantId, insuranceCompanyId, regionCode, userId)) {
                    WlRegionUserRel rel = new WlRegionUserRel();
                    rel.setTenantId(tenantId);
                    rel.setInsuranceCompanyId(insuranceCompanyId);
                    rel.setRegionCode(regionCode);
                    rel.setUserId(userId);
                    rel.setCreateTime(now);
                    wlRegionUserRelService.save(rel);
                }
            }
            return true;
        } catch (Exception e) {
            log.error("添加后勤人员关系失败", e);
            throw new RuntimeException("添加后勤人员关系失败", e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchAddLogisticsStaff(String tenantId, String insuranceCompanyId, String batchData) {
        try {
            Date now = new Date();
            // 解析批量数据：userId1:regionCode1,regionCode2;userId2:regionCode3
            String[] userDataArray = batchData.split(";");

            for (String userData : userDataArray) {
                String[] parts = userData.split(":");
                if (parts.length != 2) {
                    continue;
                }

                String userId = parts[0];
                String[] regionCodes = parts[1].split(",");

                for (String regionCode : regionCodes) {
                    // 检查关系是否已存在
                    if (!wlRegionUserRelService.existsRelation(tenantId, insuranceCompanyId, regionCode, userId)) {
                        WlRegionUserRel rel = new WlRegionUserRel();
                        rel.setTenantId(tenantId);
                        rel.setInsuranceCompanyId(insuranceCompanyId);
                        rel.setRegionCode(regionCode);
                        rel.setUserId(userId);
                        rel.setCreateTime(now);
                        wlRegionUserRelService.save(rel);
                    }
                }
            }
            return true;
        } catch (Exception e) {
            log.error("批量添加后勤人员关系失败", e);
            throw new RuntimeException("批量添加后勤人员关系失败", e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeLogisticsStaff(String tenantId, String insuranceCompanyId, String regionCode, List<String> userIds) {
        try {
            return wlRegionUserRelService.removeRegionUserRel(tenantId, insuranceCompanyId, regionCode, userIds);
        } catch (Exception e) {
            log.error("移除后勤人员关系失败", e);
            throw new RuntimeException("移除后勤人员关系失败", e);
        }
    }

    @Override
    public List<TenantLogisticsStaffVO> getLogisticsStaffByTenant(String tenantId) {
        return tenantLogisticsMapper.selectLogisticsStaffByTenant(tenantId);
    }

    @Override
    public List<TenantLogisticsStaffVO> getLogisticsStaffByUser(String userId) {
        return tenantLogisticsMapper.selectLogisticsStaffByUser(userId);
    }

    @Override
    public List<TenantLogisticsStaffVO> getLogisticsStaffByInsuranceCompany(String insuranceCompanyId) {
        return tenantLogisticsMapper.selectLogisticsStaffByInsuranceCompany(insuranceCompanyId);
    }

    @Override
    public List<TenantLogisticsStaffVO> getLogisticsStaffByRegion(String regionCode) {
        return tenantLogisticsMapper.selectLogisticsStaffByRegion(regionCode);
    }

    @Override
    public boolean isLogisticsStaff(String tenantId, String insuranceCompanyId, String regionCode, String userId) {
        return wlRegionUserRelService.existsRelation(tenantId, insuranceCompanyId, regionCode, userId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeByTenantAndInsuranceCompany(String tenantId, String insuranceCompanyId) {
        try {
            int count = tenantLogisticsMapper.deleteByTenantAndInsuranceCompany(tenantId, insuranceCompanyId);
            log.info("删除租户{}保险公司{}的后勤人员关系{}条", tenantId, insuranceCompanyId, count);
            return true;
        } catch (Exception e) {
            log.error("根据租户和保险公司删除后勤人员关系失败", e);
            throw new RuntimeException("根据租户和保险公司删除后勤人员关系失败", e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeByTenantAndRegion(String tenantId, String regionCode) {
        try {
            int count = tenantLogisticsMapper.deleteByTenantAndRegion(tenantId, regionCode);
            log.info("删除租户{}地区{}的后勤人员关系{}条", tenantId, regionCode, count);
            return true;
        } catch (Exception e) {
            log.error("根据租户和地区删除后勤人员关系失败", e);
            throw new RuntimeException("根据租户和地区删除后勤人员关系失败", e);
        }
    }
}
