package org.jeecg.modules.system.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.system.entity.WlRegionUserRel;
import org.jeecg.modules.system.mapper.WlRegionUserRelMapper;
import org.jeecg.modules.system.service.IWlRegionUserRelService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @Description: 地区用户关系表Service实现
 * @Author: jeecg-boot
 * @Date: 2024-01-01
 * @Version: V1.0
 */
@Service
@Slf4j
public class WlRegionUserRelServiceImpl extends ServiceImpl<WlRegionUserRelMapper, WlRegionUserRel> implements IWlRegionUserRelService {

    @Autowired
    private WlRegionUserRelMapper wlRegionUserRelMapper;

    @Override
    public List<WlRegionUserRel> getByTenantId(String tenantId) {
        return wlRegionUserRelMapper.selectByTenantId(tenantId);
    }

    @Override
    public List<WlRegionUserRel> getByTenantIdAndRegionCode(String tenantId, String regionCode) {
        return wlRegionUserRelMapper.selectByTenantIdAndRegionCode(tenantId, regionCode);
    }

    @Override
    public List<WlRegionUserRel> getByTenantInsuranceRegion(String tenantId, String insuranceCompanyId, String regionCode) {
        return wlRegionUserRelMapper.selectByTenantInsuranceRegion(tenantId, insuranceCompanyId, regionCode);
    }

    @Override
    public List<WlRegionUserRel> getByUserId(String userId) {
        return wlRegionUserRelMapper.selectByUserId(userId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean addRegionUserRel(String tenantId, String insuranceCompanyId, String regionCode, List<String> userIds) {
        try {
            List<WlRegionUserRel> relList = new ArrayList<>();
            Date now = new Date();
            
            for (String userId : userIds) {
                // 检查关系是否已存在
                if (!existsRelation(tenantId, insuranceCompanyId, regionCode, userId)) {
                    WlRegionUserRel rel = new WlRegionUserRel();
                    rel.setTenantId(tenantId);
                    rel.setInsuranceCompanyId(insuranceCompanyId);
                    rel.setRegionCode(regionCode);
                    rel.setUserId(userId);
                    rel.setCreateTime(now);
                    relList.add(rel);
                }
            }
            
            if (!relList.isEmpty()) {
                return this.saveBatch(relList);
            }
            return true;
        } catch (Exception e) {
            log.error("添加后勤人员关系失败", e);
            throw new RuntimeException("添加后勤人员关系失败", e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeRegionUserRel(String tenantId, String insuranceCompanyId, String regionCode, List<String> userIds) {
        try {
            for (String userId : userIds) {
                WlRegionUserRel rel = wlRegionUserRelMapper.selectByTenantInsuranceRegionUser(tenantId, insuranceCompanyId, regionCode, userId);
                if (rel != null) {
                    this.removeById(rel.getId());
                }
            }
            return true;
        } catch (Exception e) {
            log.error("移除后勤人员关系失败", e);
            throw new RuntimeException("移除后勤人员关系失败", e);
        }
    }

    @Override
    public boolean existsRelation(String tenantId, String insuranceCompanyId, String regionCode, String userId) {
        WlRegionUserRel rel = wlRegionUserRelMapper.selectByTenantInsuranceRegionUser(tenantId, insuranceCompanyId, regionCode, userId);
        return rel != null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeByTenantId(String tenantId) {
        try {
            int count = wlRegionUserRelMapper.deleteByTenantId(tenantId);
            log.info("删除租户{}的后勤人员关系{}条", tenantId, count);
            return true;
        } catch (Exception e) {
            log.error("根据租户ID删除关系失败", e);
            throw new RuntimeException("根据租户ID删除关系失败", e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeByUserId(String userId) {
        try {
            int count = wlRegionUserRelMapper.deleteByUserId(userId);
            log.info("删除用户{}的后勤人员关系{}条", userId, count);
            return true;
        } catch (Exception e) {
            log.error("根据用户ID删除关系失败", e);
            throw new RuntimeException("根据用户ID删除关系失败", e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeByTenantIdAndRegionCode(String tenantId, String regionCode) {
        try {
            int count = wlRegionUserRelMapper.deleteByTenantIdAndRegionCode(tenantId, regionCode);
            log.info("删除租户{}地区{}的后勤人员关系{}条", tenantId, regionCode, count);
            return true;
        } catch (Exception e) {
            log.error("根据租户ID和地区编码删除关系失败", e);
            throw new RuntimeException("根据租户ID和地区编码删除关系失败", e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeByTenantInsuranceRegion(String tenantId, String insuranceCompanyId, String regionCode) {
        try {
            int count = wlRegionUserRelMapper.deleteByTenantInsuranceRegion(tenantId, insuranceCompanyId, regionCode);
            log.info("删除租户{}保险公司{}地区{}的后勤人员关系{}条", tenantId, insuranceCompanyId, regionCode, count);
            return true;
        } catch (Exception e) {
            log.error("根据租户ID、保险公司ID和地区编码删除关系失败", e);
            throw new RuntimeException("根据租户ID、保险公司ID和地区编码删除关系失败", e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeBatchByIds(List<String> ids) {
        try {
            int count = wlRegionUserRelMapper.deleteBatchByIds(ids);
            log.info("批量删除后勤人员关系{}条", count);
            return true;
        } catch (Exception e) {
            log.error("批量删除关系失败", e);
            throw new RuntimeException("批量删除关系失败", e);
        }
    }
}
