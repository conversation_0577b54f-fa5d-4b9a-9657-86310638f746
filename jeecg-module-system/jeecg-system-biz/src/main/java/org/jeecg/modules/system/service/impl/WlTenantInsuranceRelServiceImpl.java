package org.jeecg.modules.system.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.system.entity.WlTenantInsuranceRel;
import org.jeecg.modules.system.mapper.WlTenantInsuranceRelMapper;
import org.jeecg.modules.system.service.IWlTenantInsuranceRelService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @Description: 租户保险公司关系表Service实现
 * @Author: jeecg-boot
 * @Date: 2024-01-01
 * @Version: V1.0
 */
@Service
@Slf4j
public class WlTenantInsuranceRelServiceImpl extends ServiceImpl<WlTenantInsuranceRelMapper, WlTenantInsuranceRel> implements IWlTenantInsuranceRelService {

    @Autowired
    private WlTenantInsuranceRelMapper wlTenantInsuranceRelMapper;

    @Override
    public List<WlTenantInsuranceRel> getByTenantId(String tenantId) {
        return wlTenantInsuranceRelMapper.selectByTenantId(tenantId);
    }

    @Override
    public List<WlTenantInsuranceRel> getByInsuranceCompanyId(String insuranceCompanyId) {
        return wlTenantInsuranceRelMapper.selectByInsuranceCompanyId(insuranceCompanyId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean addTenantInsuranceRel(String tenantId, List<String> insuranceCompanyIds) {
        try {
            List<WlTenantInsuranceRel> relList = new ArrayList<>();
            Date now = new Date();
            
            for (String insuranceCompanyId : insuranceCompanyIds) {
                // 检查关系是否已存在
                if (!existsRelation(tenantId, insuranceCompanyId)) {
                    WlTenantInsuranceRel rel = new WlTenantInsuranceRel();
                    rel.setTenantId(tenantId);
                    rel.setInsuranceCompanyId(insuranceCompanyId);
                    rel.setCreateTime(now);
                    relList.add(rel);
                }
            }
            
            if (!relList.isEmpty()) {
                return this.saveBatch(relList);
            }
            return true;
        } catch (Exception e) {
            log.error("添加租户保险公司关系失败", e);
            throw new RuntimeException("添加租户保险公司关系失败", e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeTenantInsuranceRel(String tenantId, List<String> insuranceCompanyIds) {
        try {
            for (String insuranceCompanyId : insuranceCompanyIds) {
                WlTenantInsuranceRel rel = wlTenantInsuranceRelMapper.selectByTenantAndInsurance(tenantId, insuranceCompanyId);
                if (rel != null) {
                    this.removeById(rel.getId());
                }
            }
            return true;
        } catch (Exception e) {
            log.error("移除租户保险公司关系失败", e);
            throw new RuntimeException("移除租户保险公司关系失败", e);
        }
    }

    @Override
    public boolean existsRelation(String tenantId, String insuranceCompanyId) {
        WlTenantInsuranceRel rel = wlTenantInsuranceRelMapper.selectByTenantAndInsurance(tenantId, insuranceCompanyId);
        return rel != null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeByTenantId(String tenantId) {
        try {
            int count = wlTenantInsuranceRelMapper.deleteByTenantId(tenantId);
            log.info("删除租户{}的保险公司关系{}条", tenantId, count);
            return true;
        } catch (Exception e) {
            log.error("根据租户ID删除关系失败", e);
            throw new RuntimeException("根据租户ID删除关系失败", e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeByInsuranceCompanyId(String insuranceCompanyId) {
        try {
            int count = wlTenantInsuranceRelMapper.deleteByInsuranceCompanyId(insuranceCompanyId);
            log.info("删除保险公司{}的租户关系{}条", insuranceCompanyId, count);
            return true;
        } catch (Exception e) {
            log.error("根据保险公司ID删除关系失败", e);
            throw new RuntimeException("根据保险公司ID删除关系失败", e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeBatchByIds(List<String> ids) {
        try {
            int count = wlTenantInsuranceRelMapper.deleteBatchByIds(ids);
            log.info("批量删除关系{}条", count);
            return true;
        } catch (Exception e) {
            log.error("批量删除关系失败", e);
            throw new RuntimeException("批量删除关系失败", e);
        }
    }
}
