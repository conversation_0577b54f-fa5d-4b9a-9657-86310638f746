package org.jeecg.modules.system.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description: 租户后勤人员配置VO
 * @Author: jeecg-boot
 * @Date: 2024-01-01
 * @Version: V1.0
 */
@Data
@ApiModel(value="TenantLogisticsStaffVO对象", description="租户后勤人员配置VO")
public class TenantLogisticsStaffVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**关系ID*/
    @ApiModelProperty(value = "关系ID")
    private String id;

    /**租户ID*/
    @ApiModelProperty(value = "租户ID")
    private String tenantId;

    /**租户名称*/
    @ApiModelProperty(value = "租户名称")
    private String tenantName;

    /**保险公司ID*/
    @ApiModelProperty(value = "保险公司ID")
    private String insuranceCompanyId;

    /**保险公司名称*/
    @ApiModelProperty(value = "保险公司名称")
    private String insuranceCompanyName;

    /**保险公司图标*/
    @ApiModelProperty(value = "保险公司图标")
    private String insuranceCompanyIcon;

    /**地区编码*/
    @ApiModelProperty(value = "地区编码")
    private String regionCode;

    /**地区名称*/
    @ApiModelProperty(value = "地区名称")
    private String regionName;

    /**用户ID*/
    @ApiModelProperty(value = "用户ID")
    private String userId;

    /**用户名*/
    @ApiModelProperty(value = "用户名")
    private String username;

    /**真实姓名*/
    @ApiModelProperty(value = "真实姓名")
    private String realname;

    /**头像*/
    @ApiModelProperty(value = "头像")
    private String avatar;

    /**手机号*/
    @ApiModelProperty(value = "手机号")
    private String phone;

    /**邮箱*/
    @ApiModelProperty(value = "邮箱")
    private String email;

    /**用户状态*/
    @ApiModelProperty(value = "用户状态")
    private Integer status;

    /**部门编码*/
    @ApiModelProperty(value = "部门编码")
    private String orgCode;

    /**部门名称*/
    @ApiModelProperty(value = "部门名称")
    private String orgCodeTxt;

    /**微信二维码*/
    @ApiModelProperty(value = "微信二维码")
    private String wechatQrcode;

    /**创建时间*/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
}
