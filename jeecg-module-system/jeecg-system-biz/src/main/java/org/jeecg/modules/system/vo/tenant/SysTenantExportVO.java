package org.jeecg.modules.system.vo.tenant;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.jeecg.common.aspect.annotation.Dict;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 租户信息导出VO
 * @author: jeecg-boot
 */
@Data
public class SysTenantExportVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 编码
     */
    @Excel(name = "租户编号(ID)", width = 15)
    private Integer id;

    /**
     * 名称
     */
    @Excel(name = "租户名称", width = 20)
    private String name;

    /**
     * 公司地址
     */
    @Excel(name = "公司地址", width = 30)
    private String companyAddress;

    /**
     * 简介
     */
    @Excel(name = "简介", width = 30)
    private String intro;

    /**
     * 创建人
     */
    @Excel(name = "创建者(拥有者)", width = 15, dictTable = "sys_user", dicText = "realname", dicCode = "username")
    private String createBy;

    /**
     * 创建时间
     */
    @Excel(name = "创建时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 状态 1正常 0冻结
     */
    @Excel(name = "状态", width = 10, dicCode = "tenant_status")
    private Integer status;

    @Excel(name = "排序", width = 10)
    private BigDecimal sortOrder;

    @Excel(name = "是否显示", width = 10, replace = {"是_1", "否_0"})
    private Integer isShow;

    /**
     * 所属行业
     */
    @Excel(name = "所属行业", width = 15, dicCode = "trade")
    private String trade;

    /**
     * 公司规模
     */
    @Excel(name = "公司规模", width = 15, dicCode = "company_size")
    private String companySize;

    @Excel(name = "IP地址", width = 15)
    private String ipAddress;

    /**
     * 门牌号
     */
    @Excel(name = "门牌号", width = 15)
    private String houseNumber;

    /**
     * 职级
     */
    @Excel(name = "职级", width = 15, dicCode = "company_rank")
    private String position;

    /**
     * 部门
     */
    @Excel(name = "部门", width = 15, dicCode = "company_department")
    private String department;

    /**
     * 注册时间
     */
    @Excel(name = "注册时间", width = 20)
    private String regTime;

    /**
     * 法人
     */
    @Excel(name = "法人", width = 15)
    private String legalPerson;

    @Excel(name = "电话", width = 15)
    private String phone;

    @Excel(name = "网址", width = 20)
    private String website;

    @Excel(name = "企业邮箱", width = 20)
    private String email;

    @Excel(name = "统一社会信用代码", width = 25)
    private String creditCode;

    @Excel(name = "注册资本", width = 15)
    private String registeredCapital;

    @Excel(name = "租户分类", width = 15)
    private String tenantType;

    @Excel(name = "业务类型", width = 20, replace = {"车险_0", "财险_1", "增值服务_2"})
    private String busType;
}
