-- ========================================
-- Version: 3.6.10
-- Description: 添加 wh_backgroun_config 表的 click_total 字段
-- ========================================

SET FOREIGN_KEY_CHECKS = 0;

-- 删除现有存储过程，防止重复定义
DROP PROCEDURE IF EXISTS `schema_change`;
DELIMITER ;;

CREATE PROCEDURE schema_change()
BEGIN

    -- 禁用外键检查
    SET FOREIGN_KEY_CHECKS = 0;

    -- 检查并新增 wh_backgroun_config.click_total 字段
    IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS
                   WHERE TABLE_SCHEMA = '#(database_name)' AND TABLE_NAME = 'wh_backgroun_config' AND COLUMN_NAME = 'click_total') THEN
        ALTER TABLE `#(database_name)`.`wh_backgroun_config`
            ADD COLUMN `click_total` BIGINT DEFAULT 0 COMMENT '累计总点击数';
    END IF;

    -- 启用外键检查
    SET FOREIGN_KEY_CHECKS = 1;

END;;
DELIMITER ;

-- 执行存储过程
CALL schema_change();

-- 删除存储过程
DROP PROCEDURE IF EXISTS `schema_change`;

-- 恢复外键检查
SET FOREIGN_KEY_CHECKS = 1;
