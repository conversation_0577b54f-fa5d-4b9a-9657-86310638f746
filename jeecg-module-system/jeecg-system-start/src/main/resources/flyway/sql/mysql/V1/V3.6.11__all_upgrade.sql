SET FOREIGN_KEY_CHECKS = 0;

DROP PROCEDURE IF EXISTS `schema_change`;
DELIMITER ;;

CREATE PROCEDURE schema_change()
BEGIN

    -- 禁用外键检查
    SET FOREIGN_KEY_CHECKS = 0;

    -- 创建 sys_menu 表（如果不存在）
    CREATE TABLE IF NOT EXISTS `#(database_name)`.`click_auto_pre` (
        `id` varchar(36) NOT NULL COMMENT '主键ID',
        `click_num` int(11) DEFAULT NULL COMMENT '点击数',
        `stat_date` date DEFAULT NULL COMMENT '统计日期',
        `hour` int(2) DEFAULT NULL COMMENT '小时 (0-23)',
        `tenant_id` int(10) DEFAULT NULL COMMENT '租户ID',
        PRIMARY KEY (`id`) USING BTREE,
        KEY `idx_tenant_date_hour` (`tenant_id`,`stat_date`,`hour`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='按小时统计点击报表';

END;;
DELIMITER ;

-- 执行存储过程
CALL schema_change();

-- 删除存储过程
DROP PROCEDURE IF EXISTS `schema_change`;

SET FOREIGN_KEY_CHECKS = 1;
