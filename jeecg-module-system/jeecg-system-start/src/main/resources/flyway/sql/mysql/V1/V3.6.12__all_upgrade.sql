SET FOREIGN_KEY_CHECKS = 0;

-- 删除旧的过程定义
DROP PROCEDURE IF EXISTS `schema_change`;
DELIMITER ;;

CREATE PROCEDURE schema_change()
BEGIN

    -- 禁用外键检查
    SET FOREIGN_KEY_CHECKS = 0;

    -- 创建表（如果不存在）
    CREATE TABLE IF NOT EXISTS `#(database_name)`.`pd_link_recode` (
        `id` VARCHAR(36) PRIMARY KEY COMMENT '主键',
        `update_time` DATETIME DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
        `link_date` DATE COMMENT '日期',
        `config_type` INT COMMENT '配置类型(0-车险;1-财险;2-增值服务)',
        `bounce_rate` DECIMAL(10,2) COMMENT '跳出率(%)',
        `conversion_rate` DECIMAL(10,2) COMMENT '转化率(%)',
        `form_submissions` INT COMMENT '表单提交数',
        `ctr` DECIMAL(10,2) COMMENT '点击率(%)',
        `avg_stay_time` INT COMMENT '平均停留时长(秒)',
        `return_rate` DECIMAL(10,2) COMMENT '返回率(%)',
        `completion_rate` DECIMAL(10,2) COMMENT '内容完成率(%)',
        `first_screen_ctr` DECIMAL(10,2) COMMENT '首屏CTR值(%)',
        `content_jump_rate` DECIMAL(10,2) COMMENT '内容Jump率(%)',
        `content_return_rate` DECIMAL(10,2) COMMENT '内容Return率(%)',
        `click_depth` DECIMAL(10,2) COMMENT 'Click深度分布(层)',
        `click_interval_time` DECIMAL(10,2) COMMENT 'Click间隔时间(秒)',
        `engagement_score` DECIMAL(10,2) COMMENT 'Engagement得分(分)',
        `page_rate` DECIMAL(10,2) COMMENT '翻页率比例',
        `unique_query` INT COMMENT '独立Query数',
        `top3_pv_ctr` DECIMAL(10,2) COMMENT 'TOP3 PV-CTR(%)',
        `tenant_id` INT COMMENT '租户id'
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='链接快照';

    -- 如果组合索引不存在，则添加
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.STATISTICS
        WHERE table_schema = '#(database_name)'
          AND table_name = 'pd_link_recode'
          AND index_name = 'idx_tenant_date_type'
    ) THEN
        ALTER TABLE `#(database_name)`.`pd_link_recode`
            ADD INDEX `idx_tenant_date_type` (`tenant_id`, `link_date`, `config_type`) COMMENT '租户日期类型组合索引';
    END IF;

    -- 启用外键检查
    SET FOREIGN_KEY_CHECKS = 1;

END;;
DELIMITER ;

-- 执行存储过程
CALL schema_change();

-- 删除过程定义
DROP PROCEDURE IF EXISTS `schema_change`;

SET FOREIGN_KEY_CHECKS = 1;
