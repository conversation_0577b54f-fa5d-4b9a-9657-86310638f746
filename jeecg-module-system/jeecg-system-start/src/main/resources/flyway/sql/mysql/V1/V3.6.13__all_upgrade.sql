SET FOREIGN_KEY_CHECKS = 0;

DROP PROCEDURE IF EXISTS `schema_change`;
DELIMITER ;;

CREATE PROCEDURE schema_change()
BEGIN

    -- 禁用外键检查
    SET FOREIGN_KEY_CHECKS = 0;

    -- 创建 pd_generate_config 表（如果不存在）
    CREATE TABLE IF NOT EXISTS `#(database_name)`.`pd_generate_config` (
        `id` VARCHAR(36) PRIMARY KEY COMMENT '主键',
        `create_by` VARCHAR(50) COMMENT '创建人',
        `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建日期',
        `update_by` VARCHAR(50) COMMENT '更新人',
        `update_time` DATETIME DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新日期',
        `bounce_rate_start` DECIMAL(10,2) COMMENT '跳出率开始区间(%)',
        `bounce_rate_end` DECIMAL(10,2) COMMENT '跳出率结束区间(%)',
        `conversion_rate_start` DECIMAL(10,2) COMMENT '转化率开始区间(%)',
        `conversion_rate_end` DECIMAL(10,2) COMMENT '转化率结束区间(%)',
        `form_submissions_start` INT COMMENT '表单提交数开始区间',
        `form_submissions_end` INT COMMENT '表单提交数结束区间',
        `ctr_start` DECIMAL(10,2) COMMENT '点击率开始区间(%)',
        `ctr_end` DECIMAL(10,2) COMMENT '点击率结束区间(%)',
        `avg_stay_time_start` INT COMMENT '平均停留时长开始区间(秒)',
        `avg_stay_time_end` INT COMMENT '平均停留时长结束区间(秒)',
        `return_rate_start` DECIMAL(10,2) COMMENT '返回率开始区间(%)',
        `return_rate_end` DECIMAL(10,2) COMMENT '返回率结束区间(%)',
        `completion_rate_start` DECIMAL(10,2) COMMENT '内容完成率开始区间(%)',
        `completion_rate_end` DECIMAL(10,2) COMMENT '内容完成率结束区间(%)',
        `first_screen_ctr_start` DECIMAL(10,2) COMMENT '首屏CTR值开始区间(%)',
        `first_screen_ctr_end` DECIMAL(10,2) COMMENT '首屏CTR值结束区间(%)',
        `content_jump_rate_start` DECIMAL(10,2) COMMENT '内容Jump率开始区间(%)',
        `content_jump_rate_end` DECIMAL(10,2) COMMENT '内容Jump率结束区间(%)',
        `content_return_rate_start` DECIMAL(10,2) COMMENT '内容Return率开始区间(%)',
        `content_return_rate_end` DECIMAL(10,2) COMMENT '内容Return率结束区间(%)',
        `click_depth_start` DECIMAL(10,2) COMMENT 'Click深度分布开始区间(层)',
        `click_depth_end` DECIMAL(10,2) COMMENT 'Click深度分布结束区间(层)',
        `click_interval_time_start` DECIMAL(10,2) COMMENT 'Click间隔时间开始区间(秒)',
        `click_interval_time_end` DECIMAL(10,2) COMMENT 'Click间隔时间结束区间(秒)',
        `engagement_score_start` DECIMAL(10,2) COMMENT 'Engagement得分开始区间(分)',
        `engagement_score_end` DECIMAL(10,2) COMMENT 'Engagement得分结束区间(分)',
        `page_start_num` INT COMMENT '翻页率比例开始',
        `page_end_num` INT COMMENT '翻页率结束',
        `unique_query_start` INT COMMENT '独立Query数开始区间',
        `unique_query_end` INT COMMENT '独立Query数结束区间',
        `top3_pv_ctr_start` DECIMAL(10,2) COMMENT 'TOP3 PV-CTR开始区间(%)',
        `top3_pv_ctr_end` DECIMAL(10,2) COMMENT 'TOP3 PV-CTR结束区间(%)',
        `tenant_id` INT COMMENT '租户id'
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='生成规则配置';


    -- 启用外键检查
    SET FOREIGN_KEY_CHECKS = 1;

END;;
DELIMITER ;

-- 执行存储过程
CALL schema_change();

-- 删除存储过程
DROP PROCEDURE IF EXISTS `schema_change`;

SET FOREIGN_KEY_CHECKS = 1;
