SET FOREIGN_KEY_CHECKS = 0;

DROP PROCEDURE IF EXISTS `schema_change`;
DELIMITER ;;

CREATE PROCEDURE schema_change()
BEGIN

    -- 禁用外键检查
    SET FOREIGN_KEY_CHECKS = 0;

    -- 检查并新增 sys_user.all_check 字段
    IF NOT EXISTS (
        SELECT 1 FROM INFORMATION_SCHEMA.COLUMNS
        WHERE TABLE_SCHEMA = '#(database_name)'
        AND TABLE_NAME = 'sys_user'
        AND COLUMN_NAME = 'all_check'
    ) THEN
        ALTER TABLE `#(database_name)`.`sys_user`
        ADD COLUMN `all_check` TINYINT(1)  DEFAULT 0 COMMENT '查看所有数据权限（0：否，1：是）';
    END IF;

    -- 启用外键检查
    SET FOREIGN_KEY_CHECKS = 1;

END;;
DELIMITER ;

-- 执行存储过程
CALL schema_change();

-- 删除存储过程
DROP PROCEDURE IF EXISTS `schema_change`;

SET FOREIGN_KEY_CHECKS = 1;
