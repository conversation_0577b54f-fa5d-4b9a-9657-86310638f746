SET FOREIGN_KEY_CHECKS = 0;

-- 删除旧的过程定义
DROP PROCEDURE IF EXISTS `schema_change`;
DELIMITER ;;

CREATE PROCEDURE schema_change()
BEGIN

    -- 禁用外键检查
    SET FOREIGN_KEY_CHECKS = 0;

    -- 如果 sys_user 表不存在 wechat_qrcode 字段，则添加
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.COLUMNS
        WHERE table_schema = DATABASE()
          AND table_name = 'sys_user'
          AND column_name = 'wechat_qrcode'
    ) THEN
        ALTER TABLE `#(database_name)`.`sys_user`
            ADD COLUMN `wechat_qrcode` VARCHAR(255)
                CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci
                DEFAULT NULL COMMENT '微信二维码地址';
    END IF;

    -- 启用外键检查
    SET FOREIGN_KEY_CHECKS = 1;

END;;
DELIMITER ;

-- 执行存储过程
CALL schema_change();

-- 删除过程定义
DROP PROCEDURE IF EXISTS `schema_change`;

SET FOREIGN_KEY_CHECKS = 1;
