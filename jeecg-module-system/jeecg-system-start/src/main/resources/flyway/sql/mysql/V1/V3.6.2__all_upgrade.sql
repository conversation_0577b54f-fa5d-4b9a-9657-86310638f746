SET FOREIGN_KEY_CHECKS = 0;

DROP PROCEDURE IF EXISTS `schema_change`;
DELIMITER ;;

CREATE PROCEDURE schema_change()
BEGIN

    -- 禁用外键检查
    SET FOREIGN_KEY_CHECKS = 0;

    -- 创建 pd_chat_config 表
CREATE TABLE IF NOT EXISTS `#(database_name)`.`pd_chat_config` (
                                                `id` VARCHAR(64) PRIMARY KEY COMMENT '主键',
    `chat_length_rate` JSON COMMENT '聊天长度占比（JSON格式）',
    `chat_title` JSON COMMENT '聊天标题 ID 占比（JSON格式）',
    `chat_mood_rate` JSON COMMENT '聊天情绪占比（JSON格式）',
    `response_time_rate` JSON COMMENT '回复时长占比（JSON格式）',
    `thank_at_end_rate` DECIMAL(5,2) COMMENT '感谢是否结尾占比（百分比）',
    `number_generated` INT COMMENT '生成对话总数',
    `create_time` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='聊天记录配置表';

-- 启用外键检查
SET FOREIGN_KEY_CHECKS = 1;

END;;
DELIMITER ;

-- 执行存储过程
CALL schema_change();

-- 删除存储过程
DROP PROCEDURE IF EXISTS `schema_change`;

SET FOREIGN_KEY_CHECKS = 1;