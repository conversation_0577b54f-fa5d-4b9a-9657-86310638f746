
SET FOREIGN_KEY_CHECKS = 0;

-- 删除现有存储过程，防止重复定义
DROP PROCEDURE IF EXISTS `schema_change`;
DELIMITER ;;

CREATE PROCEDURE schema_change()
BEGIN

    -- 禁用外键检查
    SET FOREIGN_KEY_CHECKS = 0;

    -- 检查并新增 pd_car_info.phone_number 字段
    IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS
                   WHERE TABLE_SCHEMA = '#(database_name)' AND TABLE_NAME = 'pd_car_info' AND COLUMN_NAME = 'phone_number') THEN
ALTER TABLE `#(database_name)`.`pd_car_info`
    ADD COLUMN `phone_number` VARCHAR(18)  NULL COMMENT '手机号' AFTER `license_plate_number`;
END IF;

    -- 检查并新增 pd_ledger.phone_number 字段
    IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS
                   WHERE TABLE_SCHEMA = '#(database_name)' AND TABLE_NAME = 'pd_ledger' AND COLUMN_NAME = 'phone_number') THEN
ALTER TABLE `#(database_name)`.`pd_ledger`
    ADD COLUMN `phone_number` VARCHAR(18)  NULL COMMENT '手机号' AFTER `insured`;
END IF;

    -- 检查并新增 app_news.news_time 字段
    IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS
                   WHERE TABLE_SCHEMA = '#(database_name)' AND TABLE_NAME = 'app_news' AND COLUMN_NAME = 'news_time') THEN
ALTER TABLE `#(database_name)`.`app_news`
    ADD COLUMN `news_time` TIMESTAMP  NULL COMMENT '资讯时间';
END IF;

    -- 检查并新增 app_news.hold_num 字段
    IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS
                   WHERE TABLE_SCHEMA = '#(database_name)' AND TABLE_NAME = 'app_news' AND COLUMN_NAME = 'hold_num') THEN
ALTER TABLE `#(database_name)`.`app_news`
    ADD COLUMN `hold_num` TINYINT(1) DEFAULT 0 COMMENT '点赞数';
END IF;

    -- 检查并新增 app_news.link 字段
    IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS
                   WHERE TABLE_SCHEMA = '#(database_name)' AND TABLE_NAME = 'app_news' AND COLUMN_NAME = 'link') THEN
ALTER TABLE `#(database_name)`.`app_news`
    ADD COLUMN `link` VARCHAR(255) NULL   COMMENT '链接' AFTER `image`;
END IF;

    -- 创建 pd_news_flash 表
CREATE TABLE IF NOT EXISTS `#(database_name)`.`pd_news_flash` (
                                                       `id` VARCHAR(36) NOT NULL,
    `order_time` DATETIME  NULL COMMENT '新闻时间',
    `content` VARCHAR(150)  NULL COMMENT '标题',
    `news_id` VARCHAR(32)  NULL COMMENT '资讯 id',
    `tenant_id` INT(11)  NULL COMMENT '租户 id',
    PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 检查并新增 pd_news_flash.hold_num 字段
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS
                   WHERE TABLE_SCHEMA = '#(database_name)' AND TABLE_NAME = 'pd_news_flash' AND COLUMN_NAME = 'hold_num') THEN
ALTER TABLE `#(database_name)`.`pd_news_flash`
    ADD COLUMN `hold_num` INT(12) DEFAULT 0 COMMENT '点赞数';
END IF;

    -- 检查并新增 sys_tenant.sort_order 字段
    IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS
                   WHERE TABLE_SCHEMA = '#(database_name)' AND TABLE_NAME = 'sys_tenant' AND COLUMN_NAME = 'sort_order') THEN
ALTER TABLE `#(database_name)`.`sys_tenant`
    ADD COLUMN `sort_order` DECIMAL(18, 2) DEFAULT NULL COMMENT '排序';
END IF;

    -- 检查并新增 sys_tenant.is_show 字段
    IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS
                   WHERE TABLE_SCHEMA = '#(database_name)' AND TABLE_NAME = 'sys_tenant' AND COLUMN_NAME = 'is_show') THEN
ALTER TABLE `#(database_name)`.`sys_tenant`
    ADD COLUMN `is_show` TINYINT(1) DEFAULT 1 COMMENT '是否展示，1是，2否';
END IF;

    -- 启用外键检查
    SET FOREIGN_KEY_CHECKS = 1;

END;;
DELIMITER ;

-- 执行存储过程
CALL schema_change();

-- 删除存储过程
DROP PROCEDURE IF EXISTS `schema_change`;

-- 恢复外键检查
SET FOREIGN_KEY_CHECKS = 1;