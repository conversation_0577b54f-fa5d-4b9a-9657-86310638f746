SET FOREIGN_KEY_CHECKS = 0;

-- 删除现有存储过程，防止重复定义
DROP PROCEDURE IF EXISTS `schema_change`;
DELIMITER ;;

CREATE PROCEDURE create_index(
    IN db_name VARCHAR(255),
    IN table_name VARCHAR(255),
    IN index_name VARCHAR(255),
    IN index_columns VARCHAR(255)
)
BEGIN
    DECLARE index_exists INT DEFAULT 0;

    -- 检查索引是否存在
SELECT COUNT(1) INTO index_exists FROM information_schema.statistics
WHERE table_schema = db_name AND table_name = table_name AND index_name = index_name;

-- 如果索引不存在，则创建
IF index_exists = 0 THEN
        SET @sql = CONCAT('ALTER TABLE ', db_name, '.', table_name, ' ADD INDEX ', index_name, ' (', index_columns, ');');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;
END IF;
END;;

-- 主存储过程：用于统一管理索引创建
CREATE PROCEDURE schema_change()
BEGIN
    SET FOREIGN_KEY_CHECKS = 0;

    -- ========================================
    -- 新增 Tenant's shelf status 表
    -- ========================================
CREATE TABLE IF NOT EXISTS `#(database_name)`.`tenant_shelf_status` (
                                                                        `id` VARCHAR(36) NOT NULL,
    `page_type` TINYINT(1) DEFAULT 0 COMMENT '展示页面(0=上架页面; 1=公司页)',
    `tenant_id` INT(11) DEFAULT NULL COMMENT '多租户',
    `is_valid` TINYINT(1) DEFAULT 0 COMMENT '是否有效(0=否, 1=是)',
    `create_by` VARCHAR(50) DEFAULT NULL COMMENT '创建人',
    `create_time` DATETIME DEFAULT NULL COMMENT '创建时间',
    PRIMARY KEY (`id`) USING BTREE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='租户上架状态表';

-- 调用索引创建过程
CALL create_index('#(database_name)', 'pd_car_info', 'idx_tenant_id', 'tenant_id');
CALL create_index('#(database_name)', 'pd_car_info', 'idx_create_time', 'create_time');
CALL create_index('#(database_name)', 'pd_car_info', 'idx_tenant_create_time', 'tenant_id, create_time');

CALL create_index('#(database_name)', 'pd_car_info_rel', 'idx_car_info_id', 'car_info_id');

CALL create_index('#(database_name)', 'pd_integrated', 'idx_id', 'id');

CALL create_index('#(database_name)', 'click_report', 'idx_company_id', 'company_id');

CALL create_index('#(database_name)', 'sys_tenant', 'idx_name', 'name');

SET FOREIGN_KEY_CHECKS = 1;
END;;

DELIMITER ;

-- 执行存储过程
CALL schema_change();

-- 删除存储过程
DROP PROCEDURE IF EXISTS `schema_change`;
DROP PROCEDURE IF EXISTS `create_index`;

SET FOREIGN_KEY_CHECKS = 1;
