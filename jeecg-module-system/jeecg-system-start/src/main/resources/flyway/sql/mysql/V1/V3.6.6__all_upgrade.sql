SET FOREIGN_KEY_CHECKS = 0;

-- 删除现有存储过程，防止重复定义
DROP PROCEDURE IF EXISTS `schema_change`;
DELIMITER ;;

CREATE PROCEDURE schema_change()
BEGIN

    -- 禁用外键检查
    SET FOREIGN_KEY_CHECKS = 0;

    -- ========================================
    -- 新增 click_auto_pre 表
    -- ========================================
CREATE TABLE IF NOT EXISTS `#(database_name)`.`click_auto_pre` (
                                                        `id` VARCHAR(36) NOT NULL,
    `click_time` DATETIME DEFAULT NULL COMMENT '点击时间',
    `tenant_id` INT(11) DEFAULT NULL COMMENT '多租户',
    `ledger_type` VARCHAR(32) DEFAULT NULL COMMENT '台账类型(1.财险台账 2.增值服务台账 3.车险台账)',
    `auto_create` TINYINT(4) DEFAULT '0' COMMENT '是否进行台账（0=否，1=是）',
    `has_chat_user` TINYINT(4) DEFAULT '0' COMMENT '是否存在聊天用户（0=否，1=是）',
    `city` VARCHAR(32) DEFAULT NULL COMMENT '城市',
    `create_by` VARCHAR(50) DEFAULT NULL COMMENT '创建人',
    `create_time` DATETIME DEFAULT NULL COMMENT '创建时间',
    PRIMARY KEY (`id`) USING BTREE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- ========================================
-- 新增 daily_config 表
-- ========================================
CREATE TABLE IF NOT EXISTS `#(database_name)`.`daily_config` (
                                                      `id` VARCHAR(36) NOT NULL,
    `config_json` JSON NOT NULL COMMENT '每日配置 JSON 内容（包含：台账区间、聊天用户区间、城市范围）',
    `click_start` INT NOT NULL COMMENT '每日点击数开始',
    `click_end` INT NOT NULL COMMENT '每日点击数结束',
    `tenant_id` INT(11) DEFAULT NULL COMMENT '多租户',
    `update_by` VARCHAR(50) DEFAULT NULL COMMENT '更新人',
    `update_time` DATETIME DEFAULT NULL COMMENT '更新日期',
    `create_by` VARCHAR(50) DEFAULT NULL COMMENT '创建人',
    `create_time` DATETIME DEFAULT NULL COMMENT '创建时间',
    PRIMARY KEY (`id`) USING BTREE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='每日租户配置表';

-- 启用外键检查
SET FOREIGN_KEY_CHECKS = 1;

END;;
DELIMITER ;

-- 执行存储过程
CALL schema_change();

-- 删除存储过程
DROP PROCEDURE IF EXISTS `schema_change`;

-- 恢复外键检查
SET FOREIGN_KEY_CHECKS = 1;