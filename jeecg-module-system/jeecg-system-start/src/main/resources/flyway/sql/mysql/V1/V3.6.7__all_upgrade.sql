-- ========================================
-- Version: 6
-- Description: Add pd_generate_config table in taiyi
-- ========================================

SET FOREIGN_KEY_CHECKS = 0;

-- 删除现有存储过程，防止重复定义
DROP PROCEDURE IF EXISTS `schema_change`;
DELIMITER ;;

CREATE PROCEDURE schema_change()
BEGIN

    -- 禁用外键检查
    SET FOREIGN_KEY_CHECKS = 0;

    -- ========================================
    -- 新增 pd_generate_config 表
    -- ========================================
CREATE TABLE IF NOT EXISTS `#(database_name)`.`pd_generate_config` (
                                                            `id` VARCHAR(36) NOT NULL,
    `create_by` VARCHAR(50) DEFAULT NULL COMMENT '创建人',
    `create_time` DATETIME DEFAULT NULL COMMENT '创建日期',
    `update_by` VARCHAR(50) DEFAULT NULL COMMENT '更新人',
    `update_time` DATETIME DEFAULT NULL COMMENT '更新日期',
    `tenant_id` INT(11) DEFAULT NULL COMMENT '租户id',
    `link_type` INT(11) DEFAULT NULL COMMENT '链接类型 0车险 1 财险 2 增值服务',

    `pv_start_num` INT(11) DEFAULT NULL COMMENT 'PV开始区间',
    `pv_end_num` INT(11) DEFAULT NULL COMMENT 'PV结束区间',
    `pv_rate` DECIMAL(10,2) DEFAULT NULL COMMENT 'PV环比率(%)',

    `uv_start_num` INT(11) DEFAULT NULL COMMENT 'UV开始区间',
    `uv_end_num` INT(11) DEFAULT NULL COMMENT 'UV结束区间',
    `uv_rate` DECIMAL(10,2) DEFAULT NULL COMMENT 'UV环比率(%)',

    `bounce_rate_start` DECIMAL(10,2) DEFAULT NULL COMMENT '跳出率开始区间(%)',
    `bounce_rate_end` DECIMAL(10,2) DEFAULT NULL COMMENT '跳出率结束区间(%)',
    `bounce_rate_rate` DECIMAL(10,2) DEFAULT NULL COMMENT '跳出率环比率(%)',

    `conversion_rate_start` DECIMAL(10,2) DEFAULT NULL COMMENT '转化率开始区间(%)',
    `conversion_rate_end` DECIMAL(10,2) DEFAULT NULL COMMENT '转化率结束区间(%)',
    `conversion_rate_rate` DECIMAL(10,2) DEFAULT NULL COMMENT '转化率环比率(%)',

    `form_submissions_start` INT(11) DEFAULT NULL COMMENT '表单提交数开始区间',
    `form_submissions_end` INT(11) DEFAULT NULL COMMENT '表单提交数结束区间',
    `form_submissions_rate` DECIMAL(10,2) DEFAULT NULL COMMENT '表单提交数环比率(%)',

    `ctr_start` DECIMAL(10,2) DEFAULT NULL COMMENT '点击率开始区间(%)',
    `ctr_end` DECIMAL(10,2) DEFAULT NULL COMMENT '点击率结束区间(%)',
    `ctr_rate` DECIMAL(10,2) DEFAULT NULL COMMENT '点击率环比率(%)',

    `avg_stay_time_start` INT(11) DEFAULT NULL COMMENT '平均停留时长开始区间(秒)',
    `avg_stay_time_end` INT(11) DEFAULT NULL COMMENT '平均停留时长结束区间(秒)',
    `avg_stay_time_rate` DECIMAL(10,2) DEFAULT NULL COMMENT '平均停留时长环比率(%)',

    `return_rate_start` DECIMAL(10,2) DEFAULT NULL COMMENT '返回率开始区间(%)',
    `return_rate_end` DECIMAL(10,2) DEFAULT NULL COMMENT '返回率结束区间(%)',
    `return_rate_rate` DECIMAL(10,2) DEFAULT NULL COMMENT '返回率环比率(%)',

    `completion_rate_start` DECIMAL(10,2) DEFAULT NULL COMMENT '内容完成率开始区间(%)',
    `completion_rate_end` DECIMAL(10,2) DEFAULT NULL COMMENT '内容完成率结束区间(%)',
    `completion_rate_rate` DECIMAL(10,2) DEFAULT NULL COMMENT '内容完成率环比率(%)',

    `first_screen_ctr_start` DECIMAL(10,2) DEFAULT NULL COMMENT '首屏CTR值开始区间(%)',
    `first_screen_ctr_end` DECIMAL(10,2) DEFAULT NULL COMMENT '首屏CTR值结束区间(%)',
    `first_screen_ctr_rate` DECIMAL(10,2) DEFAULT NULL COMMENT '首屏CTR值环比率(%)',

    `content_jump_rate_start` DECIMAL(10,2) DEFAULT NULL COMMENT '内容Jump率开始区间(%)',
    `content_jump_rate_end` DECIMAL(10,2) DEFAULT NULL COMMENT '内容Jump率结束区间(%)',
    `content_jump_rate_rate` DECIMAL(10,2) DEFAULT NULL COMMENT '内容Jump率环比率(%)',

    `content_return_rate_start` DECIMAL(10,2) DEFAULT NULL COMMENT '内容Return率开始区间(%)',
    `content_return_rate_end` DECIMAL(10,2) DEFAULT NULL COMMENT '内容Return率结束区间(%)',
    `content_return_rate_rate` DECIMAL(10,2) DEFAULT NULL COMMENT '内容Return率环比率(%)',

    `click_depth_start` DECIMAL(10,2) DEFAULT NULL COMMENT 'Click深度分布开始区间(层)',
    `click_depth_end` DECIMAL(10,2) DEFAULT NULL COMMENT 'Click深度分布结束区间(层)',
    `click_depth_rate` DECIMAL(10,2) DEFAULT NULL COMMENT 'Click深度分布环比率(%)',

    `click_interval_time_start` DECIMAL(10,2) DEFAULT NULL COMMENT 'Click间隔时间开始区间(秒)',
    `click_interval_time_end` DECIMAL(10,2) DEFAULT NULL COMMENT 'Click间隔时间结束区间(秒)',
    `click_interval_time_rate` DECIMAL(10,2) DEFAULT NULL COMMENT 'Click间隔时间环比率(%)',

    `engagement_score_start` DECIMAL(10,2) DEFAULT NULL COMMENT 'Engagement得分开始区间(分)',
    `engagement_score_end` DECIMAL(10,2) DEFAULT NULL COMMENT 'Engagement得分结束区间(分)',
    `engagement_score_rate` DECIMAL(10,2) DEFAULT NULL COMMENT 'Engagement得分环比率(%)',

    `page_start_num` INT(11) DEFAULT NULL COMMENT '翻页率比例开始',
    `page_end_num` INT(11) DEFAULT NULL COMMENT '翻页率结束',
    `page_rate` DECIMAL(10,2) DEFAULT NULL COMMENT '翻页率环比率(%)',

    `unique_query_start` INT(11) DEFAULT NULL COMMENT '独立Query数开始区间',
    `unique_query_end` INT(11) DEFAULT NULL COMMENT '独立Query数结束区间',
    `unique_query_rate` DECIMAL(10,2) DEFAULT NULL COMMENT '独立Query数环比率(%)',

    `top3_pv_ctr_start` DECIMAL(10,2) DEFAULT NULL COMMENT 'TOP3 PV-CTR开始区间(%)',
    `top3_pv_ctr_end` DECIMAL(10,2) DEFAULT NULL COMMENT 'TOP3 PV-CTR结束区间(%)',
    `top3_pv_ctr_rate` DECIMAL(10,2) DEFAULT NULL COMMENT 'TOP3 PV-CTR环比率(%)',

    PRIMARY KEY (`id`) USING BTREE,
    INDEX `idx_tenant_link` (`tenant_id`, `link_type`) USING BTREE COMMENT '租户和链接类型索引'
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='H5页面指标配置表';

-- 启用外键检查
SET FOREIGN_KEY_CHECKS = 1;

END;;
DELIMITER ;

-- 执行存储过程
CALL schema_change();

-- 删除存储过程
DROP PROCEDURE IF EXISTS `schema_change`;

-- 恢复外键检查
SET FOREIGN_KEY_CHECKS = 1;