SET FOREIGN_KEY_CHECKS = 0;

DROP PROCEDURE IF EXISTS `schema_change`;
DELIMITER ;;

CREATE PROCEDURE schema_change()
BEGIN

    -- 禁用外键检查
    SET FOREIGN_KEY_CHECKS = 0;

    -- 创建 sys_menu 表（如果不存在）
    CREATE TABLE IF NOT EXISTS `sys_menu` (
      `id` varchar(32) NOT NULL COMMENT '主键',
      `menu_key` varchar(50) NOT NULL COMMENT '菜单键',
      `menu_name` varchar(100) NOT NULL COMMENT '菜单名称',
      `parent_id` varchar(32) DEFAULT NULL COMMENT '父菜单ID',
      `order_num` int(11) DEFAULT '0' COMMENT '排序号',
      `path` varchar(200) DEFAULT NULL COMMENT '路由路径',
      `component` varchar(200) DEFAULT NULL COMMENT '组件路径',
      `is_route` tinyint(1) DEFAULT '1' COMMENT '是否路由菜单: 0:不是  1:是',
      `is_leaf` tinyint(1) DEFAULT '1' COMMENT '是否叶子节点: 0:不是  1:是',
      `is_visible` tinyint(1) DEFAULT '1' COMMENT '是否可见: 0:不可见  1:可见',
      `icon` varchar(100) DEFAULT NULL COMMENT '菜单图标',
      `description` varchar(200) DEFAULT NULL COMMENT '描述',
      `create_by` varchar(32) DEFAULT NULL COMMENT '创建人',
      `create_time` datetime DEFAULT NULL COMMENT '创建时间',
      `update_by` varchar(32) DEFAULT NULL COMMENT '更新人',
      `update_time` datetime DEFAULT NULL COMMENT '更新时间',
      `del_flag` tinyint(1) DEFAULT '0' COMMENT '删除状态: 0:正常  1:已删除',
      PRIMARY KEY (`id`),
      UNIQUE KEY `uk_menu_key` (`menu_key`),
      KEY `idx_parent_id` (`parent_id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='菜单表';

    -- 启用外键检查
    SET FOREIGN_KEY_CHECKS = 1;

END;;
DELIMITER ;

-- 执行存储过程
CALL schema_change();

-- 删除存储过程
DROP PROCEDURE IF EXISTS `schema_change`;

SET FOREIGN_KEY_CHECKS = 1;
