-- 禁用外键检查（预防某些 DDL 报错）
SET FOREIGN_KEY_CHECKS = 0;

-- 删除已有过程（防止重复定义）
DROP PROCEDURE IF EXISTS `schema_change`;
DELIMITER ;;

CREATE PROCEDURE schema_change()
BEGIN
    -- 判断字段是否存在
    IF NOT EXISTS (
        SELECT * FROM information_schema.COLUMNS
        WHERE table_schema = DATABASE()
          AND table_name = 'app_news'
          AND column_name = 'news_time'
    ) THEN
        -- TODO 设置字段可以为 null
ALTER TABLE `app_news`
    MODIFY COLUMN `news_time` timestamp NULL DEFAULT NULL COMMENT '资讯时间';
END IF;
END;;
DELIMITER ;

-- 执行存储过程
CALL schema_change();

-- 删除过程
DROP PROCEDURE IF EXISTS `schema_change`;

-- 恢复外键检查
SET FOREIGN_KEY_CHECKS = 1;
