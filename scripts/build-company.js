/**
 * 多公司版本构建脚本
 * 用于构建不同公司的版本
 * 支持生产环境和测试环境
 */
const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// 获取命令行参数
const args = process.argv.slice(2);
const companyKey = args[0] || 'xudong'; // 默认为旭动
const envType = args[1] || 'production'; // 默认为生产环境，可选值：production, test

// 验证公司标识是否有效
const validCompanies = ['xudong', 'taiyi'];
if (!validCompanies.includes(companyKey)) {
  console.error(`错误: 无效的公司标识 "${companyKey}". 有效选项: ${validCompanies.join(', ')}`);
  process.exit(1);
}

// 验证环境类型是否有效
const validEnvTypes = ['production', 'test'];
if (!validEnvTypes.includes(envType)) {
  console.error(`错误: 无效的环境类型 "${envType}". 有效选项: ${validEnvTypes.join(', ')}`);
  process.exit(1);
}

console.log(`开始构建 ${companyKey} 公司 ${envType} 环境版本...`);

// 更新公司配置文件
const companyConfigPath = path.resolve(process.cwd(), 'src/config/company.ts');
let companyConfigContent = fs.readFileSync(companyConfigPath, 'utf8');

// 替换公司标识
companyConfigContent = companyConfigContent.replace(
  /export const CURRENT_COMPANY = ['"].*['"]/,
  `export const CURRENT_COMPANY = '${companyKey}'`
);

// 写入更新后的公司配置文件
fs.writeFileSync(companyConfigPath, companyConfigContent, 'utf8');
console.log(`已更新公司配置文件，设置当前公司为 ${companyKey}`);

// 更新 package.json 中的公司信息
const packageJsonPath = path.resolve(process.cwd(), 'package.json');
const packageJson = require(packageJsonPath);

// 获取公司配置
const systemConfigPath = path.resolve(process.cwd(), 'src/config/systemConfig.ts');
const systemConfigContent = fs.readFileSync(systemConfigPath, 'utf8');

// 提取公司配置信息
const companyConfigRegex = new RegExp(`${companyKey}:\\s*{([^}]+)}`, 's');
const companyConfigMatch = systemConfigContent.match(companyConfigRegex);

if (!companyConfigMatch) {
  console.error(`无法在 systemConfig.ts 中找到 ${companyKey} 的配置信息`);
  process.exit(1);
}

// 提取公司名称
const nameRegex = /name:\s*['"]([^'"]+)['"]/;
const fullNameRegex = /fullName:\s*['"]([^'"]+)['"]/;
const systemNameRegex = /systemName:\s*['"]([^'"]+)['"]/;

const nameMatch = companyConfigMatch[1].match(nameRegex);
const fullNameMatch = companyConfigMatch[1].match(fullNameRegex);
const systemNameMatch = companyConfigMatch[1].match(systemNameRegex);

const name = nameMatch ? nameMatch[1] : '';
const fullName = fullNameMatch ? fullNameMatch[1] : '';
const systemName = systemNameMatch ? systemNameMatch[1] : '';

// 更新 package.json
if (name && fullName && systemName) {
  packageJson.name = name.toLowerCase().replace(/\s+/g, '-');
  packageJson.description = systemName;
  packageJson.author = `${fullName} <info@${name.toLowerCase().replace(/\s+/g, '')}.com>`;

  // 更新 build 配置
  if (packageJson.build) {
    packageJson.build.appId = `com.${name.toLowerCase().replace(/\s+/g, '')}.enterprise`;
    packageJson.build.productName = systemName;
    packageJson.build.copyright = `Copyright © 2024 ${fullName}`;
  }

  // 写入更新后的 package.json
  fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2), 'utf8');
  console.log(`已更新 package.json 中的公司信息`);
}

// 更新应用图标
try {
  console.log('更新应用图标...');
  execSync(`node scripts/update-app-icon.js ${companyKey}`, { stdio: 'inherit' });
  console.log('应用图标更新成功！');
} catch (error) {
  console.error('更新应用图标失败:', error);
  // 继续构建过程，不中断
}

// 更新环境变量文件
try {
  console.log('更新环境变量文件...');

  // 根据环境类型选择对应的环境变量文件
  const envFilePath = path.resolve(process.cwd(), `.env.${envType}`);
  let envFileContent = fs.readFileSync(envFilePath, 'utf8');

  // 设置公司标识
  envFileContent = envFileContent.replace(
    /REACT_APP_COMPANY=.*/,
    `REACT_APP_COMPANY=${companyKey}`
  );
  envFileContent = envFileContent.replace(
    /COMPANY_KEY=.*/,
    `COMPANY_KEY=${companyKey}`
  );

  // 设置API基础URL
  // 生产环境和测试环境的API地址配置
  const apiUrls = {
    production: {
      taiyi: 'https://www.lunarforge.cn/jeecg-boot',
      xudong: 'https://www.xdinnovation.cn/jeecg-boot'
    },
    test: {
      taiyi: 'http://101.42.109.196:2601/jeecg-boot',
      xudong: 'http://101.42.109.196:3601/jeecg-boot'
    }
  };

  const apiUrl = apiUrls[envType][companyKey] || apiUrls[envType].taiyi;
  envFileContent = envFileContent.replace(
    /VITE_GLOB_DOMAIN_URL=.*/,
    `VITE_GLOB_DOMAIN_URL=${apiUrl}`
  );

  // 写入更新后的环境变量文件
  fs.writeFileSync(envFilePath, envFileContent, 'utf8');
  console.log(`已更新环境变量文件，设置API基础URL为 ${apiUrl}`);
} catch (error) {
  console.error('更新环境变量文件失败:', error);
  // 继续构建过程，不中断
}

// 执行构建命令
try {
  console.log('开始构建项目...');
  // 使用cross-env直接传递环境变量到构建过程
  // 在Jenkins环境中，输出目录可能是'production'
  const isJenkins = process.env.JENKINS_URL || process.env.JENKINS_HOME;
  const outputDir = isJenkins ? 'production' : 'dist';

  // 确保传递公司标识到构建过程
  console.log(`构建 ${companyKey} 公司版本，设置 REACT_APP_COMPANY=${companyKey}`);

  execSync(`cross-env NODE_ENV=${envType} REACT_APP_ENV=${envType} REACT_APP_COMPANY=${companyKey} COMPANY_KEY=${companyKey} max build && node scripts/fix-html-for-production.js ${envType} ${outputDir}`, { stdio: 'inherit' });
  console.log(`${companyKey} 公司 ${envType} 环境版本构建成功！`);
} catch (error) {
  console.error('构建失败:', error);
  process.exit(1);
}
