/**
 * 菜单配置文件
 * 定义不同公司可见的菜单项
 */

/**
 * 菜单配置
 * 注意：菜单键必须与 routes.ts 中的 name 字段一致
 */

// 定义所有可能的菜单项
export const ALL_MENUS = {
  // 首页/仪表盘
  guide: { key: 'guide', title: '首页', icon: 'book' },

  // 企业管理
  corp: { key: 'corp', title: '企业管理', icon: 'table' },

  // 车险台账
  pdLedgerList: { key: 'pdLedgerList', title: '车险台账', icon: 'car' },

  // 财险台账
  pdInsuranceLedgerList: { key: 'pdInsuranceLedgerList', title: '财险台账', icon: 'bank' },

  // 增值服务台账
  pdAddedLedgerList: { key: 'pdAddedLedgerList', title: '增值服务台账', icon: 'gift' },

  // 信息管理
  info: { key: 'info', title: '信息管理', icon: 'file' },

  // 信息源列表
  pdChatSourceList: { key: 'pdChatSourceList', title: '信息源列表', icon: 'message' },

  // 信息源详情列表
  pdChatSourceDetList: { key: 'pdChatSourceDetList', title: '信息源详情列表', icon: 'file-text' },

  // 场景库
  pdSceneList: { key: 'pdSceneList', title: '场景库', icon: 'appstore' },

  // 姓名库
  pdNameList: { key: 'pdNameList', title: '姓名库', icon: 'user' },

  // 采集信息
  pdCarInfo: { key: 'pdCarInfo', title: '采集信息', icon: 'car' },

  // 任务中心
  pdTaskCenter: { key: 'pdTaskCenter', title: '任务中心', icon: 'dashboard' },

  // 系统管理
  system: { key: 'system', title: '系统管理', icon: 'setting' },

  // 完成情况
  completion: { key: 'completion', title: '完成情况', icon: 'check-circle' },

  // 批量系统补全
  batchCompletion: { key: 'batchCompletion', title: '批量系统补全', icon: 'cloud-upload' },

  // 多租户公司配置
  tenantConfig: { key: 'tenantConfig', title: '多租户公司配置', icon: 'team' },

  // 系统配置
  sysDeployConfig: { key: 'sysDeployConfig', title: '系统配置', icon: 'tool' },

  // 默认链接配置
  defaultLinkConfig: { key: 'defaultLinkConfig', title: '默认链接配置', icon: 'link' },

  // 格式转换
  formatConverter: { key: 'formatConverter', title: '格式转换', icon: 'swap' },

  // 定时任务
  quartzJob: { key: 'quartzJob', title: '定时任务', icon: 'clock-circle' },
};

// 定义每个公司可见的菜单
export const COMPANY_MENUS = {
  // 太一公司可见的菜单
  taiyi: [
    // 顶层菜单
    'guide',
    'corp',
    'info',
    'system',

    // 子菜单
    'pdLedgerList',
    'pdInsuranceLedgerList',
    'pdAddedLedgerList',
    'pdChatSourceList',
    'pdChatSourceDetList',
    'pdTaskCenter',
    'pdSceneList',
    'pdNameList',
    'pdCarInfo',
    'completion',
    'batchCompletion',
    'tenantConfig',
    'sysDeployConfig',
    'defaultLinkConfig',
    'formatConverter',
    'quartzJob',
  ],

  // 旭东公司可见的菜单
  xudong: [
    // 顶层菜单
    'guide',
    'corp',
    'info',

    // 子菜单
    'pdLedgerList',
    'pdInsuranceLedgerList',
    'pdAddedLedgerList',

    'pdChatSourceList',
    'pdChatSourceDetList',
    'pdTaskCenter',
    'pdSceneList',
    'pdNameList',
    'pdCarInfo',
  ],

  // 默认菜单（当找不到匹配的公司时使用）
  default: [
    'guide',
  ]
};
