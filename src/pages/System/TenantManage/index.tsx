import React, { useState, useRef } from 'react';
import { PageContainer, ProTable } from '@ant-design/pro-components';
import { Button, message, Modal, Space, Tooltip } from 'antd';
import { PlusOutlined, DeleteOutlined, TeamOutlined, AppstoreOutlined, InboxOutlined } from '@ant-design/icons';
import type { ActionType, ProColumns } from '@ant-design/pro-components';
import { getTenantList, deleteTenant, batchDeleteTenant } from '@/services/system/tenant';
import type { Tenant } from './data';
import { tenantColumns } from './data';
import TenantModal from './components/TenantModal';
import TenantUserModal from './components/TenantUserModal';
import TenantPackModal from './components/TenantPackModal';
import TenantRecycleBinModal from './components/TenantRecycleBinModal';
import TenantInviteUserModal from './components/TenantInviteUserModal';

const TenantManage: React.FC = () => {
  // 表格引用
  const actionRef = useRef<ActionType>();

  // 状态管理
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [tenantModalVisible, setTenantModalVisible] = useState<boolean>(false);
  const [tenantModalTitle, setTenantModalTitle] = useState<string>('新增租户');
  const [currentTenant, setCurrentTenant] = useState<Partial<Tenant>>({});
  const [tenantUserModalVisible, setTenantUserModalVisible] = useState<boolean>(false);
  const [currentTenantId, setCurrentTenantId] = useState<string>('');
  const [tenantPackModalVisible, setTenantPackModalVisible] = useState<boolean>(false);
  const [recycleBinModalVisible, setRecycleBinModalVisible] = useState<boolean>(false);
  const [inviteUserModalVisible, setInviteUserModalVisible] = useState<boolean>(false);

  // 表格列配置
  const columns: ProColumns<Tenant>[] = [
    ...tenantColumns,
    {
      title: '操作',
      valueType: 'option',
      key: 'option',
      width: 200,
      fixed: 'right',
      render: (_, record) => [
        <a key="edit" onClick={() => handleEdit(record)}>
          编辑
        </a>,
        <a key="delete" onClick={() => handleDelete(record.id)}>
          删除
        </a>,
        <a key="users" onClick={() => handleViewUsers(record.id)}>
          用户
        </a>,
      ],
    },
  ];

  // 处理新增租户
  const handleAdd = () => {
    setTenantModalTitle('新增租户');
    setCurrentTenant({});
    setTenantModalVisible(true);
  };

  // 处理编辑租户
  const handleEdit = async (record: Tenant) => {
    setTenantModalTitle('编辑租户');
    try {
      // 先获取完整的租户详情
      console.log('开始获取租户详情, ID:', record.id);
      const response = await getTenantById(record.id);
      console.log('租户详情API返回:', response);

      // 检查API响应格式，适应不同的返回结构
      let detailData = null;
      if (response.success && response.result) {
        detailData = response.result;
      } else if (response.code === 200 && response.data) {
        // 适应另一种可能的API返回格式
        detailData = response.data;
      } else if (response.data && response.data.success && response.data.result) {
        // 再适应另一种可能的API返回格式
        detailData = response.data.result;
      }

      if (detailData) {
        console.log('获取到的租户详情:', detailData);
        // 确保所有需要的字段都存在
        const tenantData = {
          ...record, // 保留表格中的数据作为基础
          ...detailData, // 用API返回的数据覆盖
          id: record.id, // 确保ID正确
        };
        console.log('最终设置的租户数据:', tenantData);
        setCurrentTenant(tenantData);
      } else {
        console.log('未找到有效的租户详情数据，使用表格数据');
        // 不显示错误消息，直接使用表格中的数据
        setCurrentTenant(record);
      }
    } catch (error) {
      console.error('获取租户详情出错:', error);
      // 不显示错误消息，直接使用表格中的数据
      setCurrentTenant(record);
    }
    setTenantModalVisible(true);
  };

  // 处理删除租户
  const handleDelete = (id: string) => {
    Modal.confirm({
      title: '确认删除',
      content: '确定要删除该租户吗？删除后可在回收站恢复。',
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        try {
          const response = await deleteTenant(id);
          if (response.success) {
            message.success('删除成功');
            if (actionRef.current) {
              actionRef.current.reload();
            }
          } else {
            message.error(response.message || '删除失败');
          }
        } catch (error) {
          message.error('删除失败');
        }
      },
    });
  };

  // 处理批量删除
  const handleBatchDelete = () => {
    if (selectedRowKeys.length === 0) {
      message.warning('请选择要删除的租户');
      return;
    }

    Modal.confirm({
      title: '确认批量删除',
      content: `确定要删除选中的 ${selectedRowKeys.length} 个租户吗？删除后可在回收站恢复。`,
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        try {
          const response = await batchDeleteTenant(selectedRowKeys as string[]);
          if (response.success) {
            message.success('批量删除成功');
            setSelectedRowKeys([]);
            if (actionRef.current) {
              actionRef.current.reload();
            }
          } else {
            message.error(response.message || '批量删除失败');
          }
        } catch (error) {
          message.error('批量删除失败');
        }
      },
    });
  };

  // 处理查看用户
  const handleViewUsers = (tenantId: string) => {
    setCurrentTenantId(tenantId);
    setTenantUserModalVisible(true);
  };

  // 处理查看产品包
  const handleViewPacks = () => {
    if (selectedRowKeys.length !== 1) {
      message.warning('请选择一个租户');
      return;
    }
    setCurrentTenantId(selectedRowKeys[0] as string);
    setTenantPackModalVisible(true);
  };

  // 处理邀请用户
  const handleInviteUser = () => {
    if (selectedRowKeys.length !== 1) {
      message.warning('请选择一个租户');
      return;
    }
    setCurrentTenantId(selectedRowKeys[0] as string);
    setInviteUserModalVisible(true);
  };

  // 处理查看回收站
  const handleViewRecycleBin = () => {
    setRecycleBinModalVisible(true);
  };

  // 表格行选择配置
  const rowSelection = {
    selectedRowKeys,
    onChange: (keys: React.Key[]) => {
      setSelectedRowKeys(keys);
    },
  };

  return (
    <PageContainer>
      <ProTable<Tenant>
        headerTitle="租户列表"
        actionRef={actionRef}
        rowKey="id"
        search={{
          labelWidth: 120,
        }}
        toolBarRender={() => [
          <Button
            key="add"
            type="primary"
            onClick={handleAdd}
            icon={<PlusOutlined />}
          >
            新增租户
          </Button>,
          <Button
            key="batchDelete"
            danger
            onClick={handleBatchDelete}
            icon={<DeleteOutlined />}
            disabled={selectedRowKeys.length === 0}
          >
            批量删除
          </Button>,
          <Button
            key="invite"
            onClick={handleInviteUser}
            icon={<TeamOutlined />}
            disabled={selectedRowKeys.length !== 1}
          >
            邀请用户加入
          </Button>,
          <Button
            key="packs"
            onClick={handleViewPacks}
            icon={<AppstoreOutlined />}
            disabled={selectedRowKeys.length !== 1}
          >
            套餐
          </Button>,
          <Button
            key="recycleBin"
            onClick={handleViewRecycleBin}
            icon={<InboxOutlined />}
          >
            回收站
          </Button>,
        ]}
        request={async (params) => {
          // 处理分页参数
          const { current, pageSize, ...rest } = params;
          const response = await getTenantList({
            pageNo: current,
            pageSize,
            ...rest,
          });

          if (response.success) {
            return {
              data: response.result.records || [],
              success: true,
              total: response.result.total || 0,
            };
          }
          return {
            data: [],
            success: false,
            total: 0,
          };
        }}
        columns={columns}
        rowSelection={rowSelection}
        scroll={{ x: 1500 }}
      />

      {/* 租户表单弹窗 */}
      <TenantModal
        visible={tenantModalVisible}
        title={tenantModalTitle}
        onCancel={() => setTenantModalVisible(false)}
        onSuccess={() => {
          setTenantModalVisible(false);
          if (actionRef.current) {
            actionRef.current.reload();
          }
        }}
        values={currentTenant}
      />

      {/* 租户用户弹窗 */}
      <TenantUserModal
        visible={tenantUserModalVisible}
        tenantId={currentTenantId}
        onCancel={() => setTenantUserModalVisible(false)}
      />

      {/* 租户产品包弹窗 */}
      <TenantPackModal
        visible={tenantPackModalVisible}
        tenantId={currentTenantId}
        onCancel={() => setTenantPackModalVisible(false)}
      />

      {/* 租户回收站弹窗 */}
      <TenantRecycleBinModal
        visible={recycleBinModalVisible}
        onCancel={() => setRecycleBinModalVisible(false)}
        onSuccess={() => {
          if (actionRef.current) {
            actionRef.current.reload();
          }
        }}
      />

      {/* 邀请用户弹窗 */}
      <TenantInviteUserModal
        visible={inviteUserModalVisible}
        tenantId={currentTenantId}
        onCancel={() => setInviteUserModalVisible(false)}
        onSuccess={() => {
          setInviteUserModalVisible(false);
          message.success('邀请发送成功');
        }}
      />
    </PageContainer>
  );
};

export default TenantManage;
