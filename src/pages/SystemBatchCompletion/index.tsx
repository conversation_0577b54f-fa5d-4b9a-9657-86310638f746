import React, { useState, useEffect, useRef } from 'react';
import { <PERSON><PERSON>ontainer, ProCard } from '@ant-design/pro-components';
import {
  Form,
  Button,
  Space,
  Typography,
  message,
  Upload,
  Alert,
  Row,
  Col,
  Card,
  Progress,
  Statistic,
  Tabs,
  Table,
  Tag,
  Tooltip,
  DatePicker,
  InputNumber,
  Radio,
  Checkbox,
  Collapse,
  Spin,
} from 'antd';
import {
  FileExcelOutlined,
  InboxOutlined,
  CloudUploadOutlined,
  InfoCircleOutlined,
  ReloadOutlined,
  ClockCircleOutlined,
  UserOutlined,
  BarChartOutlined,
  UploadOutlined,
  SettingOutlined,
  BankOutlined,
  EnvironmentOutlined,
} from '@ant-design/icons';
import type { UploadFile, UploadProps } from 'antd/es/upload/interface';
import type { RcFile } from 'antd/es/upload';
import { uploadBatchSystemCompletionFile, getBatchSystemCompletionProgress, uploadSystemCompletionFiles, DailyConfigDto } from '@/services/corp/tenantConfig';
import { FormCitySelector } from '@/components/CitySelector';
import { cityData } from '@/constants/cityData';
import TenantSelect from '@/components/TenantSelect';
import dayjs from 'dayjs';
import styles from '../SystemCompletion/index.less';

const { Text, Paragraph } = Typography;
const { TabPane } = Tabs;

interface TenantInfo {
  tenantId: number;
  tenantName: string;
  startTime: number;
  endTime?: number;
  duration?: number;
  clickCount?: number;
  status: string;
}

interface TaskProgress {
  totalTasks: number;
  completedTasks: number;
  remainingTasks: number;
  status: string;
  percent: number;
  tenantList: TenantInfo[];
}

const SystemBatchCompletion: React.FC = () => {
  const [form] = Form.useForm();
  const [fileList, setFileList] = useState<UploadFile[]>([]);
  const [submitting, setSubmitting] = useState(false);
  const [uploadSuccess, setUploadSuccess] = useState(false);

  // 任务进度状态
  const [taskProgress, setTaskProgress] = useState<TaskProgress>({
    totalTasks: 0,
    completedTasks: 0,
    remainingTasks: 0,
    status: '',
    percent: 0,
    tenantList: []
  });

  // 当前活动的标签页
  const [activeTab, setActiveTab] = useState('upload');

  // 轮询定时器
  const pollingTimerRef = useRef<NodeJS.Timeout | null>(null);
  const [polling, setPolling] = useState(false);

  // 开始轮询任务进度
  const startPolling = () => {
    if (pollingTimerRef.current) {
      clearInterval(pollingTimerRef.current);
    }

    setPolling(true);

    // 立即执行一次
    fetchTaskProgress();

    // 设置定时器，每3秒轮询一次
    pollingTimerRef.current = setInterval(() => {
      fetchTaskProgress();
    }, 3000);
  };

  // 停止轮询
  const stopPolling = () => {
    if (pollingTimerRef.current) {
      clearInterval(pollingTimerRef.current);
      pollingTimerRef.current = null;
    }
    setPolling(false);
  };

  // 获取任务进度
  const fetchTaskProgress = async () => {
    try {
      const response = await getBatchSystemCompletionProgress();

      if (response && response.success && response.result) {
        const {
          totalTasks,
          completedTasks,
          remainingTasks,
          status,
          tenantList
        } = response.result;

        // 计算百分比
        const percent = totalTasks > 0 ? Math.floor((completedTasks / totalTasks) * 100) : 0;

        setTaskProgress({
          totalTasks,
          completedTasks,
          remainingTasks,
          status,
          percent,
          tenantList: tenantList || []
        });

        // 如果任务已完成或失败，停止轮询
        if (status === '已完成' || status === '处理失败') {
          stopPolling();
        }
      }
    } catch (error) {
      console.error('获取任务进度失败:', error);
      // 出错时不停止轮询，继续尝试
    }
  };

  // 格式化时间戳为可读时间
  const formatTime = (timestamp: number) => {
    return new Date(timestamp).toLocaleString();
  };

  // 格式化持续时间（毫秒转为分钟和秒）
  const formatDuration = (duration: number) => {
    const seconds = Math.floor(duration / 1000);
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}分${remainingSeconds}秒`;
  };

  // 组件挂载时获取任务进度
  useEffect(() => {
    // 页面加载时获取任务进度，无论是否有上传成功的记录
    fetchTaskProgress();
  }, []);

  // 组件卸载时清除定时器
  useEffect(() => {
    return () => {
      if (pollingTimerRef.current) {
        clearInterval(pollingTimerRef.current);
      }
    };
  }, []);

  // 处理表单提交
  const handleSubmit = async () => {
    if (fileList.length === 0) {
      message.warning('请先选择要上传的文件');
      return;
    }

    const file = fileList[0];
    if (!file.originFileObj) {
      message.error('无法获取文件，请重新上传');
      return;
    }

    try {
      setSubmitting(true);

      // 调用API上传文件
      const result = await uploadBatchSystemCompletionFile(file.originFileObj);

      if (result && result.success) {
        setUploadSuccess(true);
        message.success('批量系统补全任务已提交');

        // 开始轮询任务进度
        startPolling();

        // 切换到任务中心页签
        setActiveTab('taskCenter');
      } else {
        message.error(result?.message || '提交失败，请稍后重试');
      }
    } catch (error) {
      console.error('批量系统补全提交错误:', error);
      message.error('提交失败，请稍后重试');
    } finally {
      setSubmitting(false);
    }
  };

  // 重置表单
  const handleReset = () => {
    form.resetFields();
    setFileList([]);
    setUploadSuccess(false);
    stopPolling();
    setTaskProgress({
      totalTasks: 0,
      completedTasks: 0,
      remainingTasks: 0,
      status: '',
      percent: 0,
      tenantList: []
    });
  };

  // 处理文件上传前检查
  const beforeUpload = (file: RcFile) => {
    const isExcelOrCsv =
      file.type === 'application/vnd.ms-excel' ||
      file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
      file.type === 'text/csv' ||
      /\.(xlsx|xls|csv)$/i.test(file.name);

    if (!isExcelOrCsv) {
      message.error('只能上传 Excel 或 CSV 文件!');
      return Upload.LIST_IGNORE;
    }

    const isLt20M = file.size / 1024 / 1024 < 20;
    if (!isLt20M) {
      message.error('文件必须小于 20MB!');
      return Upload.LIST_IGNORE;
    }

    // 限制只能上传一个文件
    setFileList([{
      uid: '1',
      name: file.name,
      status: 'done',
      size: file.size,
      type: file.type,
      originFileObj: file,
    }]);

    return false;
  };

  // 处理文件上传变化
  const handleUploadChange: UploadProps['onChange'] = ({ fileList }) => {
    // 只保留最新的一个文件
    if (fileList.length > 0) {
      setFileList([fileList[fileList.length - 1]]);
    } else {
      setFileList([]);
    }
  };

  // 渲染上传页签内容
  const renderUploadTab = () => (
    <Row gutter={[24, 24]}>
      <Col span={24}>
        <Alert
          message="批量系统补全说明"
          description={
            <div>
              <Paragraph>
                通过上传Excel文件，可以一次性为多个公司配置系统补全数据。请确保您的Excel文件格式正确，包含所有必要的字段。
              </Paragraph>
              <Paragraph>
                <strong>文件要求：</strong>
                <ul>
                  <li>文件格式：Excel (.xlsx, .xls) 或 CSV (.csv)</li>
                  <li>文件大小：不超过20MB</li>
                  <li>每行数据对应一个公司的系统补全配置</li>
                </ul>
              </Paragraph>
            </div>
          }
          type="info"
          showIcon
          icon={<InfoCircleOutlined />}
          style={{ marginBottom: 24 }}
        />
      </Col>

      {/* 文件上传区域 */}
      <Col span={24}>
        <Card title="上传文件">
          <Upload.Dragger
            name="file"
            fileList={fileList}
            beforeUpload={beforeUpload}
            onChange={handleUploadChange}
            onRemove={() => setFileList([])}
            maxCount={1}
            accept=".xlsx,.xls,.csv"
            style={{ marginBottom: 24 }}
            disabled={submitting || uploadSuccess}
          >
            <p className="ant-upload-drag-icon">
              <InboxOutlined />
            </p>
            <p className="ant-upload-text">点击或拖拽文件到此区域上传</p>
            <p className="ant-upload-hint">
              支持单个Excel或CSV文件上传，文件大小不超过20MB
            </p>
          </Upload.Dragger>

          {fileList.length > 0 && (
            <div style={{ marginTop: 16, display: 'flex', alignItems: 'center' }}>
              <Space align="center">
                <FileExcelOutlined style={{ fontSize: 24, color: '#52c41a' }} />
                <div>
                  <Text strong>{fileList[0].name}</Text>
                  <br />
                  <Text type="secondary">
                    {(fileList[0].size! / 1024 / 1024).toFixed(2)} MB
                  </Text>
                </div>
              </Space>
            </div>
          )}

          <div style={{ marginTop: 24, textAlign: 'right' }}>
            <Space>
              <Button onClick={handleReset} disabled={submitting}>重置</Button>
              <Button
                type="primary"
                onClick={handleSubmit}
                disabled={fileList.length === 0 || submitting || uploadSuccess}
                loading={submitting}
                icon={<CloudUploadOutlined />}
              >
                开始批量补全
              </Button>
            </Space>
          </div>
        </Card>
      </Col>

      {/* 上传成功提示 */}
      {uploadSuccess && (
        <Col span={24}>
          <Alert
            message="文件上传成功"
            description={
              <div>
                <p>您的文件已成功上传，系统正在处理中。</p>
                <p>请点击上方的"任务中心"标签页查看处理进度。</p>
              </div>
            }
            type="success"
            showIcon
            action={
              <Button
                type="primary"
                size="small"
                onClick={() => setActiveTab('taskCenter')}
              >
                查看进度
              </Button>
            }
          />
        </Col>
      )}
    </Row>
  );

  // 渲染系统补全页签内容
  const renderSystemCompletionTab = () => {
    const [systemForm] = Form.useForm();
    const [systemSubmitting, setSystemSubmitting] = useState(false);
    const [inputMode, setInputMode] = useState<'count' | 'ratio'>('ratio');
    const [calculatedValue, setCalculatedValue] = useState<number | null>(null);

    // 计算比例或台账数
    const calculateValue = () => {
      const clickNum = systemForm.getFieldValue('clickNum');
      const recordNum = systemForm.getFieldValue('recordNum');

      if (!clickNum || !recordNum) {
        setCalculatedValue(null);
        return;
      }

      if (inputMode === 'count') {
        // 用户输入的是台账数，计算比例
        // 注意：后端期望的是直接的百分比值，1%就是1，12.5%就是12.5
        const ratio = Math.min(100, ((recordNum / clickNum) * 100));
        setCalculatedValue(parseFloat(ratio.toFixed(1)));
      } else {
        // 用户输入的是比例，计算台账数
        // 用户输入的比例值直接传递给后端，不需要除以100
        const count = Math.round((recordNum / 100) * clickNum);
        setCalculatedValue(count);
      }
    };

    // 当点击数或台账数/比例变化时重新计算
    useEffect(() => {
      if (systemForm) {
        calculateValue();
      }
    }, [systemForm?.getFieldValue('clickNum'), systemForm?.getFieldValue('recordNum'), inputMode]);

    // 处理系统补全表单提交
    const handleSystemSubmit = async () => {
      try {
        const values = await systemForm.validateFields();
        setSystemSubmitting(true);

        // 处理日期范围
        const dateRange = values.dateRange || [];
        const startDate = dateRange[0] ? dateRange[0].format('YYYY-MM-DD') : '';
        const endDate = dateRange[1] ? dateRange[1].format('YYYY-MM-DD') : '';

        // 处理城市代码
        const cityCodes = values.provinces ? values.provinces.map((item: string) => {
          const [city] = item.split(':');
          return city;
        }) : [];

        // 计算最终的比例值
        let finalRatio = values.recordNum;
        if (inputMode === 'count') {
          // 如果用户输入的是台账数，转换为比例（保留一位小数）
          const ratio = Math.min(100, ((values.recordNum / values.clickNum) * 100));
          finalRatio = parseFloat(ratio.toFixed(1));
        }
        // 注意：后端期望的是直接的百分比值，1%就是1，12.5%就是12.5

        // 构建提交数据，确保与后端 LedgerImportDTO 类型一致
        const submitData = {
          startDate,
          endDate,
          recordNum: finalRatio.toString(), // 直接传递百分比值，1表示1%，12.5表示12.5%
          clickNum: values.clickNum, // 后端需要 Integer 类型
          tenantId: values.tenantIds, // 租户ID列表，后端需要 List<String>
          cityCodes // 城市代码列表，后端需要 List<String>
        };

        // 调用API
        console.log('提交数据:', submitData);

        // 模拟延迟，让用户感知到提交过程
        await new Promise(resolve => setTimeout(resolve, 1000));

        const result = await uploadSystemCompletionFiles(submitData);

        if (result && result.success) {
          message.success(result.message || '系统补全任务已创建');

          // 开始轮询任务进度
          startPolling();

          // 切换到任务中心页签
          setActiveTab('taskCenter');
        } else {
          message.error(result?.message || '创建系统补全任务失败');
        }
      } catch (error) {
        console.error('系统补全提交错误:', error);
        message.error('提交失败，请检查表单');
      } finally {
        setSystemSubmitting(false);
      }
    };

    return (
      <Row gutter={[24, 24]}>
        <Col span={24}>
          <Alert
            message="系统补全说明"
            description={
              <div>
                <Paragraph>
                  通过设置参数，可以为多个租户生成系统补全数据。请确保填写正确的参数。
                </Paragraph>
                <Paragraph>
                  <strong>参数说明：</strong>
                  <ul>
                    <li>日期范围：生成数据的时间范围</li>
                    <li>台账数量：需要生成的台账数量</li>
                    <li>点击数：每个台账的点击数</li>
                    <li>租户：选择需要生成数据的租户（可多选）</li>
                    <li>城市：选择需要生成数据的城市（可多选）</li>
                  </ul>
                </Paragraph>
              </div>
            }
            type="info"
            showIcon
            icon={<InfoCircleOutlined />}
            style={{ marginBottom: 24 }}
          />
        </Col>

        <Col span={24}>
          <Card title="系统补全配置">
            <Form
              form={systemForm}
              layout="horizontal"
              labelCol={{ span: 4 }}
              wrapperCol={{ span: 8 }}
            >
              <Form.Item
                name="dateRange"
                label="开始/结束日期"
                rules={[{ required: true, message: '请选择日期范围' }]}
              >
                <DatePicker.RangePicker style={{ width: '350px' }} />
              </Form.Item>

              <Form.Item
                name="clickNum"
                label="点击数"
                rules={[{ required: true, message: '请输入点击数' }]}
              >
                <InputNumber
                  style={{ width: '200px' }}
                  min={1}
                  placeholder="请输入点击数"
                  onChange={() => calculateValue()}
                />
              </Form.Item>

              <Form.Item label="输入方式" style={{ marginBottom: 12 }}>
                <Radio.Group
                  value={inputMode}
                  onChange={(e) => {
                    setInputMode(e.target.value);
                    systemForm.setFieldsValue({ recordNum: undefined });
                    setCalculatedValue(null);
                  }}
                  buttonStyle="solid"
                  optionType="button"
                  size="middle"
                >
                  <Radio.Button value="ratio">按比例</Radio.Button>
                  <Radio.Button value="count">按台账数</Radio.Button>
                </Radio.Group>
              </Form.Item>

              <Form.Item
                name="recordNum"
                label={inputMode === 'ratio' ? "台账占比" : "台账数量"}
                rules={[{ required: true, message: inputMode === 'ratio' ? '请输入台账占比' : '请输入台账数量' }]}
                tooltip={inputMode === 'ratio'
                  ? "台账数占点击数的比例，取值范围0.1-100，例如：22.3表示22.3%的点击生成台账"
                  : "生成的台账数量，不能超过点击数"}
              >
                <InputNumber
                  style={{ width: '200px' }}
                  min={inputMode === 'ratio' ? 0.1 : 1}
                  max={inputMode === 'ratio' ? 100 : systemForm.getFieldValue('clickNum')}
                  precision={inputMode === 'ratio' ? 1 : 0}
                  step={inputMode === 'ratio' ? 0.1 : 1}
                  placeholder={inputMode === 'ratio' ? "请输入台账占比" : "请输入台账数量"}
                  onChange={() => calculateValue()}
                  addonAfter={inputMode === 'ratio' ? "%" : "条"}
                />
              </Form.Item>

              {calculatedValue !== null && (
                <Form.Item label="计算结果" style={{ marginBottom: 12 }}>
                  <Tag color="blue" style={{ fontSize: '14px', padding: '2px 8px' }}>
                    {inputMode === 'ratio'
                      ? `${calculatedValue} 条`
                      : `${calculatedValue}%`}
                  </Tag>
                </Form.Item>
              )}

              <Form.Item
                name="tenantIds"
                label="选择租户"
                rules={[{ required: true, message: '请选择租户' }]}
              >
                <TenantSelect
                  mode="multiple"
                  placeholder="请选择租户(可多选)"
                  allowClear
                  style={{ width: '350px' }}
                />
              </Form.Item>

              <Form.Item
                name="provinces"
                label="选择省份/城市"
                rules={[{ required: true, message: '请选择省份/城市' }]}
              >
                <FormCitySelector
                  name="provinces"
                  cityData={cityData}
                  placeholder="请选择省份或城市(可多选)"
                  showSelected={false}
                  required={false}
                  style={{ width: '350px' }}
                />
              </Form.Item>

              <Form.Item wrapperCol={{ offset: 4, span: 8 }}>
                <div style={{ display: 'flex', justifyContent: 'flex-start', gap: '10px' }}>
                  <Button
                    type="primary"
                    onClick={handleSystemSubmit}
                    loading={systemSubmitting}
                  >
                    提交
                  </Button>
                  <Button
                    onClick={() => systemForm.resetFields()}
                  >
                    重置
                  </Button>
                </div>
              </Form.Item>
            </Form>
          </Card>
        </Col>
      </Row>
    );
  };

  // 渲染任务中心页签内容
  const renderTaskCenterTab = () => (
    <Row gutter={[24, 24]}>
      <Col span={24}>
        <Card title="任务处理进度">
          <div style={{ padding: '16px 0' }}>
            <Row gutter={[16, 16]}>
              <Col span={24}>
                <Progress
                  percent={taskProgress.percent}
                  status={
                    taskProgress.status === '处理失败' ? 'exception' :
                    taskProgress.status === '已完成' ? 'success' : 'active'
                  }
                />
              </Col>
              <Col span={24}>
                <Row gutter={16}>
                  <Col span={8}>
                    <Card style={{ textAlign: 'center', background: '#f5f5f5' }}>
                      <Statistic
                        title="总任务数"
                        value={taskProgress.totalTasks}
                        valueStyle={{ color: '#1890ff' }}
                      />
                    </Card>
                  </Col>
                  <Col span={8}>
                    <Card style={{ textAlign: 'center', background: '#f5f5f5' }}>
                      <Statistic
                        title="已完成/处理中"
                        value={taskProgress.completedTasks}
                        valueStyle={{ color: '#52c41a' }}
                      />
                    </Card>
                  </Col>
                  <Col span={8}>
                    <Card style={{ textAlign: 'center', background: '#f5f5f5' }}>
                      <Statistic
                        title="剩余任务"
                        value={taskProgress.remainingTasks}
                        valueStyle={{ color: '#faad14' }}
                      />
                    </Card>
                  </Col>
                </Row>
              </Col>
              <Col span={24}>
                <Alert
                  message={`当前状态: ${taskProgress.status || '处理中'}`}
                  type={
                    taskProgress.status === '处理失败' ? 'error' :
                    taskProgress.status === '已完成' ? 'success' : 'info'
                  }
                  showIcon
                />
              </Col>
            </Row>
          </div>
        </Card>
      </Col>

      {/* 租户处理列表 */}
      <Col span={24}>
        <Card title="租户处理列表">
          <Table
            dataSource={taskProgress.tenantList
              .map((item, index) => ({
                ...item,
                key: index,
              }))}
            rowKey="key"
            sortDirections={['ascend', 'descend']}
            showSorterTooltip={true}
            columns={[
              {
                title: '租户ID',
                dataIndex: 'tenantId',
                key: 'tenantId',
                sorter: (a, b) => (a.tenantId || 0) - (b.tenantId || 0),
              },
              {
                title: '租户名称',
                dataIndex: 'tenantName',
                key: 'tenantName',
                sorter: (a, b) => {
                  const aName = a.tenantName || '';
                  const bName = b.tenantName || '';
                  return aName.localeCompare(bName);
                },
              },
              {
                title: '开始时间',
                dataIndex: 'startTime',
                key: 'startTime',
                sorter: (a, b) => (a.startTime || 0) - (b.startTime || 0),
                render: (text) => {
                  if (!text || isNaN(new Date(text).getTime())) {
                    return '-';
                  }
                  return formatTime(text);
                },
              },
              {
                title: '结束时间',
                dataIndex: 'endTime',
                key: 'endTime',
                sorter: (a, b) => {
                  const aTime = a.endTime || 0;
                  const bTime = b.endTime || 0;
                  return aTime - bTime;
                },
                render: (text) => {
                  if (!text || isNaN(new Date(text).getTime())) {
                    return '-';
                  }
                  return formatTime(text);
                },
              },
              {
                title: '处理耗时',
                dataIndex: 'duration',
                key: 'duration',
                sorter: (a, b) => {
                  const aDuration = a.duration || 0;
                  const bDuration = b.duration || 0;
                  return aDuration - bDuration;
                },
                render: (text) => {
                  if (!text || isNaN(Number(text))) {
                    return '-';
                  }
                  return formatDuration(text);
                },
              },
              {
                title: '点击数',
                dataIndex: 'clickCount',
                key: 'clickCount',
                sorter: (a, b) => (a.clickCount || 0) - (b.clickCount || 0),
                render: (text) => {
                  if (!text || isNaN(Number(text))) {
                    return '-';
                  }
                  return (
                    <Tag color="blue">
                      <BarChartOutlined /> {text}
                    </Tag>
                  );
                },
              },
              {
                title: (
                  <span>
                    预估耗时
                    <Tooltip title="1个点击耗时按照1.64ms为预估耗时，不为准确耗时">
                      <InfoCircleOutlined style={{ marginLeft: 4 }} />
                    </Tooltip>
                  </span>
                ),
                dataIndex: 'clickCount',
                key: 'estimatedDuration',
                sorter: (a, b) => {
                  const aValue = a.clickCount || 0;
                  const bValue = b.clickCount || 0;
                  return aValue - bValue;
                },
                render: (clickCount) => {
                  if (!clickCount || isNaN(Number(clickCount))) return '-';
                  // 计算预估耗时：点击数 × 1.64 ms
                  const estimatedMs = Number(clickCount) * 1.64;
                  // 转换为更易读的格式
                  const seconds = Math.floor(estimatedMs / 1000);
                  const minutes = Math.floor(seconds / 60);
                  const remainingSeconds = seconds % 60;

                  return <span>{minutes}分{remainingSeconds}秒</span>;
                },
              },
              {
                title: '状态',
                dataIndex: 'status',
                key: 'status',
                defaultSortOrder: 'ascend',
                sorter: (a, b) => {
                  const statusOrder: Record<string, number> = { '处理中': 0, '排队中': 1, '已完成': 2 };
                  return (statusOrder[a.status as string] || 999) - (statusOrder[b.status as string] || 999);
                },
                render: (text) => {
                  let color = 'default';
                  switch (text) {
                    case '处理中':
                      color = 'processing';
                      break;
                    case '已完成':
                      color = 'success';
                      break;
                    case '排队中':
                      color = 'warning';
                      break;
                    default:
                      color = 'default';
                  }
                  return (
                    <Tag color={color}>
                      {text}
                    </Tag>
                  );
                },
              },
            ]}
            pagination={false}
            size="middle"
          />
        </Card>
      </Col>
    </Row>
  );

  return (
    <PageContainer title="批量系统补全">
      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane tab="表格补全" key="upload">
          {renderUploadTab()}
        </TabPane>
        <TabPane tab="系统补全" key="systemCompletion">
          {renderSystemCompletionTab()}
        </TabPane>
        <TabPane tab="任务中心" key="taskCenter">
          {renderTaskCenterTab()}
        </TabPane>
      </Tabs>
    </PageContainer>
  );
};

export default SystemBatchCompletion;
