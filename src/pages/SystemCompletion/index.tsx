import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>ontainer, ProCard } from '@ant-design/pro-components';
import {
  Form,
  DatePicker,
  InputNumber,
  Button,
  Checkbox,
  Space,
  Row,
  Col,
  Typography,
  Divider,
  message,
  Upload,
  Spin,
  Tabs,
  Tooltip,
  Card,
  Collapse,
} from 'antd';
import {
  UploadOutlined,
  FileExcelOutlined,
  FileTextOutlined,
  EnvironmentOutlined,
  BankOutlined,
  InfoCircleOutlined,
  SettingOutlined,
  CalendarOutlined,
  BarChartOutlined,
} from '@ant-design/icons';
import { FormCitySelector } from '@/components/CitySelector';
import { cityData } from '@/constants/cityData';
import type { UploadFile, UploadProps } from 'antd/es/upload/interface';
import type { RcFile } from 'antd/es/upload';
import { uploadSystemCompletionFiles, DailyConfigDto } from '@/services/corp/tenantConfig';
import TenantSelect from '@/components/TenantSelect';
import dayjs from 'dayjs';
import styles from './index.less';

const { RangePicker } = DatePicker;
const { Title, Text } = Typography;

const SystemCompletion: React.FC = () => {
  const [form] = Form.useForm();
  const [selectedLedgerTypes, setSelectedLedgerTypes] = useState<string[]>(['car']);
  const [fileList, setFileList] = useState<UploadFile[]>([]);
  const [uploading, setUploading] = useState(false);
  const [submitting, setSubmitting] = useState(false);

  // 控制折叠面板的展开状态
  const [activeCollapseKeys, setActiveCollapseKeys] = useState<string[]>(['car']);

  // 处理表单提交
  const handleSubmit = async (values: any) => {
    console.log('提交的表单数据:', values);
    console.log('上传的文件:', fileList);

    // 文件不再是必选的，但如果有文件，需要确保已上传完成
    if (fileList.length > 0) {
      const allUploaded = fileList.every(file => file.status === 'done');
      if (!allUploaded) {
        message.warning('请等待所有文件上传完成');
        return;
      }
    }

    // 检查是否选择了租户
    if (!values.tenantId) {
      message.warning('请选择需要补全数据的公司');
      return;
    }

    // 检查是否选择了至少一种台账类型
    const hasCarInsurance = values.carInsurance?.enabled;
    const hasPropertyInsurance = values.propertyInsurance?.enabled;
    const hasValueAddedService = values.valueAddedService?.enabled;

    if (!hasCarInsurance && !hasPropertyInsurance && !hasValueAddedService) {
      message.warning('请至少选择一种台账类型');
      return;
    }

    try {
      setSubmitting(true);

      // 获取实际的文件对象
      const files: File[] = [];

      // 如果有文件，则收集文件对象
      if (fileList.length > 0) {
        for (const file of fileList) {
          if (file.originFileObj) {
            files.push(file.originFileObj);
          }
        }

        // 如果有文件列表但无法获取文件对象，显示错误
        if (fileList.length > 0 && files.length === 0) {
          message.error('无法获取文件，请重新上传');
          setSubmitting(false);
          return;
        }
      }

      // 构建配置JSON
      const configJson: any = {};

      // 添加台账类型配置 - 按照后端期望的格式构建
      // 车险台账 - carLedger
      if (values.carInsurance?.enabled) {
        configJson.carLedger = {
          ledgerStart: values.carInsurance.ledgerPercentMin,
          ledgerEnd: values.carInsurance.ledgerPercentMax,
          chatUserStart: values.carInsurance.chatPercentMin,
          chatUserEnd: values.carInsurance.chatPercentMax,
        };
      }

      // 财险台账 - financeLedger
      if (values.propertyInsurance?.enabled) {
        configJson.financeLedger = {
          ledgerStart: values.propertyInsurance.ledgerPercentMin,
          ledgerEnd: values.propertyInsurance.ledgerPercentMax,
          chatUserStart: values.propertyInsurance.chatPercentMin,
          chatUserEnd: values.propertyInsurance.chatPercentMax,
        };
      }

      // 增值服务台账 - valueAddedLedger
      if (values.valueAddedService?.enabled) {
        configJson.valueAddedLedger = {
          ledgerStart: values.valueAddedService.ledgerPercentMin,
          ledgerEnd: values.valueAddedService.ledgerPercentMax,
          chatUserStart: values.valueAddedService.chatPercentMin,
          chatUserEnd: values.valueAddedService.chatPercentMax,
        };
      }

      // 添加城市配置 - 转换为后端期望的格式
      if (values.cityList && values.cityList.length > 0) {
        // 将城市字符串数组转换为对象数组，格式为 [{city: "城市名", type: 类型值}]
        configJson.cityList = values.cityList.map((item: string) => {
          const [city, type] = item.split(':');
          return {
            city,
            type: parseInt(type, 10)
          };
        });
      }

      // 构建DTO对象
      const dto: DailyConfigDto = {
        configJson: JSON.stringify(configJson),
        clickNum: values.h5Clicks,
        // 添加租户ID
        tenantId: values.tenantId,
        // 根据选中的台账类型设置linkType
        linkType: values.carInsurance?.enabled ? 0 : (values.propertyInsurance?.enabled ? 1 : 2),
        // 如果有日期范围，添加开始和结束日期
        monthDataStart: values.dateRange?.[0] ? dayjs(values.dateRange[0]).format('YYYY-MM-DD') : undefined,
        monthDataEnd: values.dateRange?.[1] ? dayjs(values.dateRange[1]).format('YYYY-MM-DD') : undefined,
      };

      // 调用API上传文件和配置
      const result = await uploadSystemCompletionFiles({
        files,
        ...dto
      });

      if (result && result.success) {
        message.success('系统补全任务已提交');
        // 清空文件列表
        setFileList([]);
        // 重置表单
        form.resetFields();
      } else {
        message.error(result?.message || '提交失败，请稍后重试');
      }
    } catch (error) {
      console.error('系统补全提交错误:', error);
      message.error('提交失败，请稍后重试');
    } finally {
      setSubmitting(false);
    }
  };

  // 处理台账类型选择变化
  const handleLedgerTypeChange = (checkedValues: string[]) => {
    setSelectedLedgerTypes(checkedValues);
    // 根据勾选状态更新折叠面板的展开状态
    setActiveCollapseKeys(checkedValues);
  };

  // 监听表单字段变化，自动展开/折叠对应的区间设置
  useEffect(() => {
    const values = form.getFieldsValue();
    const activeKeys: string[] = [];

    // 根据各台账类型的启用状态决定是否展开对应的折叠面板
    if (values.carInsurance?.enabled) {
      activeKeys.push('car');
    }
    if (values.propertyInsurance?.enabled) {
      activeKeys.push('property');
    }
    if (values.valueAddedService?.enabled) {
      activeKeys.push('valueAdded');
    }

    setActiveCollapseKeys(activeKeys);
  }, [form]);

  // 处理文件上传变化
  const handleUploadChange: UploadProps['onChange'] = ({ fileList }) => {
    // 限制最多3个文件
    // 如果文件数量超过3个，保留最新的文件
    if (fileList.length > 3) {
      // 按照上传时间排序，保留最新的三个文件
      const sortedFiles = [...fileList].sort((a, b) => {
        // 如果文件正在上传，优先保留
        if (a.status === 'uploading') return -1;
        if (b.status === 'uploading') return 1;
        // 否则按uid排序（这里假设较新的文件有较大的uid）
        return (b.uid || '').localeCompare(a.uid || '');
      });
      const limitedFileList = sortedFiles.slice(0, 3);
      setFileList(limitedFileList);
      // 如果文件数量超过3个，显示提示
      if (fileList.length > 3) {
        message.warning('最多只能上传3个文件，已自动保留最新的文件');
      }
    } else {
      setFileList(fileList);
    }
  };

  // 处理文件上传前检查
  const beforeUpload = (file: UploadFile) => {
    const isExcelOrCsv =
      file.type === 'application/vnd.ms-excel' ||
      file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
      file.type === 'text/csv' ||
      /\.(xlsx|xls|csv)$/i.test(file.name);

    if (!isExcelOrCsv) {
      message.error('只能上传 Excel 或 CSV 文件!');
    }

    const isLt10M = file.size / 1024 / 1024 < 10;
    if (!isLt10M) {
      message.error('文件必须小于 10MB!');
    }

    return isExcelOrCsv && isLt10M;
  };

  // 自定义上传操作 - 只在本地保存文件，不实际上传到服务器
  const customUpload = async (options: any) => {
    const { file, onSuccess, onError } = options;
    setUploading(true);

    try {
      // 检查是否已经达到最大文件数
      // 注意：这里需要考虑当前正在上传的文件，所以判断条件应该是 fileList.length > 3
      if (fileList.length >= 3) {
        // 检查是否是替换现有文件
        const isReplacing = fileList.some(f => f.uid === file.uid);
        if (!isReplacing) {
          message.warning('最多只能上传3个文件');
          onError('最多只能上传3个文件');
          setUploading(false);
          return;
        }
      }

      // 这里只是在本地保存文件，不实际上传到服务器
      // 在表单提交时一次性上传所有文件
      setTimeout(() => {
        onSuccess("文件已选择");
        message.success(`${file.name} 文件已选择`);
        setUploading(false);
      }, 500);
    } catch (err) {
      onError();
      message.error('文件选择失败');
      setUploading(false);
    }
  };

  return (
    <PageContainer title="系统补全">
      <ProCard className={styles.mainContainer}>
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          initialValues={{
            h5Clicks: 10000,
            carInsurance: {
              enabled: true,
              ledgerPercentMin: 45,
              ledgerPercentMax: 55,
              chatPercentMin: 25,
              chatPercentMax: 35,
            },
            propertyInsurance: {
              enabled: false,
              ledgerPercentMin: 25,
              ledgerPercentMax: 35,
              chatPercentMin: 25,
              chatPercentMax: 35,
            },
            valueAddedService: {
              enabled: false,
              ledgerPercentMin: 20,
              ledgerPercentMax: 30,
              chatPercentMin: 25,
              chatPercentMax: 35,
            },
            cityList: ['北京市:1', '上海市:1', '深圳市:2'],
            // 租户ID初始值为空，需要用户选择
            tenantId: undefined,
          }}
        >
          {/* 数据导入选项 */}
          <ProCard
            title={
              <Space>
                <UploadOutlined />
                <span>数据导入选项</span>
              </Space>
            }
            className={styles.sectionCard}
            headerBordered
            collapsible
          >
            <Upload
              name="file"
              fileList={fileList}
              onChange={handleUploadChange}
              beforeUpload={beforeUpload}
              customRequest={customUpload}
              multiple={true}
              accept=".xlsx,.xls,.csv"
            >
              <Button type="primary" icon={<UploadOutlined />} loading={uploading}>
                导入表格数据
              </Button>
              <Text type="secondary" style={{ marginLeft: 12 }}>
                上传Excel或CSV文件导入数据（最多3个）
              </Text>
            </Upload>

            {fileList.length > 0 && (
              <div className={styles.uploadedFiles}>
                <Divider style={{ margin: '8px 0' }} />
                <Row gutter={[8, 8]}>
                  {fileList.map((file, index) => (
                    <Col span={24} key={file.uid || index}>
                      <div className={styles.fileItem}>
                        {file.type?.includes('excel') || file.name.endsWith('.xlsx') || file.name.endsWith('.xls') ? (
                          <FileExcelOutlined className={styles.fileIcon} style={{ color: '#52c41a' }} />
                        ) : (
                          <FileTextOutlined className={styles.fileIcon} style={{ color: '#1890ff' }} />
                        )}
                        <Text strong>{file.name}</Text>
                        {file.status === 'done' && (
                          <Text type="success" style={{ marginLeft: 8 }}>
                            已上传
                          </Text>
                        )}
                        {file.status === 'uploading' && (
                          <Text type="warning" style={{ marginLeft: 8 }}>
                            上传中...
                          </Text>
                        )}
                      </div>
                    </Col>
                  ))}
                </Row>
              </div>
            )}
          </ProCard>

          {/* 系统补全设置 */}
          <ProCard
            title={
              <Space>
                <SettingOutlined />
                <span>系统补全设置</span>
              </Space>
            }
            className={styles.sectionCard}
            headerBordered
            style={{ marginTop: 16 }}
          >
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  label="选择公司"
                  name="tenantId"
                  rules={[{ required: true, message: '请选择需要补全数据的公司' }]}
                  tooltip="选择需要进行系统补全的公司"
                >
                  <TenantSelect placeholder="请选择公司" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  label="H5 点击数"
                  name="h5Clicks"
                  rules={[{ required: true, message: '请输入H5点击数' }]}
                  tooltip="设置本次补全所基于的总H5点击数"
                >
                  <InputNumber
                    style={{ width: '100%' }}
                    min={1}
                    placeholder="请输入H5点击数"
                    formatter={value => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                    parser={value => value!.replace(/\$\s?|(,*)/g, '')}
                  />
                </Form.Item>
              </Col>
            </Row>
            <Form.Item
              label="选择日期范围"
              name="dateRange"
              rules={[{ required: true, message: '请选择日期范围' }]}
            >
              <RangePicker
                style={{ width: '100%' }}
                placeholder={['开始日期', '结束日期']}
                allowClear
              />
            </Form.Item>
          </ProCard>

          {/* 台账类型选择 */}
          <ProCard
            title={
              <Space>
                <BankOutlined />
                <span>台账类型选择</span>
              </Space>
            }
            className={styles.sectionCard}
            headerBordered
            style={{ marginTop: 16 }}
          >
            <Form.Item
              name="ledgerTypes"
              rules={[{ required: true, message: '请至少选择一种台账类型' }]}
            >
              <Checkbox.Group onChange={handleLedgerTypeChange}>
                <Collapse
                  className={styles.ledgerCollapse}
                  bordered={false}
                  activeKey={activeCollapseKeys}
                >
                  {/* 车险台账 */}
                  <Collapse.Panel
                    key="car"
                    header={
                      <div style={{ display: 'flex', alignItems: 'center' }}>
                        <Form.Item name={['carInsurance', 'enabled']} valuePropName="checked" noStyle>
                          <Checkbox
                            value="car"
                            onChange={(e) => {
                              // 更新折叠面板状态
                              if (e.target.checked) {
                                setActiveCollapseKeys(prev => [...prev.filter(k => k !== 'car'), 'car']);
                              } else {
                                setActiveCollapseKeys(prev => prev.filter(k => k !== 'car'));
                              }
                            }}
                          >
                            车险台账
                          </Checkbox>
                        </Form.Item>
                        <Text type="secondary" style={{ marginLeft: 24 }}>包含车辆保险相关记录</Text>
                      </div>
                    }
                    className={styles.collapsePanel}
                  >
                    <Row gutter={[16, 8]}>
                      <Col span={24}>
                        <Card size="small" className={styles.compactCard}>
                          <Row gutter={16}>
                            <Col span={12}>
                              <div className={styles.percentSetting}>
                                <Text strong>台账数占比区间 (%)</Text>
                                <Space>
                                  <Form.Item name={['carInsurance', 'ledgerPercentMin']} noStyle>
                                    <InputNumber min={0} max={100} />
                                  </Form.Item>
                                  <span>至</span>
                                  <Form.Item name={['carInsurance', 'ledgerPercentMax']} noStyle>
                                    <InputNumber min={0} max={100} />
                                  </Form.Item>
                                  <span>%</span>
                                </Space>
                                <Text type="secondary" style={{ fontSize: 12 }}>此类型台账数占总H5点击数的比例范围</Text>
                              </div>
                            </Col>
                            <Col span={12}>
                              <div className={styles.percentSetting}>
                                <Text strong>聊天用户数占比区间 (%)</Text>
                                <Space>
                                  <Form.Item name={['carInsurance', 'chatPercentMin']} noStyle>
                                    <InputNumber min={0} max={100} />
                                  </Form.Item>
                                  <span>至</span>
                                  <Form.Item name={['carInsurance', 'chatPercentMax']} noStyle>
                                    <InputNumber min={0} max={100} />
                                  </Form.Item>
                                  <span>%</span>
                                </Space>
                                <Text type="secondary" style={{ fontSize: 12 }}>聊天用户数占此类型台账数的比例范围</Text>
                              </div>
                            </Col>
                          </Row>
                        </Card>
                      </Col>
                    </Row>
                  </Collapse.Panel>

                  {/* 财险台账 */}
                  <Collapse.Panel
                    key="property"
                    header={
                      <div style={{ display: 'flex', alignItems: 'center' }}>
                        <Form.Item name={['propertyInsurance', 'enabled']} valuePropName="checked" noStyle>
                          <Checkbox
                            value="property"
                            onChange={(e) => {
                              // 更新折叠面板状态
                              if (e.target.checked) {
                                setActiveCollapseKeys(prev => [...prev.filter(k => k !== 'property'), 'property']);
                              } else {
                                setActiveCollapseKeys(prev => prev.filter(k => k !== 'property'));
                              }
                            }}
                          >
                            财险台账
                          </Checkbox>
                        </Form.Item>
                        <Text type="secondary" style={{ marginLeft: 24 }}>包含财产保险相关记录</Text>
                      </div>
                    }
                    className={styles.collapsePanel}
                  >
                    <Row gutter={[16, 8]}>
                      <Col span={24}>
                        <Card size="small" className={styles.compactCard}>
                          <Row gutter={16}>
                            <Col span={12}>
                              <div className={styles.percentSetting}>
                                <Text strong>台账数占比区间 (%)</Text>
                                <Space>
                                  <Form.Item name={['propertyInsurance', 'ledgerPercentMin']} noStyle>
                                    <InputNumber min={0} max={100} />
                                  </Form.Item>
                                  <span>至</span>
                                  <Form.Item name={['propertyInsurance', 'ledgerPercentMax']} noStyle>
                                    <InputNumber min={0} max={100} />
                                  </Form.Item>
                                  <span>%</span>
                                </Space>
                                <Text type="secondary" style={{ fontSize: 12 }}>此类型台账数占总H5点击数的比例范围</Text>
                              </div>
                            </Col>
                            <Col span={12}>
                              <div className={styles.percentSetting}>
                                <Text strong>聊天用户数占比区间 (%)</Text>
                                <Space>
                                  <Form.Item name={['propertyInsurance', 'chatPercentMin']} noStyle>
                                    <InputNumber min={0} max={100} />
                                  </Form.Item>
                                  <span>至</span>
                                  <Form.Item name={['propertyInsurance', 'chatPercentMax']} noStyle>
                                    <InputNumber min={0} max={100} />
                                  </Form.Item>
                                  <span>%</span>
                                </Space>
                                <Text type="secondary" style={{ fontSize: 12 }}>聊天用户数占此类型台账数的比例范围</Text>
                              </div>
                            </Col>
                          </Row>
                        </Card>
                      </Col>
                    </Row>
                  </Collapse.Panel>

                  {/* 增值服务台账 */}
                  <Collapse.Panel
                    key="valueAdded"
                    header={
                      <div style={{ display: 'flex', alignItems: 'center' }}>
                        <Form.Item name={['valueAddedService', 'enabled']} valuePropName="checked" noStyle>
                          <Checkbox
                            value="valueAdded"
                            onChange={(e) => {
                              // 更新折叠面板状态
                              if (e.target.checked) {
                                setActiveCollapseKeys(prev => [...prev.filter(k => k !== 'valueAdded'), 'valueAdded']);
                              } else {
                                setActiveCollapseKeys(prev => prev.filter(k => k !== 'valueAdded'));
                              }
                            }}
                          >
                            增值服务台账
                          </Checkbox>
                        </Form.Item>
                        <Text type="secondary" style={{ marginLeft: 24 }}>包含增值服务相关记录</Text>
                      </div>
                    }
                    className={styles.collapsePanel}
                  >
                    <Row gutter={[16, 8]}>
                      <Col span={24}>
                        <Card size="small" className={styles.compactCard}>
                          <Row gutter={16}>
                            <Col span={12}>
                              <div className={styles.percentSetting}>
                                <Text strong>台账数占比区间 (%)</Text>
                                <Space>
                                  <Form.Item name={['valueAddedService', 'ledgerPercentMin']} noStyle>
                                    <InputNumber min={0} max={100} />
                                  </Form.Item>
                                  <span>至</span>
                                  <Form.Item name={['valueAddedService', 'ledgerPercentMax']} noStyle>
                                    <InputNumber min={0} max={100} />
                                  </Form.Item>
                                  <span>%</span>
                                </Space>
                                <Text type="secondary" style={{ fontSize: 12 }}>此类型台账数占总H5点击数的比例范围</Text>
                              </div>
                            </Col>
                            <Col span={12}>
                              <div className={styles.percentSetting}>
                                <Text strong>聊天用户数占比区间 (%)</Text>
                                <Space>
                                  <Form.Item name={['valueAddedService', 'chatPercentMin']} noStyle>
                                    <InputNumber min={0} max={100} />
                                  </Form.Item>
                                  <span>至</span>
                                  <Form.Item name={['valueAddedService', 'chatPercentMax']} noStyle>
                                    <InputNumber min={0} max={100} />
                                  </Form.Item>
                                  <span>%</span>
                                </Space>
                                <Text type="secondary" style={{ fontSize: 12 }}>聊天用户数占此类型台账数的比例范围</Text>
                              </div>
                            </Col>
                          </Row>
                        </Card>
                      </Col>
                    </Row>
                  </Collapse.Panel>
                </Collapse>
              </Checkbox.Group>
            </Form.Item>
          </ProCard>

          {/* 业务城市 */}
          <ProCard
            title={
              <Space>
                <EnvironmentOutlined />
                <span>业务城市</span>
                <Text type="secondary" style={{ fontSize: 12 }}>（可选）</Text>
              </Space>
            }
            className={styles.sectionCard}
            headerBordered
            style={{ marginTop: 16 }}
            collapsible
          >
            <FormCitySelector
              name="cityList"
              cityData={cityData}
              placeholder="请选择城市"
              showSelected={false}
              required={false}
            />
          </ProCard>

          {/* 提交按钮 */}
          <ProCard
            className={styles.actionCard}
            style={{ marginTop: 16 }}
          >
            <div style={{ display: 'flex', justifyContent: 'flex-end' }}>
              <Space>
                <Button onClick={() => form.resetFields()}>重置</Button>
                <Button
                  type="primary"
                  htmlType="submit"
                  loading={submitting}
                  disabled={uploading}
                  icon={<BarChartOutlined />}
                >
                  开始补全
                </Button>
              </Space>
            </div>
          </ProCard>

          {/* 加载状态 */}
          {submitting && (
            <div style={{ position: 'absolute', top: 0, left: 0, right: 0, bottom: 0, background: 'rgba(255,255,255,0.6)', display: 'flex', justifyContent: 'center', alignItems: 'center', zIndex: 1000 }}>
              <Spin size="large" tip="正在提交系统补全任务，请稍候..." />
            </div>
          )}
        </Form>
      </ProCard>
    </PageContainer>
  );
};

export default SystemCompletion;
