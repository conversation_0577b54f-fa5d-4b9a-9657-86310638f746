import React, { useState, useEffect } from 'react';
import { Page<PERSON>ontainer, ProCard } from '@ant-design/pro-components';
import {
  Form,
  DatePicker,
  InputNumber,
  Button,
  Checkbox,
  Space,
  Row,
  Col,
  Typography,
  Divider,
  message,
  Upload,
  Spin,
  Tooltip,
  Card,
  Tag,
  Empty,
} from 'antd';
import {
  UploadOutlined,
  FileExcelOutlined,
  FileTextOutlined,
  EnvironmentOutlined,
  BankOutlined,
  InfoCircleOutlined,
  SettingOutlined,
  CalendarOutlined,
  BarChartOutlined,
  PercentageOutlined,
  CalculatorOutlined,
  CarOutlined,
  GiftOutlined,
} from '@ant-design/icons';
import { FormCitySelector } from '@/components/CitySelector';
import { cityData } from '@/constants/cityData';
import type { UploadFile, UploadProps } from 'antd/es/upload/interface';
import type { RcFile } from 'antd/es/upload';
import { uploadSystemCompletionFiles, DailyConfigDto } from '@/services/corp/tenantConfig';
import TenantSelect from '@/components/TenantSelect';
import dayjs from 'dayjs';

const { RangePicker } = DatePicker;
const { Text } = Typography;

interface CityRatio {
  cityCode: string;
  cityName: string;
  ratio: number;
}

const SystemCompletion: React.FC = () => {
  const [form] = Form.useForm();
  const [uploading, setUploading] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [fileList, setFileList] = useState<UploadFile[]>([]);
  const [cityRatios, setCityRatios] = useState<CityRatio[]>([]);
  const [showCityRatios, setShowCityRatios] = useState(false);
  const [activeCollapseKeys, setActiveCollapseKeys] = useState<string[]>(['car']);

  // 处理表单值变化
  const handleFormValuesChange = (changedValues: any, allValues: any) => {
    // 这里可以添加实时计算逻辑
  };

  // 处理城市选择变化
  const handleCityChange = (selectedCities: string[]) => {
    if (selectedCities.length > 0) {
      const newCityRatios = selectedCities.map(cityCode => {
        const existingRatio = cityRatios.find(r => r.cityCode === cityCode);
        const cityInfo = cityData.find(c => c.value === cityCode);
        return {
          cityCode,
          cityName: cityInfo?.label || cityCode,
          ratio: existingRatio?.ratio || 0,
        };
      });
      setCityRatios(newCityRatios);
      setShowCityRatios(true);
    } else {
      setCityRatios([]);
      setShowCityRatios(false);
    }
  };

  // 处理城市配比变化
  const handleCityRatioChange = (cityCode: string, ratio: number) => {
    setCityRatios(prev => 
      prev.map(city => 
        city.cityCode === cityCode ? { ...city, ratio } : city
      )
    );
  };

  // 计算总配比
  const totalRatio = cityRatios.reduce((sum, city) => sum + (city.ratio || 0), 0);

  // 处理文件上传
  const beforeUpload = (file: RcFile) => {
    const isValidType = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
                       file.type === 'application/vnd.ms-excel' ||
                       file.type === 'text/csv';
    if (!isValidType) {
      message.error('只能上传 Excel 或 CSV 文件！');
      return false;
    }
    const isLt10M = file.size / 1024 / 1024 < 10;
    if (!isLt10M) {
      message.error('文件大小不能超过 10MB！');
      return false;
    }
    return false; // 阻止自动上传
  };

  const customUpload = async ({ file, onSuccess, onError }: any) => {
    try {
      setUploading(true);
      // 这里应该调用实际的上传API
      // const response = await uploadSystemCompletionFiles(file);
      setTimeout(() => {
        onSuccess('ok');
        message.success(`${file.name} 文件上传成功`);
        setUploading(false);
      }, 1000);
    } catch (error) {
      onError(error);
      message.error(`${file.name} 文件上传失败`);
      setUploading(false);
    }
  };

  const handleUploadChange: UploadProps['onChange'] = (info) => {
    let newFileList = [...info.fileList];
    newFileList = newFileList.slice(-3); // 最多保留3个文件
    setFileList(newFileList);
  };

  // 处理表单提交
  const handleSubmit = async (values: any) => {
    try {
      setSubmitting(true);
      
      // 构建提交数据
      const submitData = {
        ...values,
        cityRatios: cityRatios.filter(city => city.ratio > 0),
        uploadedFiles: fileList.map(file => file.response),
      };

      console.log('提交数据:', submitData);
      
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      message.success('系统补全任务已提交成功！');
      
    } catch (error) {
      console.error('提交失败:', error);
      message.error('提交失败，请重试');
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <PageContainer title="系统补全">
      <div style={{ padding: '0 24px', maxWidth: 1400, margin: '0 auto' }}>
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          onValuesChange={handleFormValuesChange}
          initialValues={{
            h5Clicks: 10000,
            carInsurance: {
              enabled: true,
              ledgerPercentMin: 45,
              ledgerPercentMax: 55,
              chatPercentMin: 25,
              chatPercentMax: 35,
            },
            propertyInsurance: {
              enabled: false,
              ledgerPercentMin: 25,
              ledgerPercentMax: 35,
              chatPercentMin: 25,
              chatPercentMax: 35,
            },
            valueAddedService: {
              enabled: false,
              ledgerPercentMin: 20,
              ledgerPercentMax: 30,
              chatPercentMin: 25,
              chatPercentMax: 35,
            },
            cityList: ['北京市:1', '上海市:1', '深圳市:2'],
            tenantId: undefined,
          }}
        >
          {/* 数据导入选项 */}
          <ProCard
            title="数据导入选项"
            style={{ marginBottom: 24 }}
            headerBordered
            extra={<Text type="secondary">上传Excel或CSV文件导入数据</Text>}
          >
            <Upload
              name="file"
              fileList={fileList}
              onChange={handleUploadChange}
              beforeUpload={beforeUpload}
              customRequest={customUpload}
              multiple={true}
              accept=".xlsx,.xls,.csv"
            >
              <Button type="primary" icon={<UploadOutlined />} loading={uploading}>
                导入表格数据
              </Button>
              <Text type="secondary" style={{ marginLeft: 12 }}>
                上传Excel或CSV文件导入数据（最多3个）
              </Text>
            </Upload>

            {fileList.length > 0 && (
              <div style={{ marginTop: 16 }}>
                <Divider style={{ margin: '8px 0' }} />
                <Row gutter={[8, 8]}>
                  {fileList.map((file, index) => (
                    <Col span={24} key={file.uid || index}>
                      <div style={{ display: 'flex', alignItems: 'center', padding: '8px 0' }}>
                        {file.type?.includes('excel') || file.name.endsWith('.xlsx') || file.name.endsWith('.xls') ? (
                          <FileExcelOutlined style={{ color: '#52c41a', marginRight: 8 }} />
                        ) : (
                          <FileTextOutlined style={{ color: '#1890ff', marginRight: 8 }} />
                        )}
                        <Text strong>{file.name}</Text>
                        {file.status === 'done' && (
                          <Text type="success" style={{ marginLeft: 8 }}>
                            已上传
                          </Text>
                        )}
                        {file.status === 'uploading' && (
                          <Text type="warning" style={{ marginLeft: 8 }}>
                            上传中...
                          </Text>
                        )}
                      </div>
                    </Col>
                  ))}
                </Row>
              </div>
            )}
          </ProCard>

          {/* 系统补全设置 */}
          <ProCard
            title="系统补全设置"
            style={{ marginBottom: 24 }}
            headerBordered
            extra={<Text type="secondary">配置补全参数</Text>}
          >
            <Row gutter={24}>
              <Col span={12}>
                <Form.Item
                  label="选择公司"
                  name="tenantId"
                  rules={[{ required: true, message: '请选择需要补全数据的公司' }]}
                  tooltip="选择需要进行系统补全的公司"
                >
                  <TenantSelect placeholder="请选择公司" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  label="点击数"
                  name="h5Clicks"
                  rules={[{ required: true, message: '请输入点击数' }]}
                  tooltip="设置本次补全所基于的总点击数"
                >
                  <InputNumber
                    style={{ width: '100%' }}
                    min={1}
                    placeholder="请输入点击数"
                    formatter={value => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                    parser={value => value!.replace(/\$\s?|(,*)/g, '')}
                  />
                </Form.Item>
              </Col>
            </Row>
            <Form.Item
              label="选择日期范围"
              name="dateRange"
              rules={[{ required: true, message: '请选择日期范围' }]}
            >
              <RangePicker
                style={{ width: '100%' }}
                placeholder={['开始日期', '结束日期']}
                allowClear
              />
            </Form.Item>
          </ProCard>
