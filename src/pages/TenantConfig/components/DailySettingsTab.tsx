import React, { useState, useEffect, useRef } from 'react';
import {
  Form,
  Card,
  InputNumber,
  Button,
  Checkbox,
  Space,
  message,
  Row,
  Col,
  Typography,
  Divider,
  Tooltip,
  Badge,
  Tag,
  Empty,
  Statistic
} from 'antd';
import { CarOutlined, BankOutlined, GiftOutlined, InfoCircleOutlined, PercentageOutlined, SettingOutlined, EnvironmentOutlined } from '@ant-design/icons';
import { ProCard } from '@ant-design/pro-components';
import { addDailyConfig, editDailyConfig, getDailyConfigInfo } from '@/services/corp/tenantConfig';
import type { FormRef, LedgerConfig, CityItem, DailyConfigInfo, CityRatioItem } from '../data';
import { FormCitySelector } from '@/components/CitySelector';

const { Title, Text } = Typography;

interface DailySettingsTabProps {
  tenantId: number;
  tenantName: string;
}



const DailySettingsTab: React.FC<DailySettingsTabProps> = ({ tenantId, tenantName }) => {
  const [form] = Form.useForm();
  const formRef = useRef<FormRef>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [submitting, setSubmitting] = useState<boolean>(false);
  // 保存配置ID，用于区分新增还是编辑
  const [configId, setConfigId] = useState<string>('');

  // 台账类型选择状态
  const [carLedgerEnabled, setCarLedgerEnabled] = useState<boolean>(false);
  const [financeLedgerEnabled, setFinanceLedgerEnabled] = useState<boolean>(false);
  const [valueAddedLedgerEnabled, setValueAddedLedgerEnabled] = useState<boolean>(false);

  // 城市配比相关状态
  const [selectedCities, setSelectedCities] = useState<string[]>([]);
  const [cityRatios, setCityRatios] = useState<CityRatioItem[]>([]);
  const [showCityRatios, setShowCityRatios] = useState<boolean>(false);

  // 处理城市选择变化
  const handleCityChange = (cities: string[]) => {
    setSelectedCities(cities);

    if (cities && cities.length > 0) {
      // 提取城市编码
      const cityCodes = cities.map(city => city.split(':')[0]);

      // 初始化城市配比数据（只保存城市编码）
      const newCityRatios: CityRatioItem[] = cityCodes.map(code => ({
        cityCode: code,
        ratio: 0
      }));

      setCityRatios(newCityRatios);
      setShowCityRatios(true);
    } else {
      setCityRatios([]);
      setShowCityRatios(false);
    }
  };

  // 处理城市配比变化
  const handleCityRatioChange = (cityCode: string, ratio: number) => {
    setCityRatios(prev =>
      prev.map(item =>
        item.cityCode === cityCode ? { ...item, ratio } : item
      )
    );
  };

  // 计算配比总和
  const getTotalRatio = () => {
    return cityRatios.reduce((sum, item) => sum + (item.ratio || 0), 0);
  };

  // 验证配比是否有效
  const validateRatios = () => {
    if (cityRatios.length === 0) return true; // 没有城市时不需要验证
    const total = getTotalRatio();
    return total === 100;
  };

  // 简化的计算函数，用于触发重新渲染
  const calculateResults = () => {
    // 这个函数现在只是用来触发表单值变化的监听
    // 实际计算在Form.Item shouldUpdate中进行
  };

  // 获取配置数据
  const fetchConfig = async () => {
    // 允许tenantId为0，但不允许为null或undefined
    if (tenantId === null || tenantId === undefined) return;

    setLoading(true);
    try {
      console.log(`开始获取租户ID ${tenantId} 的每日设置详情`);

      // 获取每日设置详情
      const response = await getDailyConfigInfo(tenantId);
      console.log('获取每日设置详情响应:', response);

      // 无论哪种情况，先重置表单和状态
      form.resetFields();
      setCarLedgerEnabled(false);
      setFinanceLedgerEnabled(false);
      setValueAddedLedgerEnabled(false);
      setConfigId(''); // 重置配置ID
      // 重置城市配比状态
      setSelectedCities([]);
      setCityRatios([]);
      setShowCityRatios(false);

      // 如果返回成功但结果为null，则保持表单为空
      if (response.success && !response.result) {
        console.log('返回结果为null，表单保持为空');
        return;
      }

      // 如果有结果数据
      if (response.success && response.result) {
        const { id, configJson, clickStart, clickEnd } = response.result;

        // 保存配置ID，用于后续编辑
        if (id) {
          setConfigId(id);
          console.log('保存配置ID:', id);
        } else {
          setConfigId('');
          console.log('未找到配置ID，将使用新增接口');
        }

        // 如果configJson为null或空字符串，则使用空对象
        let config = {};
        if (configJson && configJson !== 'null' && configJson !== '{}') {
          try {
            config = JSON.parse(configJson);
          } catch (e) {
            console.error('解析configJson失败:', e);
          }
        }

        console.log('解析后的配置:', config);

        // 准备表单数据
        const formData: any = {};

        // 设置点击数范围
        if (clickStart !== null && clickStart !== undefined) {
          formData.clickStart = clickStart;
        }

        if (clickEnd !== null && clickEnd !== undefined) {
          formData.clickEnd = clickEnd;
        }

        // 处理城市列表
        let cityListData = [];
        if (config.cityList) {
          if (typeof config.cityList === 'string') {
            // 如果是字符串形式（例如："上海市|北京市"）
            cityListData = config.cityList.split('|').filter(Boolean).map(city => `${city}:${city}:1`);
          } else if (Array.isArray(config.cityList)) {
            // 检查数组中的第一个元素来判断数据格式
            const firstItem = config.cityList[0];

            if (typeof firstItem === 'string') {
              // 如果是纯字符串数组，可能是编码数组或已格式化的数组
              if (firstItem.includes(':')) {
                // 已经是格式化的数组 ["530000:云南省:1"]
                cityListData = config.cityList;
              } else {
                // 纯编码数组 ["530000", "110000"]
                try {
                  // 使用新的工具函数将编码转换为标准格式
                  cityListData = await convertCodesToStandardFormat(config.cityList);
                } catch (error) {
                  console.error('转换城市编码失败:', error);
                  // 如果转换失败，使用原始编码作为名称
                  cityListData = config.cityList.map((code: string) => `${code}:${code}:1`);
                }
              }
            } else if (typeof firstItem === 'object') {
              // 对象数组形式
              cityListData = config.cityList.map((item: any) => {
                // 处理新的数据结构 (cityCode, cityName, type)
                if (item.cityCode && item.cityName) {
                  return `${item.cityCode}:${item.cityName}:${item.type}`;
                }
                // 处理旧的数据结构 (city, type)
                else if (item.city) {
                  return `${item.city}:${item.city}:${item.type}`;
                }
                // 未知结构，尝试使用可用字段
                return `${item.cityCode || item.city || ''}:${item.cityName || item.city || ''}:${item.type || 1}`;
              });
            }
          }

          if (cityListData.length > 0) {
            formData.cityList = cityListData;
            // 设置选中的城市
            setSelectedCities(cityListData);
          }
        }

        // 处理城市配比数据
        if (config.cityRatios && Array.isArray(config.cityRatios)) {
          setCityRatios(config.cityRatios);
          setShowCityRatios(config.cityRatios.length > 0);
        } else {
          setCityRatios([]);
          setShowCityRatios(false);
        }

        // 处理台账数据
        if (config.carLedger) {
          formData.carLedger = config.carLedger;
          setCarLedgerEnabled(true);
        }

        if (config.financeLedger) {
          formData.financeLedger = config.financeLedger;
          setFinanceLedgerEnabled(true);
        }

        if (config.valueAddedLedger) {
          formData.valueAddedLedger = config.valueAddedLedger;
          setValueAddedLedgerEnabled(true);
        }

        console.log('设置表单数据:', formData);

        // 设置表单值
        form.setFieldsValue(formData);
      } else {
        // API调用失败，表单保持为空
        console.log('API调用失败，表单保持为空');
      }
    } catch (error) {
      console.error('获取配置数据出错:', error);
      // 出错时表单保持为空
      form.resetFields();
      setCarLedgerEnabled(false);
      setFinanceLedgerEnabled(false);
      setValueAddedLedgerEnabled(false);
      // 重置城市配比状态
      setSelectedCities([]);
      setCityRatios([]);
      setShowCityRatios(false);
    } finally {
      setLoading(false);
    }
  };

  // 当租户ID变化时获取配置
  useEffect(() => {
    // 允许tenantId为0，但不允许为null或undefined
    if (tenantId === 0 || tenantId) {
      console.log(`租户ID变化为 ${tenantId}，开始获取配置`);
      fetchConfig();
    }
  }, [tenantId, form]); // 添加form依赖项，确保表单实例变化时也重新加载

  // 监听表单值变化
  const handleFormValuesChange = (changedValues: any, allValues: any) => {
    if (changedValues.cityList) {
      handleCityChange(changedValues.cityList);
    }
    // 当表单值变化时重新计算结果
    calculateResults();
  };



  // 提交表单
  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();

      // 验证城市配比
      if (showCityRatios && !validateRatios()) {
        message.error('城市配比总和必须等于100%');
        return;
      }

      setSubmitting(true);
      console.log('提交表单数据:', values);

      // 处理城市数据 - 只传递城市编码
      let cityList: string[] = [];
      if (Array.isArray(values.cityList) && values.cityList.length > 0) {
        cityList = values.cityList.map((item: string) => {
          const parts = item.split(':');
          // 只取编码部分，格式: code:name:type -> code
          return parts[0]; // 返回城市编码
        });
      }

      // 准备台账数据
      let carLedgerData = null;
      let financeLedgerData = null;
      let valueAddedLedgerData = null;

      if (carLedgerEnabled && values.carLedger) {
        carLedgerData = {
          ledgerStart: values.carLedger.ledgerStart || 0,
          ledgerEnd: values.carLedger.ledgerEnd || 0,
          chatUserStart: values.carLedger.chatUserStart || 0,
          chatUserEnd: values.carLedger.chatUserEnd || 0
        };
      }

      if (financeLedgerEnabled && values.financeLedger) {
        financeLedgerData = {
          ledgerStart: values.financeLedger.ledgerStart || 0,
          ledgerEnd: values.financeLedger.ledgerEnd || 0,
          chatUserStart: values.financeLedger.chatUserStart || 0,
          chatUserEnd: values.financeLedger.chatUserEnd || 0
        };
      }

      if (valueAddedLedgerEnabled && values.valueAddedLedger) {
        valueAddedLedgerData = {
          ledgerStart: values.valueAddedLedger.ledgerStart || 0,
          ledgerEnd: values.valueAddedLedger.ledgerEnd || 0,
          chatUserStart: values.valueAddedLedger.chatUserStart || 0,
          chatUserEnd: values.valueAddedLedger.chatUserEnd || 0
        };
      }

      // 构建配置JSON - cityList 直接存储编码数组
      const configJson = {
        cityList: cityList.length > 0 ? cityList : [], // 直接存储编码数组，如 ["530000", "110000"]
        cityRatios: showCityRatios ? cityRatios : [], // 城市配比数据
        carLedger: carLedgerData,
        financeLedger: financeLedgerData,
        valueAddedLedger: valueAddedLedgerData
      };

      console.log('构建的configJson:', configJson);

      // 构建请求数据
      const requestData: {
        configJson: string;
        clickStart: number;
        clickEnd: number;
        tenantId: number;
        id?: string;
      } = {
        configJson: JSON.stringify(configJson),
        clickStart: values.clickStart || 0,
        clickEnd: values.clickEnd || 0,
        tenantId
      };

      // 如果有ID，将ID添加到请求数据中
      if (configId) {
        requestData.id = configId;
      }

      console.log('发送的请求数据:', requestData);

      // 发送请求
      let response;
      if (configId) {
        // 如果有ID，调用编辑接口
        console.log('调用编辑接口，ID:', configId);
        response = await editDailyConfig(configId, requestData);
      } else {
        // 如果没有ID，调用新增接口
        console.log('调用新增接口');
        response = await addDailyConfig(requestData);
      }

      console.log('保存响应:', response);

      if (response.success) {
        message.success('配置保存成功');
        // 重新加载数据
        fetchConfig();
      } else {
        message.error(response.message || '保存失败');
      }
    } catch (error) {
      console.error('提交表单出错:', error);
      message.error('表单验证失败，请检查输入');
    } finally {
      setSubmitting(false);
    }
  };

  // 渲染台账类型卡片
  const renderLedgerCard = (
    title: string,
    icon: React.ReactNode,
    description: string,
    fieldName: string,
    enabled: boolean,
    setEnabled: (enabled: boolean) => void
  ) => {

    return (
      <Card
        size="small"
        style={{ marginBottom: 0 }}
        title={
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <Checkbox
              checked={enabled}
              onChange={(e) => setEnabled(e.target.checked)}
              style={{ marginRight: 8 }}
            />
            <span style={{ display: 'flex', alignItems: 'center' }}>
              {icon} <span style={{ marginLeft: 6, fontWeight: 500, fontSize: 14 }}>{title}</span>
            </span>
            {enabled && (
              <Tag color="success" size="small" style={{ marginLeft: 8 }}>
                已启用
              </Tag>
            )}
          </div>
        }
        bodyStyle={{ padding: enabled ? '12px' : 0, display: enabled ? 'block' : 'none' }}
      >
        {enabled && (
          <div style={{ display: 'flex', flexDirection: 'column', gap: 12 }}>
            {/* 台账数占比 */}
            <div>
              <div style={{ display: 'flex', alignItems: 'center', marginBottom: 6 }}>
                <Text style={{ fontSize: 12, fontWeight: 500 }}>预约数占比区间</Text>
                <Tooltip title="此类型预约数占总点击数的比例范围">
                  <InfoCircleOutlined style={{ marginLeft: 4, color: '#8c8c8c', fontSize: 12 }} />
                </Tooltip>
              </div>
              <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                <Form.Item
                  name={[fieldName, 'ledgerStart']}
                  noStyle
                  rules={[{ required: true, message: '请输入最小值' }]}
                >
                  <InputNumber
                    placeholder="最小值"
                    style={{ width: 120 }}
                    min={0}
                    max={100}
                    addonAfter="%"
                    precision={2}
                  />
                </Form.Item>
                <Text type="secondary" style={{ fontSize: 12 }}>至</Text>
                <Form.Item
                  name={[fieldName, 'ledgerEnd']}
                  noStyle
                  rules={[{ required: true, message: '请输入最大值' }]}
                >
                  <InputNumber
                    placeholder="最大值"
                    style={{ width: 120 }}
                    min={0}
                    max={100}
                    addonAfter="%"
                    precision={2}
                  />
                </Form.Item>
              </div>
            </div>

            {/* 聊天用户数占比 */}
            <div>
              <div style={{ display: 'flex', alignItems: 'center', marginBottom: 6 }}>
                <Text style={{ fontSize: 12, fontWeight: 500 }}>聊天用户数占比区间</Text>
                <Tooltip title="聊天用户数占此类型预约数的比例范围">
                  <InfoCircleOutlined style={{ marginLeft: 4, color: '#8c8c8c', fontSize: 12 }} />
                </Tooltip>
              </div>
              <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                <Form.Item
                  name={[fieldName, 'chatUserStart']}
                  noStyle
                  rules={[{ required: true, message: '请输入最小值' }]}
                >
                  <InputNumber
                    placeholder="最小值"
                    style={{ width: 120 }}
                    min={0}
                    max={100}
                    addonAfter="%"
                    precision={2}
                  />
                </Form.Item>
                <Text type="secondary" style={{ fontSize: 12 }}>至</Text>
                <Form.Item
                  name={[fieldName, 'chatUserEnd']}
                  noStyle
                  rules={[{ required: true, message: '请输入最大值' }]}
                >
                  <InputNumber
                    placeholder="最大值"
                    style={{ width: 120 }}
                    min={0}
                    max={100}
                    addonAfter="%"
                    precision={2}
                  />
                </Form.Item>
              </div>
            </div>
          </div>
        )}
      </Card>
    );
  };

  return (
    <div style={{ padding: '20px 0' }}>
      <div style={{ marginBottom: 16 }}>
        <Title level={4}>{tenantName} - 每日设置</Title>
      </div>

      <Form
        form={form}
        layout="vertical"
        ref={formRef}
        onValuesChange={handleFormValuesChange}
        // 不设置初始值，由API返回数据决定
      >
        <ProCard title="每月基础设置" style={{ marginBottom: 24 }} headerBordered>
          <div style={{
              backgroundColor: '#f0f8ff',
              padding: 16,
              borderRadius: 4,
              border: '1px solid #cce5ff',
              marginBottom: 16
            }}>
            <div style={{ display: 'flex', alignItems: 'center', marginBottom: 12 }}>
              <Text strong style={{ fontSize: 16 }}>
                点击数范围
              </Text>
              <Tag color="blue" style={{ marginLeft: 8 }}>关键指标</Tag>
              <Tooltip title="设置每月预期的点击总数范围">
                <InfoCircleOutlined style={{ marginLeft: 8, color: '#1890ff' }} />
              </Tooltip>
            </div>

            <Form.Item noStyle shouldUpdate>
              {({ getFieldValue }) => {
                const clickStart = getFieldValue('clickStart') || 0;
                const clickEnd = getFieldValue('clickEnd') || 0;
                return (
                  <>
                    <Row gutter={16} align="middle">
                      <Col span={24}>
                        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                          <div style={{ flex: 1 }}>
                            <Form.Item
                              name="clickStart"
                              noStyle
                              rules={[{ required: true, message: '请输入最小值' }]}
                            >
                              <InputNumber
                                placeholder="最小值"
                                style={{ width: '100%' }}
                                min={0}
                                formatter={value => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                                parser={value => value ? value.replace(/\$\s?|(,*)/g, '') : ''}
                              />
                            </Form.Item>
                          </div>
                          <div style={{ padding: '0 12px', color: '#999' }}>
                            <Text type="secondary">至</Text>
                          </div>
                          <div style={{ flex: 1 }}>
                            <Form.Item
                              name="clickEnd"
                              noStyle
                              rules={[{ required: true, message: '请输入最大值' }]}
                            >
                              <InputNumber
                                placeholder="最大值"
                                style={{ width: '100%' }}
                                min={0}
                                formatter={value => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                                parser={value => value ? value.replace(/\$\s?|(,*)/g, '') : ''}
                              />
                            </Form.Item>
                          </div>
                        </div>
                      </Col>
                    </Row>
                    <div style={{ marginTop: 8, textAlign: 'center' }}>
                      <Badge color="#1890ff" text={
                        <Text type="secondary">
                          当前设置范围: {clickStart.toLocaleString()} - {clickEnd.toLocaleString()} 次点击
                        </Text>
                      } />
                    </div>
                  </>
                );
              }}
            </Form.Item>
          </div>
        </ProCard>

        <ProCard title="预约信息设置" style={{ marginBottom: 24 }} headerBordered extra={<Text type="secondary">选择需要生成的预约信息类型</Text>}>
          <Row gutter={24}>
            {/* 左侧：预约信息设置 */}
            <Col span={16}>
              <div style={{ display: 'flex', flexDirection: 'column', gap: 16 }}>
                {renderLedgerCard(
                  '车险预约',
                  <CarOutlined />,
                  '包含车辆保险预约相关记录',
                  'carLedger',
                  carLedgerEnabled,
                  setCarLedgerEnabled
                )}
                {renderLedgerCard(
                  '财险预约',
                  <BankOutlined />,
                  '包含财产保险预约相关记录',
                  'financeLedger',
                  financeLedgerEnabled,
                  setFinanceLedgerEnabled
                )}
                {renderLedgerCard(
                  '增值服务预约',
                  <GiftOutlined />,
                  '包含增值服务预约相关记录',
                  'valueAddedLedger',
                  valueAddedLedgerEnabled,
                  setValueAddedLedgerEnabled
                )}
              </div>
            </Col>

            {/* 右侧：计算结果 */}
            <Col span={8}>
              <Form.Item noStyle shouldUpdate>
                {({ getFieldValue }) => {
                  const clickStart = getFieldValue('clickStart') || 0;
                  const clickEnd = getFieldValue('clickEnd') || 0;
                  const carLedger = getFieldValue('carLedger');
                  const financeLedger = getFieldValue('financeLedger');
                  const valueAddedLedger = getFieldValue('valueAddedLedger');

                  const hasData = (clickStart > 0 || clickEnd > 0) &&
                    (carLedgerEnabled || financeLedgerEnabled || valueAddedLedgerEnabled);

                  return (
                    <Card title="计算结果" style={{ height: '100%', minHeight: 400 }}>
                      {hasData ? (
                        <div>
                          {/* 车险预约计算结果 */}
                          {carLedgerEnabled && carLedger && (
                            <div style={{ marginBottom: 16 }}>
                              <div style={{ fontWeight: 'bold', marginBottom: 8, color: '#1890ff' }}>
                                <CarOutlined style={{ marginRight: 4 }} />
                                车险预约
                              </div>
                              <div style={{ fontSize: 12, color: '#666', marginBottom: 4, display: 'flex', alignItems: 'center' }}>
                                预约数区间: {Math.round(clickStart * (carLedger.ledgerStart || 0) / 100)} - {Math.round(clickEnd * (carLedger.ledgerEnd || 0) / 100)}
                                <Tooltip title={`基于点击数 × 预约数占比(${carLedger.ledgerStart || 0}%-${carLedger.ledgerEnd || 0}%)计算`}>
                                  <InfoCircleOutlined style={{ marginLeft: 4, color: '#8c8c8c', fontSize: 10 }} />
                                </Tooltip>
                              </div>
                              <div style={{ fontSize: 12, color: '#666', display: 'flex', alignItems: 'center' }}>
                                聊天用户区间: {Math.round(clickStart * (carLedger.ledgerStart || 0) * (carLedger.chatUserStart || 0) / 10000)} - {Math.round(clickEnd * (carLedger.ledgerEnd || 0) * (carLedger.chatUserEnd || 0) / 10000)}
                                <Tooltip title={`基于预约数 × 聊天用户占比(${carLedger.chatUserStart || 0}%-${carLedger.chatUserEnd || 0}%)计算`}>
                                  <InfoCircleOutlined style={{ marginLeft: 4, color: '#8c8c8c', fontSize: 10 }} />
                                </Tooltip>
                              </div>

                              {/* 城市分配 */}
                              {showCityRatios && cityRatios.length > 0 && getTotalRatio() === 100 && (
                                <div style={{ marginTop: 8, padding: 8, backgroundColor: '#f5f5f5', borderRadius: 4 }}>
                                  <div style={{ fontSize: 11, fontWeight: 'bold', marginBottom: 4 }}>城市分配:</div>
                                  {cityRatios.map(city => {
                                    const cityLedgerMin = Math.round(clickStart * (carLedger.ledgerStart || 0) * city.ratio / 10000);
                                    const cityLedgerMax = Math.round(clickEnd * (carLedger.ledgerEnd || 0) * city.ratio / 10000);
                                    // 从选中的城市列表中找到对应的城市名称
                                    const selectedCity = selectedCities.find(sc => sc.split(':')[0] === city.cityCode);
                                    const cityName = selectedCity ? selectedCity.split(':')[1] : city.cityCode;
                                    return (
                                      <div key={city.cityCode} style={{ fontSize: 10, color: '#666' }}>
                                        {cityName}: {cityLedgerMin}-{cityLedgerMax}
                                      </div>
                                    );
                                  })}
                                </div>
                              )}
                            </div>
                          )}

                          {/* 财险预约计算结果 */}
                          {financeLedgerEnabled && financeLedger && (
                            <div style={{ marginBottom: 16 }}>
                              <div style={{ fontWeight: 'bold', marginBottom: 8, color: '#52c41a' }}>
                                <BankOutlined style={{ marginRight: 4 }} />
                                财险预约
                              </div>
                              <div style={{ fontSize: 12, color: '#666', marginBottom: 4, display: 'flex', alignItems: 'center' }}>
                                预约数区间: {Math.round(clickStart * (financeLedger.ledgerStart || 0) / 100)} - {Math.round(clickEnd * (financeLedger.ledgerEnd || 0) / 100)}
                                <Tooltip title={`基于点击数 × 预约数占比(${financeLedger.ledgerStart || 0}%-${financeLedger.ledgerEnd || 0}%)计算`}>
                                  <InfoCircleOutlined style={{ marginLeft: 4, color: '#8c8c8c', fontSize: 10 }} />
                                </Tooltip>
                              </div>
                              <div style={{ fontSize: 12, color: '#666', display: 'flex', alignItems: 'center' }}>
                                聊天用户区间: {Math.round(clickStart * (financeLedger.ledgerStart || 0) * (financeLedger.chatUserStart || 0) / 10000)} - {Math.round(clickEnd * (financeLedger.ledgerEnd || 0) * (financeLedger.chatUserEnd || 0) / 10000)}
                                <Tooltip title={`基于预约数 × 聊天用户占比(${financeLedger.chatUserStart || 0}%-${financeLedger.chatUserEnd || 0}%)计算`}>
                                  <InfoCircleOutlined style={{ marginLeft: 4, color: '#8c8c8c', fontSize: 10 }} />
                                </Tooltip>
                              </div>

                              {/* 城市分配 */}
                              {showCityRatios && cityRatios.length > 0 && getTotalRatio() === 100 && (
                                <div style={{ marginTop: 8, padding: 8, backgroundColor: '#f5f5f5', borderRadius: 4 }}>
                                  <div style={{ fontSize: 11, fontWeight: 'bold', marginBottom: 4 }}>城市分配:</div>
                                  {cityRatios.map(city => {
                                    const cityLedgerMin = Math.round(clickStart * (financeLedger.ledgerStart || 0) * city.ratio / 10000);
                                    const cityLedgerMax = Math.round(clickEnd * (financeLedger.ledgerEnd || 0) * city.ratio / 10000);
                                    // 从选中的城市列表中找到对应的城市名称
                                    const selectedCity = selectedCities.find(sc => sc.split(':')[0] === city.cityCode);
                                    const cityName = selectedCity ? selectedCity.split(':')[1] : city.cityCode;
                                    return (
                                      <div key={city.cityCode} style={{ fontSize: 10, color: '#666' }}>
                                        {cityName}: {cityLedgerMin}-{cityLedgerMax}
                                      </div>
                                    );
                                  })}
                                </div>
                              )}
                            </div>
                          )}

                          {/* 增值服务预约计算结果 */}
                          {valueAddedLedgerEnabled && valueAddedLedger && (
                            <div style={{ marginBottom: 16 }}>
                              <div style={{ fontWeight: 'bold', marginBottom: 8, color: '#fa8c16' }}>
                                <GiftOutlined style={{ marginRight: 4 }} />
                                增值服务预约
                              </div>
                              <div style={{ fontSize: 12, color: '#666', marginBottom: 4, display: 'flex', alignItems: 'center' }}>
                                预约数区间: {Math.round(clickStart * (valueAddedLedger.ledgerStart || 0) / 100)} - {Math.round(clickEnd * (valueAddedLedger.ledgerEnd || 0) / 100)}
                                <Tooltip title={`基于点击数 × 预约数占比(${valueAddedLedger.ledgerStart || 0}%-${valueAddedLedger.ledgerEnd || 0}%)计算`}>
                                  <InfoCircleOutlined style={{ marginLeft: 4, color: '#8c8c8c', fontSize: 10 }} />
                                </Tooltip>
                              </div>
                              <div style={{ fontSize: 12, color: '#666', display: 'flex', alignItems: 'center' }}>
                                聊天用户区间: {Math.round(clickStart * (valueAddedLedger.ledgerStart || 0) * (valueAddedLedger.chatUserStart || 0) / 10000)} - {Math.round(clickEnd * (valueAddedLedger.ledgerEnd || 0) * (valueAddedLedger.chatUserEnd || 0) / 10000)}
                                <Tooltip title={`基于预约数 × 聊天用户占比(${valueAddedLedger.chatUserStart || 0}%-${valueAddedLedger.chatUserEnd || 0}%)计算`}>
                                  <InfoCircleOutlined style={{ marginLeft: 4, color: '#8c8c8c', fontSize: 10 }} />
                                </Tooltip>
                              </div>

                              {/* 城市分配 */}
                              {showCityRatios && cityRatios.length > 0 && getTotalRatio() === 100 && (
                                <div style={{ marginTop: 8, padding: 8, backgroundColor: '#f5f5f5', borderRadius: 4 }}>
                                  <div style={{ fontSize: 11, fontWeight: 'bold', marginBottom: 4 }}>城市分配:</div>
                                  {cityRatios.map(city => {
                                    const cityLedgerMin = Math.round(clickStart * (valueAddedLedger.ledgerStart || 0) * city.ratio / 10000);
                                    const cityLedgerMax = Math.round(clickEnd * (valueAddedLedger.ledgerEnd || 0) * city.ratio / 10000);
                                    // 从选中的城市列表中找到对应的城市名称
                                    const selectedCity = selectedCities.find(sc => sc.split(':')[0] === city.cityCode);
                                    const cityName = selectedCity ? selectedCity.split(':')[1] : city.cityCode;
                                    return (
                                      <div key={city.cityCode} style={{ fontSize: 10, color: '#666' }}>
                                        {cityName}: {cityLedgerMin}-{cityLedgerMax}
                                      </div>
                                    );
                                  })}
                                </div>
                              )}
                            </div>
                          )}
                        </div>
                      ) : (
                        <Empty
                          description="请设置点击数范围并选择预约信息类型"
                          style={{ marginTop: 60 }}
                        />
                      )}
                    </Card>
                  );
                }}
              </Form.Item>
            </Col>
          </Row>
        </ProCard>

        <ProCard
          title={<>
            <span>业务城市</span>
            <Tag color="blue" style={{ marginLeft: 8 }}>可选</Tag>
            <Tooltip title="选择业务所在的城市，并设置各城市的业务配比">
              <InfoCircleOutlined style={{ marginLeft: 8, color: '#8c8c8c' }} />
            </Tooltip>
          </>}
          headerBordered
          style={{ marginBottom: 24 }}
        >
          <div style={{ display: 'flex', alignItems: 'center', marginBottom: 16 }}>
            <EnvironmentOutlined style={{ fontSize: 18, color: '#1890ff', marginRight: 8 }} />
            <Text strong>选择业务开展城市</Text>
          </div>

          <FormCitySelector
            name="cityList"
            placeholder="请选择业务城市"
            showSelected={false}
            required={false}
            useBackendData={true}
            cityData={[]} // 提供一个空数组作为后备数据
          />

          {/* 城市配比设置区域 */}
          {showCityRatios && cityRatios.length > 0 && (
            <div style={{ marginTop: 24 }}>
              <Divider orientation="left">
                <div style={{ display: 'flex', alignItems: 'center' }}>
                  <PercentageOutlined style={{ marginRight: 8, color: '#1890ff' }} />
                  <Text strong>城市业务配比设置</Text>
                  <Tooltip title="设置各城市的业务占比，总和必须等于100%">
                    <InfoCircleOutlined style={{ marginLeft: 8, color: '#8c8c8c' }} />
                  </Tooltip>
                </div>
              </Divider>

              <div style={{
                backgroundColor: '#f9f9f9',
                padding: 16,
                borderRadius: 6,
                border: '1px solid #e8e8e8'
              }}>
                <Row gutter={[16, 16]}>
                  {cityRatios.map((city, index) => {
                    // 从选中的城市列表中找到对应的城市名称
                    const selectedCity = selectedCities.find(sc => sc.split(':')[0] === city.cityCode);
                    const cityName = selectedCity ? selectedCity.split(':')[1] : city.cityCode;

                    return (
                      <Col span={12} key={city.cityCode}>
                        <div style={{
                          backgroundColor: '#fff',
                          padding: 12,
                          borderRadius: 4,
                          border: '1px solid #d9d9d9'
                        }}>
                          <div style={{ display: 'flex', alignItems: 'center', marginBottom: 8 }}>
                            <EnvironmentOutlined style={{ color: '#1890ff', marginRight: 6 }} />
                            <Text strong>{cityName}</Text>
                          </div>
                          <InputNumber
                            value={city.ratio}
                            onChange={(value) => handleCityRatioChange(city.cityCode, value || 0)}
                            placeholder="配比百分比"
                            min={0}
                            max={100}
                            precision={1}
                            addonAfter="%"
                            style={{ width: '100%' }}
                          />
                        </div>
                      </Col>
                    );
                  })}
                </Row>

                <div style={{ marginTop: 16, textAlign: 'center' }}>
                  <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
                    <Text type="secondary">配比总和: </Text>
                    <Badge
                      color={getTotalRatio() === 100 ? '#52c41a' : '#ff4d4f'}
                      text={
                        <Text
                          strong
                          style={{
                            color: getTotalRatio() === 100 ? '#52c41a' : '#ff4d4f',
                            fontSize: 16
                          }}
                        >
                          {getTotalRatio()}%
                        </Text>
                      }
                    />
                    {getTotalRatio() === 100 ? (
                      <Tag color="success" style={{ marginLeft: 8 }}>✓ 配比正确</Tag>
                    ) : (
                      <Tag color="error" style={{ marginLeft: 8 }}>⚠ 总和必须为100%</Tag>
                    )}
                  </div>
                </div>
              </div>
            </div>
          )}
        </ProCard>

        <ProCard bordered style={{ marginTop: 24 }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <div>
              <Text type="secondary">最后更新时间: {new Date().toLocaleString()}</Text>
            </div>
            <Space size="middle">
              <Button>取消</Button>
              <Button type="primary" icon={<SettingOutlined />} onClick={handleSubmit} loading={submitting}>
                保存设置
              </Button>
            </Space>
          </div>
        </ProCard>


      </Form>
    </div>
  );
};

export default DailySettingsTab;
