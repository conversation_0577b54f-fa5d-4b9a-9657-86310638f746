import React, { useState } from 'react';
import { Input, List, Button, Tooltip, Tag, Typography, Avatar, Space } from 'antd';
import { SearchOutlined, TeamOutlined, AppstoreOutlined, BarsOutlined } from '@ant-design/icons';

import type { Tenant } from '../data';

const { Search } = Input;
const { Text, Title } = Typography;

interface TenantListProps {
  tenants: Tenant[];
  loading: boolean;
  selectedTenant: Tenant | null;
  onSelectTenant: (tenant: Tenant) => void;
}

const TenantList: React.FC<TenantListProps> = ({
  tenants,
  loading,
  selectedTenant,
  onSelectTenant,
}) => {
  const [searchValue, setSearchValue] = useState<string>('');

  // 搜索租户
  const handleSearch = (value: string) => {
    setSearchValue(value);
  };

  // 过滤租户列表
  const filteredTenants = tenants.filter(tenant =>
    tenant.name.toLowerCase().includes(searchValue.toLowerCase())
  );

  const [viewMode, setViewMode] = useState<'list' | 'card'>('list');

  return (
    <div style={{ height: '100%', display: 'flex', flexDirection: 'column', overflow: 'hidden' }}>
      <div style={{ padding: '16px 16px 8px' }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 12 }}>
          <Title level={5} style={{ margin: 0 }}>
            <TeamOutlined /> 公司列表
          </Title>
          <Space>
            <Tooltip title="列表视图">
              <Button
                type={viewMode === 'list' ? 'primary' : 'text'}
                icon={<BarsOutlined />}
                size="small"
                onClick={() => setViewMode('list')}
              />
            </Tooltip>
            <Tooltip title="卡片视图">
              <Button
                type={viewMode === 'card' ? 'primary' : 'text'}
                icon={<AppstoreOutlined />}
                size="small"
                onClick={() => setViewMode('card')}
              />
            </Tooltip>
          </Space>
        </div>
        <Search
          placeholder="搜索公司名称..."
          allowClear
          enterButton={<SearchOutlined />}
          onSearch={handleSearch}
          style={{ marginBottom: 12 }}
        />
      </div>

      <div style={{ flex: 1, overflow: 'auto', padding: '0 16px', height: '100%', maxHeight: 'calc(100% - 100px)' }}>
        {viewMode === 'list' ? (
          <List
            dataSource={filteredTenants}
            loading={loading}
            size="small"
            itemLayout="horizontal"
            renderItem={tenant => (
              <List.Item
                key={tenant.id}
                onClick={() => onSelectTenant(tenant)}
                style={{
                  cursor: 'pointer',
                  padding: '12px 16px',
                  borderRadius: '4px',
                  marginBottom: '8px',
                  backgroundColor: selectedTenant?.id === tenant.id ? '#e6f7ff' : 'white',
                  border: selectedTenant?.id === tenant.id ? '1px solid #91d5ff' : '1px solid #f0f0f0',
                  transition: 'all 0.3s',
                }}
              >
                <List.Item.Meta
                  avatar={
                    <Avatar style={{ backgroundColor: '#1890ff' }}>
                      {tenant.name.substring(0, 1)}
                    </Avatar>
                  }
                  title={<Text strong>{tenant.name}</Text>}
                  description={
                    <Tag color="blue" style={{ marginTop: 4 }}>
                      ID: {tenant.id}
                    </Tag>
                  }
                />
              </List.Item>
            )}
          />
        ) : (
          <div style={{ display: 'flex', flexWrap: 'wrap', gap: '12px' }}>
            {filteredTenants.map(tenant => (
              <div
                key={tenant.id}
                onClick={() => onSelectTenant(tenant)}
                style={{
                  width: 'calc(50% - 6px)',
                  padding: '12px',
                  borderRadius: '4px',
                  backgroundColor: selectedTenant?.id === tenant.id ? '#e6f7ff' : 'white',
                  border: selectedTenant?.id === tenant.id ? '1px solid #91d5ff' : '1px solid #f0f0f0',
                  cursor: 'pointer',
                  transition: 'all 0.3s',
                }}
              >
                <div style={{ display: 'flex', alignItems: 'center', marginBottom: 8 }}>

                  <Avatar style={{ backgroundColor: '#1890ff', marginRight: 8 }}>
                    {tenant.name.substring(0, 1)}
                  </Avatar>
                  <Text strong style={{ flex: 1, overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap' }}>
                    {tenant.name}
                  </Text>
                </div>
                <div>
                  <Tag color="blue">ID: {tenant.id}</Tag>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default TenantList;
