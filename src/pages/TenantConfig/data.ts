import type { FormInstance } from 'antd';

// 每日配置数据模型
export interface DailyConfig {
  id?: string;
  configJson: string;
  clickStart: number;
  clickEnd: number;
  tenantId: number;
}

// 台账配置数据模型
export interface LedgerConfig {
  ledgerStart: number;
  ledgerEnd: number;
  chatUserStart: number;
  chatUserEnd: number;
}

// 城市数据模型
export interface CityItem {
  city: string;
  type: number;
}

// 城市配比数据模型
export interface CityRatioItem {
  cityCode: string;    // 城市编码
  cityName: string;    // 城市名称
  ratio: number;       // 配比百分比 (0-100)
}

// 完整配置数据模型
export interface CompleteConfig {
  cityList: string | CityItem[];
  cityRatios?: CityRatioItem[];  // 城市配比数据
  carLedger: LedgerConfig;
  financeLedger: LedgerConfig;
  valueAddedLedger: LedgerConfig;
}

// 每日设置详情数据模型
export interface DailyConfigInfo {
  id?: string;
  configJson: string;
  clickStart: number;
  clickEnd: number;
  tenantId: number;
  updateBy?: string;
  updateTime?: string;
  createBy?: string;
  createTime?: string;
}

// 租户数据模型
export interface Tenant {
  id: number;
  name: string;
}

// 表单引用类型
export type FormRef = FormInstance;
