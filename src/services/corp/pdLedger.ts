import { request } from '@/utils/request';

// API 路径
const API = {
  list: '/corp/pdLedger/list',
  add: '/corp/pdLedger/add',
  edit: '/corp/pdLedger/edit',
  delete: '/corp/pdLedger/delete',
  deleteBatch: '/corp/pdLedger/deleteBatch',
  deleteBatchs: '/corp/pdLedger/deleteBatchs',
  importExcel: '/corp/pdLedger/importExcel',
  exportXls: '/corp/pdLedger/exportXls',
  chatGiveTo: '/corp/pdLedger/chatGiveTo',
  systemCompletion: '/corp/pdLedger/pageSystemCompletion',
};

/**
 * 获取车险台账列表
 * @param params 查询参数
 */
export async function fetchPdLedgerList(params: any) {
  const { current, pageSize, ...restParams } = params;
  return request(`${API.list}/${current}/${pageSize}`, {
    method: 'POST',
    data: restParams,
  });
}

/**
 * 添加车险台账
 * @param params 表单数据
 */
export async function addPdLedger(params: any) {
  return request(API.add, {
    method: 'POST',
    data: params,
  });
}

/**
 * 编辑车险台账
 * @param params 表单数据
 */
export async function editPdLedger(params: any) {
  return request(API.edit, {
    method: 'POST',
    data: params,
  });
}

/**
 * 删除车险台账
 * @param id 记录ID
 */
export async function deletePdLedger(id: string) {
  return request(API.delete, {
    method: 'DELETE',
    params: { id },
  });
}

/**
 * 批量删除车险台账
 * @param ids ID数组
 */
export async function batchDeletePdLedger(ids: string[]) {
  return request(API.deleteBatch, {
    method: 'DELETE',
    data: { ids },
  });
}

/**
 * 范围删除车险台账
 * @param params 删除参数
 */
export async function rangeDeletePdLedger(params: any) {
  return request(API.deleteBatchs, {
    method: 'DELETE',
    params,
  });
}

/**
 * 生成聊天记录
 */
export async function generateChatRecord() {
  return request(API.chatGiveTo, {
    method: 'POST',
  });
}

/**
 * 获取导出URL
 */
export function getExportUrl() {
  return API.exportXls;
}

/**
 * 获取导入URL
 */
export function getImportUrl() {
  return API.importExcel;
}

/**
 * 系统补全
 * @param params 系统补全参数
 */
export async function systemCompletion(params: any) {
  return request(API.systemCompletion, {
    method: 'POST',
    data: params,
  });
}
