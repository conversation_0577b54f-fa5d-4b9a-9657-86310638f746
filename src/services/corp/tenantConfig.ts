// 多租户配置相关API
import { request } from '@umijs/max';
import { uploadFile } from '@/utils/request';

// 定义每日配置DTO类型
export interface DailyConfigDto {
  configJson?: string;       // 每日配置 JSON 内容
  clickNum?: number;         // 点击数
  linkType?: number;         // 链接类型(0-车险;1-财险;2-增值服务)
  tenantId?: number;         // 租户id
  monthDataStart?: string;   // 日期开始时间
  monthDataEnd?: string;     // 日期结束时间
}

// 添加每日配置
export async function addDailyConfig(data: any) {
  return request('/daily-config/add', {
    method: 'POST',
    data,
  });
}

// 编辑每日配置
export async function editDailyConfig(id: string, data: any) {
  return request(`/daily-config/edit/${id}`, {
    method: 'POST',
    data,
  });
}

// 获取租户列表
// tenantType: 租户类型，0-车险，1-财险，2-增值服务，多选时用逗号隔开，如 "0,1,2"
export async function getTenantList(tenantType?: string | number[], params?: any) {
  // 如果 tenantType 是数组，则转换为逗号分隔的字符串
  const formattedTenantType = Array.isArray(tenantType) ? tenantType.join(',') : tenantType;

  return request('/sys/tenant/getTenantList', {
    method: 'GET',
    params: {
      ...params,
      // 如果没有指定 tenantType，则不传这个参数
      ...(formattedTenantType !== undefined ? { tenantType: formattedTenantType } : {})
    },
  });
}

// 注意: clickAuto/pre/get 接口不再使用

// 获取每日设置详情
export async function getDailyConfigInfo(tenantId: number) {
  return request(`/daily-config/info/${tenantId}`, {
    method: 'GET',
  });
}

// 批量导入所有公司配置
export async function uploadMultipleConfig(file: File) {
  const formData = new FormData();
  formData.append('file', file);

  return uploadFile('/daily-config/uploadFile/multi', formData);
}

// 系统补全 - 上传多个文件
export async function uploadSystemCompletionFiles(data: any) {
  // 构建符合后端 LedgerImportDTO 的数据结构
  const requestData = {
    startDate: data.startDate,
    endDate: data.endDate,
    recordNum: data.recordNum,
    clickNum: data.clickNum,
    tenantId: data.tenantId,
    cityCodes: data.cityCodes
  };

  console.log('提交系统补全数据:', requestData);
  console.log('调用接口: /corp/pdLedger/pageSystemCompletion');

  // 使用 request 发送 JSON 数据，而不是 FormData
  return request('/corp/pdLedger/pageSystemCompletion', {
    method: 'POST',
    data: requestData,
  });
}

// 批量系统补全 - 上传单个Excel文件
export async function uploadBatchSystemCompletionFile(file: File) {
  const formData = new FormData();

  // 添加文件
  formData.append('files', file);

  // 调用批量系统补全接口
  return uploadFile('/clickAuto/pre/uploadFile/multiBatch', formData);
}

// 查询批量系统补全任务处理进度
export async function getBatchSystemCompletionProgress() {
  return request('/clickAuto/pre/batchProgress', {
    method: 'GET',
  });
}

// 获取租户详细信息
export async function getTenantById(id: number) {
  return request('/sys/tenant/queryById', {
    method: 'GET',
    params: { id },
  });
}

// 编辑租户信息
export async function editTenant(data: any) {
  return request('/sys/tenant/edit', {
    method: 'POST',
    data,
  });
}

// 批量导入每日设置
export async function uploadBatchDailyConfig(formData: FormData) {
  return uploadFile('/daily-config/importExcel', formData);
}

// 批量导入链接设置
export async function uploadBatchLinkConfig(formData: FormData) {
  return uploadFile('/corp/pdLinkInfo/importExcel', formData);
}

// 获取链接生成规则配置
export async function getGenerateConfig(tenantId: number) {
  return request(`/generate-config/list/${tenantId}`, {
    method: 'GET',
  });
}

// 保存链接生成规则配置
export async function saveGenerateConfig(data: any) {
  return request('/generate-config/save', {
    method: 'POST',
    data,
  });
}
