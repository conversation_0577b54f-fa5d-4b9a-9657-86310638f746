/**
 * 处理城市数据，去除城市名称中的"省"、"市"等后缀
 * @param cityList 城市列表，格式为 ["城市名:类型", ...]
 * @returns 处理后的城市对象数组，格式为 [{city: "城市名", type: 类型值}]
 */
export const processCityData = (cityList: string[]): { city: string; type: number }[] => {
  if (!cityList || !Array.isArray(cityList) || cityList.length === 0) return [];
  
  return cityList.map((item: string) => {
    const [city, typeStr] = item.split(':');
    const type = typeStr ? parseInt(typeStr, 10) : 1;
    
    // 去掉"省"、"市"、"自治区"、"特别行政区"等后缀
    let processedCity = city;
    if (city.endsWith('省')) {
      processedCity = city.slice(0, -1);
    } else if (city.endsWith('市')) {
      processedCity = city.slice(0, -1);
    } else if (city.endsWith('自治区')) {
      processedCity = city.slice(0, -3);
    } else if (city.endsWith('特别行政区')) {
      processedCity = city.slice(0, -5);
    }
    
    return {
      city: processedCity,
      type: isNaN(type) ? 1 : type
    };
  });
};

/**
 * 处理城市数据字符串，去除城市名称中的"省"、"市"等后缀
 * @param cityList 城市列表，格式为 ["城市名:类型", ...]
 * @returns 处理后的城市字符串数组，格式为 ["城市名:类型", ...]
 */
export const processCityStrings = (cityList: string[]): string[] => {
  if (!cityList || !Array.isArray(cityList) || cityList.length === 0) return [];
  
  return cityList.map((item: string) => {
    const [city, type] = item.split(':');
    
    // 去掉"省"、"市"、"自治区"、"特别行政区"等后缀
    let processedCity = city;
    if (city.endsWith('省')) {
      processedCity = city.slice(0, -1);
    } else if (city.endsWith('市')) {
      processedCity = city.slice(0, -1);
    } else if (city.endsWith('自治区')) {
      processedCity = city.slice(0, -3);
    } else if (city.endsWith('特别行政区')) {
      processedCity = city.slice(0, -5);
    }
    
    return `${processedCity}:${type}`;
  });
};
